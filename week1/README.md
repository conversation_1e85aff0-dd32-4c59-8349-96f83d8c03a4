# 第1周：Web开发概述与环境搭建

## 📚 课程内容

本周我们将学习Web开发的基础知识，包括：

1. **Web发展历史与基本原理**
2. **HTTP协议基础**
3. **开发环境配置**
4. **第一个HTML页面**

## 📁 文件结构

```
week1/
├── slides/                 # 课程幻灯片
│   ├── index.html         # 主幻灯片文件
│   ├── styles.css         # 幻灯片样式
│   └── script.js          # 幻灯片交互脚本
├── examples/              # 代码示例
│   └── first-webpage.html # 第一个网页示例
├── exercises/             # 练习文件
│   └── exercise1.html     # 个人介绍页面练习
├── setup-guide.md         # 环境配置指南
└── README.md             # 本文件
```

## 🎯 学习目标

完成本周学习后，你应该能够：

- [ ] 了解Web技术的发展历程
- [ ] 理解Web应用的基本工作原理
- [ ] 掌握HTTP协议的基础概念
- [ ] 配置完整的Web开发环境
- [ ] 创建基本的HTML页面
- [ ] 使用浏览器开发者工具

## 🚀 快速开始

### 1. 查看课程幻灯片
打开 `slides/index.html` 文件，可以通过以下方式：
- 双击文件在浏览器中打开
- 使用VS Code的Live Server插件预览

**幻灯片操作说明：**
- 使用方向键或点击按钮切换幻灯片
- 按空格键前进到下一张
- 按ESC键切换全屏模式
- 支持触摸滑动（移动设备）

### 2. 学习代码示例
查看 `examples/first-webpage.html`，这是一个完整的HTML页面示例，包含：
- 基本的HTML结构
- 内联CSS样式
- 简单的JavaScript交互
- 详细的代码注释

### 3. 完成练习
打开 `exercises/exercise1.html`，按照页面中的提示完成个人介绍页面的制作。

### 4. 配置开发环境
参考 `setup-guide.md` 文件，完成开发环境的配置。

## 💻 环境配置

### 必需软件
1. **Visual Studio Code** - 代码编辑器
2. **Google Chrome/Firefox** - 浏览器
3. **Live Server插件** - 实时预览

### 推荐插件
- HTML CSS Support
- JavaScript (ES6) code snippets
- Prettier - Code formatter
- Auto Rename Tag

详细配置步骤请参考 [环境配置指南](setup-guide.md)。

## 📖 课程重点

### Web发展历史
- 1989年：万维网诞生
- 1993年：第一个图形化浏览器
- 1995年：JavaScript诞生
- 2004年：AJAX技术兴起
- 2008年：HTML5标准制定
- 2010+：现代Web开发时代

### HTTP协议基础
- **请求方法**：GET、POST、PUT、DELETE
- **状态码**：200(成功)、404(未找到)、500(服务器错误)
- **请求/响应结构**：头部、主体

### HTML基础结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
</head>
<body>
    <!-- 页面内容 -->
</body>
</html>
```

## 🔧 开发者工具使用

### Chrome DevTools面板
- **Elements**：查看和编辑HTML/CSS
- **Console**：JavaScript控制台和错误信息
- **Sources**：调试JavaScript代码
- **Network**：监控网络请求
- **Application**：查看存储数据

### 常用快捷键
- `F12` - 打开/关闭开发者工具
- `Ctrl+Shift+I` - 打开开发者工具
- `Ctrl+Shift+C` - 选择元素模式

## 📝 课堂练习

### 练习1：环境配置
1. 安装VS Code和必要插件
2. 配置浏览器开发者工具
3. 创建第一个HTML文件
4. 使用Live Server预览页面

### 练习2：HTML基础
1. 创建包含标题、段落、列表的页面
2. 添加基本的内联样式
3. 使用开发者工具检查元素
4. 在控制台中执行JavaScript代码

### 练习3：个人介绍页面
完成 `exercises/exercise1.html` 中的个人介绍页面：
1. 填写个人基本信息
2. 添加技能和兴趣爱好
3. 自定义样式和颜色
4. 测试交互功能

## 📋 作业要求

### 必做作业
1. **完成开发环境配置**
   - 安装所有必需软件
   - 配置VS Code插件
   - 验证环境工作正常

2. **创建个人介绍页面**
   - 基于练习模板创建
   - 包含完整的个人信息
   - 至少使用5种不同的HTML标签
   - 添加基本的CSS样式

### 选做作业
1. **探索开发者工具**
   - 尝试修改网页样式
   - 在控制台执行JavaScript代码
   - 使用Network面板查看资源加载

2. **创意页面**
   - 设计一个主题页面（如兴趣爱好、学习计划等）
   - 尝试使用更多HTML标签
   - 添加简单的交互功能

## 🎓 评分标准

| 项目 | 权重 | 评分要点 |
|------|------|----------|
| 环境配置 | 30% | 软件安装完整性、插件配置正确性 |
| HTML结构 | 40% | 语法正确性、标签使用合理性、语义化程度 |
| 页面内容 | 20% | 信息完整性、内容原创性 |
| 代码规范 | 10% | 缩进格式、注释质量、命名规范 |

## 🔗 参考资源

### 官方文档
- [MDN Web Docs](https://developer.mozilla.org/zh-CN/) - Web技术权威文档
- [HTML标准](https://html.spec.whatwg.org/) - HTML官方规范
- [VS Code文档](https://code.visualstudio.com/docs) - VS Code使用指南

### 学习网站
- [W3Schools](https://www.w3schools.com/) - Web技术教程
- [菜鸟教程](https://www.runoob.com/) - 中文编程教程
- [FreeCodeCamp](https://www.freecodecamp.org/) - 免费编程课程

### 在线工具
- [CodePen](https://codepen.io/) - 在线代码编辑器
- [JSFiddle](https://jsfiddle.net/) - 前端代码测试
- [Can I Use](https://caniuse.com/) - 浏览器兼容性查询

## ❓ 常见问题

### Q: Live Server无法启动怎么办？
A: 确保已安装Live Server插件，重启VS Code，确保文件扩展名为.html

### Q: 网页中文显示乱码？
A: 确保HTML文件编码为UTF-8，添加`<meta charset="UTF-8">`标签

### Q: 开发者工具打不开？
A: 尝试右键选择"检查元素"，或使用菜单：更多工具 > 开发者工具

### Q: 如何在VS Code中快速生成HTML模板？
A: 输入`!`然后按Tab键，或输入`html:5`然后按Tab键

## 📞 获取帮助

如果在学习过程中遇到问题：

1. **查阅文档**：先查看相关官方文档和教程
2. **同学讨论**：与同学交流学习心得和问题
3. **在线搜索**：使用搜索引擎查找解决方案
4. **询问老师**：课堂上或课后向老师请教

## 🎉 下周预告

**第2周：HTML5基础**
- HTML5新特性介绍
- 常用标签详解
- 语义化标签使用
- 表单元素
- 实践：制作个人简历页面

---

**祝学习愉快！** 🚀
