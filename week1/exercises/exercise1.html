<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>练习1：个人介绍页面</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
        }
        
        h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #667eea;
            font-size: 1.2rem;
            margin-top: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .info-item h3 {
            color: #667eea;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            list-style: none;
            padding: 0;
        }
        
        .skills-list li {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .hobbies-list {
            list-style: none;
            padding: 0;
        }
        
        .hobbies-list li {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: relative;
            padding-left: 50px;
        }
        
        .hobbies-list li::before {
            content: "🎯";
            position: absolute;
            left: 15px;
            font-size: 1.5rem;
        }
        
        .contact-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .contact-info h3 {
            margin-top: 0;
            color: white;
        }
        
        .contact-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .contact-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .contact-links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .todo-section {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .todo-section h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .todo-list {
            text-align: left;
            color: #856404;
        }
        
        .todo-list li {
            margin-bottom: 8px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .profile-photo {
                width: 120px;
                height: 120px;
                font-size: 2rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .contact-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="profile-photo">
                <!-- 这里可以放置头像，现在用首字母代替 -->
                ?
            </div>
            <h1>你的姓名</h1>
            <div class="subtitle">计算机专业学生 | Web开发学习者</div>
        </div>
        
        <div class="section">
            <h2>📋 基本信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <h3>学校专业</h3>
                    <p>请填写你的学校和专业信息</p>
                </div>
                <div class="info-item">
                    <h3>年级班级</h3>
                    <p>请填写你的年级和班级</p>
                </div>
                <div class="info-item">
                    <h3>学号</h3>
                    <p>请填写你的学号</p>
                </div>
                <div class="info-item">
                    <h3>联系方式</h3>
                    <p>请填写你的邮箱或其他联系方式</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>💻 技能与特长</h2>
            <p>请列出你目前掌握的技能和特长：</p>
            <ul class="skills-list">
                <li>HTML基础</li>
                <li>请添加更多技能</li>
                <li>...</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🎨 兴趣爱好</h2>
            <ul class="hobbies-list">
                <li>
                    <strong>编程学习</strong><br>
                    对Web开发充满兴趣，希望能够创建出色的网站
                </li>
                <li>
                    <strong>请添加你的爱好</strong><br>
                    描述你的兴趣爱好
                </li>
                <li>
                    <strong>请添加更多爱好</strong><br>
                    继续添加你的其他兴趣
                </li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🎯 学习目标</h2>
            <p>通过这门Web程序设计课程，我希望能够：</p>
            <ol>
                <li>掌握HTML、CSS、JavaScript的基础知识</li>
                <li>学会使用现代Web开发框架</li>
                <li>能够独立开发完整的Web应用</li>
                <li>请添加你的其他学习目标...</li>
            </ol>
        </div>
        
        <div class="contact-info">
            <h3>📞 联系我</h3>
            <p>如果你想了解更多关于我的信息，欢迎通过以下方式联系：</p>
            <div class="contact-links">
                <a href="mailto:<EMAIL>">📧 邮箱</a>
                <a href="#" onclick="alert('请添加你的GitHub链接')">🐙 GitHub</a>
                <a href="#" onclick="alert('请添加你的其他联系方式')">💬 其他</a>
            </div>
        </div>
        
        <div class="todo-section">
            <h3>📝 练习任务</h3>
            <p><strong>请完成以下任务来个性化这个页面：</strong></p>
            <ol class="todo-list">
                <li>将"你的姓名"替换为你的真实姓名</li>
                <li>填写完整的基本信息</li>
                <li>添加你的真实技能和特长</li>
                <li>列出你的兴趣爱好</li>
                <li>设定你的学习目标</li>
                <li>更新联系方式</li>
                <li>尝试修改颜色和样式</li>
                <li>添加更多个人信息</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为头像添加点击事件
            const profilePhoto = document.querySelector('.profile-photo');
            profilePhoto.addEventListener('click', function() {
                const name = prompt('请输入你的姓名：');
                if (name) {
                    document.querySelector('h1').textContent = name;
                    profilePhoto.textContent = name.charAt(0).toUpperCase();
                }
            });
            
            // 为技能标签添加点击事件
            const skillsList = document.querySelector('.skills-list');
            skillsList.addEventListener('click', function(e) {
                if (e.target.tagName === 'LI') {
                    const newSkill = prompt('请输入新的技能：');
                    if (newSkill) {
                        const li = document.createElement('li');
                        li.textContent = newSkill;
                        skillsList.appendChild(li);
                    }
                }
            });
            
            // 在控制台输出欢迎信息
            console.log('🎉 欢迎来到你的个人介绍页面！');
            console.log('💡 提示：点击头像可以修改姓名，点击技能标签可以添加新技能');
            console.log('🔧 打开开发者工具，尝试在Console面板中输入JavaScript代码');
        });
        
        // 添加一些有趣的控制台命令
        window.changeTheme = function(color) {
            document.documentElement.style.setProperty('--main-color', color);
            console.log('主题颜色已更改为：' + color);
        };
        
        window.addSkill = function(skill) {
            const skillsList = document.querySelector('.skills-list');
            const li = document.createElement('li');
            li.textContent = skill;
            skillsList.appendChild(li);
            console.log('已添加技能：' + skill);
        };
        
        // 显示一些控制台提示
        setTimeout(() => {
            console.log('🎯 尝试在控制台中输入以下命令：');
            console.log('   changeTheme("#ff6b6b") - 更改主题颜色');
            console.log('   addSkill("新技能") - 添加新技能');
        }, 2000);
    </script>
</body>
</html>
