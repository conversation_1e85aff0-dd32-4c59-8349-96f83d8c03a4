<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP方法：幂等性与安全性</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fullscreen-svg {
            width: 100vw;
            height: 100vh;
            display: block;
        }

        /* 动画样式 */
        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease-in forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 1s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .zoom-in {
            transform: scale(0);
            opacity: 0;
            animation: zoomIn 0.8s ease-out forwards;
        }

        @keyframes zoomIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.05); opacity: 1; }
        }

        .glow-safe {
            filter: drop-shadow(0 0 15px rgba(46, 204, 113, 0.8));
        }

        .glow-idempotent {
            filter: drop-shadow(0 0 15px rgba(52, 152, 219, 0.8));
        }

        .glow-both {
            filter: drop-shadow(0 0 15px rgba(155, 89, 182, 0.8));
        }

        .interactive:hover {
            transform: scale(1.05);
            cursor: pointer;
        }

        .method-highlight {
            animation: methodPulse 1.5s ease-in-out;
        }

        @keyframes methodPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); filter: brightness(1.2); }
        }

        .concept-appear {
            opacity: 0;
            transform: translateX(-50px);
            animation: conceptSlide 1s ease-out forwards;
        }

        @keyframes conceptSlide {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <svg class="fullscreen-svg" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
        <!-- 背景渐变定义 -->
        <defs>
            <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#2c3e50"/>
                <stop offset="50%" style="stop-color:#34495e"/>
                <stop offset="100%" style="stop-color:#2c3e50"/>
            </linearGradient>
            
            <linearGradient id="safeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#2ecc71"/>
                <stop offset="100%" style="stop-color:#27ae60"/>
            </linearGradient>
            
            <linearGradient id="idempotentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3498db"/>
                <stop offset="100%" style="stop-color:#2980b9"/>
            </linearGradient>
            
            <linearGradient id="bothGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#9b59b6"/>
                <stop offset="100%" style="stop-color:#8e44ad"/>
            </linearGradient>
            
            <linearGradient id="neitherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#e74c3c"/>
                <stop offset="100%" style="stop-color:#c0392b"/>
            </linearGradient>
        </defs>
        
        <!-- 背景 -->
        <rect width="1920" height="1080" fill="url(#bgGradient)"/>
        
        <!-- 标题 -->
        <g class="fade-in" style="animation-delay: 0.5s">
            <text x="960" y="100" text-anchor="middle" font-size="64" font-weight="bold" fill="white">
                HTTP方法的幂等性与安全性
            </text>
            <text x="960" y="150" text-anchor="middle" font-size="32" fill="#ecf0f1">
                Idempotent & Safe HTTP Methods
            </text>
        </g>
        
        <!-- 概念定义区域 -->
        <g class="slide-up" style="animation-delay: 1s">
            <rect x="100" y="200" width="1720" height="180" rx="20" fill="rgba(52, 73, 94, 0.9)" stroke="#34495e" stroke-width="3"/>
            
            <!-- 安全性定义 -->
            <g class="concept-appear" style="animation-delay: 1.5s">
                <circle cx="300" cy="250" r="20" fill="#2ecc71"/>
                <text x="340" y="260" font-size="32" font-weight="bold" fill="#2ecc71">安全性 (Safe)</text>
                <text x="340" y="295" font-size="24" fill="white">不会修改服务器状态的方法</text>
                <text x="340" y="325" font-size="24" fill="white">只读操作，不产生副作用</text>
            </g>
            
            <!-- 幂等性定义 -->
            <g class="concept-appear" style="animation-delay: 2s">
                <circle cx="1000" cy="250" r="20" fill="#3498db"/>
                <text x="1040" y="260" font-size="32" font-weight="bold" fill="#3498db">幂等性 (Idempotent)</text>
                <text x="1040" y="295" font-size="24" fill="white">多次执行与单次执行效果相同</text>
                <text x="1040" y="325" font-size="24" fill="white">重复调用不会产生额外影响</text>
            </g>
        </g>
        
        <!-- HTTP方法分类展示 -->
        <g class="zoom-in" style="animation-delay: 2.5s">
            <!-- 既安全又幂等 -->
            <g transform="translate(200, 450)">
                <rect x="0" y="0" width="300" height="200" rx="15" fill="url(#bothGradient)" stroke="#8e44ad" stroke-width="4" class="interactive" data-category="both"/>
                <text x="150" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                    既安全又幂等
                </text>
                <text x="150" y="60" text-anchor="middle" font-size="20" fill="rgba(255,255,255,0.9)">
                    Safe + Idempotent
                </text>
                
                <!-- GET方法 -->
                <rect x="20" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="GET"/>
                <text x="60" y="105" text-anchor="middle" font-size="20" font-weight="bold" fill="white">GET</text>
                
                <!-- HEAD方法 -->
                <rect x="110" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="HEAD"/>
                <text x="150" y="105" text-anchor="middle" font-size="20" font-weight="bold" fill="white">HEAD</text>
                
                <!-- OPTIONS方法 -->
                <rect x="200" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="OPTIONS"/>
                <text x="240" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">OPTIONS</text>
                
                <text x="150" y="150" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    只读操作
                </text>
                <text x="150" y="175" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    可重复执行
                </text>
            </g>
            
            <!-- 仅幂等 -->
            <g transform="translate(600, 450)">
                <rect x="0" y="0" width="300" height="200" rx="15" fill="url(#idempotentGradient)" stroke="#2980b9" stroke-width="4" class="interactive" data-category="idempotent"/>
                <text x="150" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                    仅幂等
                </text>
                <text x="150" y="60" text-anchor="middle" font-size="20" fill="rgba(255,255,255,0.9)">
                    Idempotent Only
                </text>
                
                <!-- PUT方法 -->
                <rect x="60" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="PUT"/>
                <text x="100" y="105" text-anchor="middle" font-size="20" font-weight="bold" fill="white">PUT</text>
                
                <!-- DELETE方法 -->
                <rect x="160" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="DELETE"/>
                <text x="200" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="white">DELETE</text>
                
                <text x="150" y="150" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    会修改状态
                </text>
                <text x="150" y="175" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    但可重复执行
                </text>
            </g>
            
            <!-- 都不是 -->
            <g transform="translate(1000, 450)">
                <rect x="0" y="0" width="300" height="200" rx="15" fill="url(#neitherGradient)" stroke="#c0392b" stroke-width="4" class="interactive" data-category="neither"/>
                <text x="150" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                    都不是
                </text>
                <text x="150" y="60" text-anchor="middle" font-size="20" fill="rgba(255,255,255,0.9)">
                    Neither Safe nor Idempotent
                </text>
                
                <!-- POST方法 -->
                <rect x="110" y="80" width="80" height="40" rx="8" fill="rgba(255,255,255,0.2)" class="interactive method-card" data-method="POST"/>
                <text x="150" y="105" text-anchor="middle" font-size="20" font-weight="bold" fill="white">POST</text>
                
                <text x="150" y="150" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    会修改状态
                </text>
                <text x="150" y="175" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.8)">
                    重复执行有副作用
                </text>
            </g>
        </g>
        
        <!-- 详细说明区域 -->
        <g class="slide-up" style="animation-delay: 3s">
            <rect x="100" y="700" width="1720" height="320" rx="20" fill="rgba(44, 62, 80, 0.9)" stroke="#2c3e50" stroke-width="3"/>
            
            <text x="960" y="740" text-anchor="middle" font-size="36" font-weight="bold" fill="white">
                实际应用示例
            </text>
            
            <!-- GET示例 -->
            <g transform="translate(150, 780)">
                <rect x="0" y="0" width="350" height="120" rx="10" fill="rgba(155, 89, 182, 0.3)" stroke="#9b59b6" stroke-width="2"/>
                <text x="175" y="25" text-anchor="middle" font-size="24" font-weight="bold" fill="#9b59b6">GET /users/123</text>
                <text x="20" y="50" font-size="18" fill="white">✅ 安全：不修改用户数据</text>
                <text x="20" y="75" font-size="18" fill="white">✅ 幂等：多次查询结果一致</text>
                <text x="20" y="100" font-size="18" fill="white">💡 用途：获取用户信息</text>
            </g>
            
            <!-- PUT示例 -->
            <g transform="translate(550, 780)">
                <rect x="0" y="0" width="350" height="120" rx="10" fill="rgba(52, 152, 219, 0.3)" stroke="#3498db" stroke-width="2"/>
                <text x="175" y="25" text-anchor="middle" font-size="24" font-weight="bold" fill="#3498db">PUT /users/123</text>
                <text x="20" y="50" font-size="18" fill="white">❌ 不安全：会修改用户数据</text>
                <text x="20" y="75" font-size="18" fill="white">✅ 幂等：多次更新结果相同</text>
                <text x="20" y="100" font-size="18" fill="white">💡 用途：更新用户信息</text>
            </g>
            
            <!-- POST示例 -->
            <g transform="translate(950, 780)">
                <rect x="0" y="0" width="350" height="120" rx="10" fill="rgba(231, 76, 60, 0.3)" stroke="#e74c3c" stroke-width="2"/>
                <text x="175" y="25" text-anchor="middle" font-size="24" font-weight="bold" fill="#e74c3c">POST /users</text>
                <text x="20" y="50" font-size="18" fill="white">❌ 不安全：会创建新用户</text>
                <text x="20" y="75" font-size="18" fill="white">❌ 不幂等：多次调用创建多个</text>
                <text x="20" y="100" font-size="18" fill="white">💡 用途：创建新用户</text>
            </g>
            
            <!-- DELETE示例 -->
            <g transform="translate(1350, 780)">
                <rect x="0" y="0" width="350" height="120" rx="10" fill="rgba(52, 152, 219, 0.3)" stroke="#3498db" stroke-width="2"/>
                <text x="175" y="25" text-anchor="middle" font-size="24" font-weight="bold" fill="#3498db">DELETE /users/123</text>
                <text x="20" y="50" font-size="18" fill="white">❌ 不安全：会删除用户数据</text>
                <text x="20" y="75" font-size="18" fill="white">✅ 幂等：多次删除结果相同</text>
                <text x="20" y="100" font-size="18" fill="white">💡 用途：删除用户</text>
            </g>
        </g>
        
        <!-- 总结要点 -->
        <g class="fade-in" style="animation-delay: 4s">
            <rect x="400" y="1030" width="1120" height="40" rx="20" fill="rgba(52, 152, 219, 0.2)" stroke="#3498db" stroke-width="2"/>
            <text x="960" y="1055" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                💡 记忆要点：GET/HEAD/OPTIONS安全且幂等，PUT/DELETE仅幂等，POST都不是
            </text>
        </g>
        
        <!-- 交互提示 -->
        <g class="fade-in" style="animation-delay: 4.5s">
            <text x="960" y="30" text-anchor="middle" font-size="20" fill="#95a5a6">
                🖱️ 点击HTTP方法卡片查看详细说明 | 点击分类区域了解更多
            </text>
        </g>
    </svg>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // HTTP方法详细信息
            const methodDetails = {
                'GET': {
                    title: 'GET方法详解',
                    safe: true,
                    idempotent: true,
                    details: [
                        '• 用于获取资源信息',
                        '• 不应该修改服务器状态',
                        '• 可以被缓存',
                        '• 参数通过URL传递',
                        '• 浏览器可以重复发送'
                    ]
                },
                'HEAD': {
                    title: 'HEAD方法详解',
                    safe: true,
                    idempotent: true,
                    details: [
                        '• 类似GET但只返回响应头',
                        '• 不返回响应体',
                        '• 用于检查资源是否存在',
                        '• 获取资源元信息',
                        '• 常用于缓存验证'
                    ]
                },
                'OPTIONS': {
                    title: 'OPTIONS方法详解',
                    safe: true,
                    idempotent: true,
                    details: [
                        '• 用于获取服务器支持的方法',
                        '• CORS预检请求使用',
                        '• 返回Allow头部信息',
                        '• 不修改服务器状态',
                        '• 用于API能力发现'
                    ]
                },
                'PUT': {
                    title: 'PUT方法详解',
                    safe: false,
                    idempotent: true,
                    details: [
                        '• 用于创建或完全更新资源',
                        '• 多次调用结果相同',
                        '• 请求体包含完整资源',
                        '• 具有覆盖语义',
                        '• 常用于资源的完整替换'
                    ]
                },
                'DELETE': {
                    title: 'DELETE方法详解',
                    safe: false,
                    idempotent: true,
                    details: [
                        '• 用于删除指定资源',
                        '• 多次删除同一资源结果相同',
                        '• 第一次删除成功，后续返回404',
                        '• 但整体效果是幂等的',
                        '• 删除不存在的资源也是幂等的'
                    ]
                },
                'POST': {
                    title: 'POST方法详解',
                    safe: false,
                    idempotent: false,
                    details: [
                        '• 用于创建新资源或提交数据',
                        '• 每次调用可能产生不同结果',
                        '• 常用于表单提交',
                        '• 创建操作通常不幂等',
                        '• 需要防止重复提交'
                    ]
                }
            };
            
            // 分类详细信息
            const categoryDetails = {
                'both': {
                    title: '既安全又幂等的方法',
                    description: '这些方法是最"友好"的HTTP方法，可以安全地重复调用而不会产生任何副作用。',
                    characteristics: [
                        '• 不会修改服务器状态',
                        '• 可以被浏览器缓存',
                        '• 可以被代理服务器缓存',
                        '• 搜索引擎爬虫可以安全访问',
                        '• 网络中断后可以安全重试'
                    ]
                },
                'idempotent': {
                    title: '仅幂等的方法',
                    description: '这些方法会修改服务器状态，但多次执行的效果与单次执行相同。',
                    characteristics: [
                        '• 会修改服务器状态',
                        '• 多次调用结果一致',
                        '• 网络故障时可以安全重试',
                        '• 适合用于更新和删除操作',
                        '• 客户端可以实现自动重试机制'
                    ]
                },
                'neither': {
                    title: '既不安全也不幂等的方法',
                    description: '这些方法会修改服务器状态，且多次执行可能产生不同的结果。',
                    characteristics: [
                        '• 会修改服务器状态',
                        '• 多次调用可能产生不同结果',
                        '• 需要防止重复提交',
                        '• 常用于创建新资源',
                        '• 需要特别小心处理网络重试'
                    ]
                }
            };
            
            // 方法卡片点击事件
            const methodCards = document.querySelectorAll('.method-card');
            methodCards.forEach(card => {
                card.addEventListener('click', function() {
                    const method = this.getAttribute('data-method');
                    const details = methodDetails[method];
                    
                    if (details) {
                        showMethodDetails(details);
                        
                        // 高亮效果
                        this.classList.add('method-highlight');
                        setTimeout(() => {
                            this.classList.remove('method-highlight');
                        }, 1500);
                    }
                });
            });
            
            // 分类区域点击事件
            const categoryCards = document.querySelectorAll('[data-category]');
            categoryCards.forEach(card => {
                card.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    const details = categoryDetails[category];
                    
                    if (details) {
                        showCategoryDetails(details);
                        
                        // 高亮效果
                        if (category === 'both') {
                            this.classList.add('glow-both');
                        } else if (category === 'idempotent') {
                            this.classList.add('glow-idempotent');
                        } else if (category === 'neither') {
                            this.classList.add('glow-safe');
                        }
                        
                        setTimeout(() => {
                            this.classList.remove('glow-both', 'glow-idempotent', 'glow-safe');
                        }, 3000);
                    }
                });
            });
            
            // 显示方法详情
            function showMethodDetails(details) {
                const infoBox = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                infoBox.innerHTML = `
                    <rect x="560" y="250" width="800" height="300" rx="15" fill="rgba(44, 62, 80, 0.95)" stroke="#3498db" stroke-width="3"/>
                    <text x="960" y="290" text-anchor="middle" font-size="28" font-weight="bold" fill="white">${details.title}</text>
                    <text x="960" y="320" text-anchor="middle" font-size="20" fill="${details.safe ? '#2ecc71' : '#e74c3c'}">
                        ${details.safe ? '✅ 安全' : '❌ 不安全'} | ${details.idempotent ? '✅ 幂等' : '❌ 不幂等'}
                    </text>
                    ${details.details.map((detail, i) => 
                        `<text x="590" y="${360 + i * 30}" font-size="18" fill="#ecf0f1">${detail}</text>`
                    ).join('')}
                `;
                
                document.querySelector('svg').appendChild(infoBox);
                
                setTimeout(() => {
                    infoBox.remove();
                }, 5000);
            }
            
            // 显示分类详情
            function showCategoryDetails(details) {
                const infoBox = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                infoBox.innerHTML = `
                    <rect x="460" y="200" width="1000" height="350" rx="15" fill="rgba(44, 62, 80, 0.95)" stroke="#9b59b6" stroke-width="3"/>
                    <text x="960" y="240" text-anchor="middle" font-size="28" font-weight="bold" fill="white">${details.title}</text>
                    <text x="490" y="280" font-size="20" fill="#ecf0f1">${details.description}</text>
                    ${details.characteristics.map((char, i) => 
                        `<text x="490" y="${320 + i * 30}" font-size="18" fill="#ecf0f1">${char}</text>`
                    ).join('')}
                `;
                
                document.querySelector('svg').appendChild(infoBox);
                
                setTimeout(() => {
                    infoBox.remove();
                }, 6000);
            }
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (e.key === 'r' || e.key === 'R') {
                    location.reload();
                }
                
                // 数字键快速查看方法详情
                const methodKeys = {
                    '1': 'GET',
                    '2': 'HEAD', 
                    '3': 'OPTIONS',
                    '4': 'PUT',
                    '5': 'DELETE',
                    '6': 'POST'
                };
                
                if (methodKeys[e.key]) {
                    const details = methodDetails[methodKeys[e.key]];
                    if (details) {
                        showMethodDetails(details);
                    }
                }
            });
        });
    </script>
</body>
</html>
