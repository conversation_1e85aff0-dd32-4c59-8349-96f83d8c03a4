<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL结构详解 - SVG可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 1200px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .url-svg {
            width: 100%;
            height: auto;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
        }

        .explanation {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .part-explanation {
            background: rgba(102, 126, 234, 0.1);
            padding: 2rem;
            border-radius: 15px;
            border-left: 5px solid var(--color);
        }

        .part-explanation h3 {
            color: var(--color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .part-explanation .example {
            font-family: 'Courier New', monospace;
            background: var(--color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            font-weight: bold;
        }

        .part-explanation ul {
            list-style: none;
            padding-left: 0;
        }

        .part-explanation li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .part-explanation li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--color);
            font-weight: bold;
        }

        .protocol { --color: #e74c3c; }
        .host { --color: #f39c12; }
        .port { --color: #27ae60; }
        .path { --color: #3498db; }
        .query { --color: #9b59b6; }
        .hash { --color: #1abc9c; }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .demo-input {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-bottom: 1rem;
        }

        .demo-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .demo-result {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            min-height: 100px;
        }

        .url-part {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            margin: 0.2rem;
            border-radius: 5px;
            color: white;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .explanation {
                grid-template-columns: 1fr;
            }
        }

        /* SVG动画 */
        .url-part-svg {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .url-part-svg:hover {
            filter: brightness(1.2);
            transform: scale(1.05);
        }

        .connector-line {
            stroke-dasharray: 5,5;
            animation: dash 2s linear infinite;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: -10;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL结构详解</h1>
        <p style="text-align: center; color: #666; font-size: 1.1rem; margin-bottom: 2rem;">
            统一资源定位符 (Uniform Resource Locator) 的组成部分
        </p>

        <!-- SVG图解 -->
        <svg class="url-svg" viewBox="0 0 1000 400" xmlns="http://www.w3.org/2000/svg">
            <!-- 背景 -->
            <rect width="1000" height="400" fill="#f8f9fa"/>
            
            <!-- 标题 -->
            <text x="500" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                URL结构示例
            </text>
            
            <!-- URL示例背景 -->
            <rect x="50" y="70" width="900" height="60" rx="10" fill="white" stroke="#dee2e6" stroke-width="2"/>
            
            <!-- URL各部分 -->
            <!-- 协议 -->
            <rect x="70" y="80" width="80" height="40" rx="5" fill="#e74c3c" class="url-part-svg" data-part="protocol"/>
            <text x="110" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">https</text>
            
            <!-- 分隔符 :// -->
            <text x="160" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">://</text>
            
            <!-- 主机 -->
            <rect x="190" y="80" width="200" height="40" rx="5" fill="#f39c12" class="url-part-svg" data-part="host"/>
            <text x="290" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">www.example.com</text>
            
            <!-- 分隔符 : -->
            <text x="400" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">:</text>
            
            <!-- 端口 -->
            <rect x="410" y="80" width="60" height="40" rx="5" fill="#27ae60" class="url-part-svg" data-part="port"/>
            <text x="440" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">443</text>
            
            <!-- 路径 -->
            <rect x="480" y="80" width="160" height="40" rx="5" fill="#3498db" class="url-part-svg" data-part="path"/>
            <text x="560" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">/api/users/123</text>
            
            <!-- 分隔符 ? -->
            <text x="650" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">?</text>
            
            <!-- 查询参数 -->
            <rect x="660" y="80" width="140" height="40" rx="5" fill="#9b59b6" class="url-part-svg" data-part="query"/>
            <text x="730" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">page=1&size=10</text>
            
            <!-- 分隔符 # -->
            <text x="810" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">#</text>
            
            <!-- 哈希 -->
            <rect x="820" y="80" width="100" height="40" rx="5" fill="#1abc9c" class="url-part-svg" data-part="hash"/>
            <text x="870" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">section1</text>
            
            <!-- 连接线和说明 -->
            <!-- 协议说明 -->
            <line x1="110" y1="130" x2="110" y2="160" stroke="#e74c3c" stroke-width="2" class="connector-line"/>
            <rect x="50" y="170" width="120" height="50" rx="5" fill="#e74c3c" opacity="0.9"/>
            <text x="110" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">协议</text>
            <text x="110" y="205" text-anchor="middle" font-size="10" fill="white">Protocol</text>
            
            <!-- 主机说明 -->
            <line x1="290" y1="130" x2="290" y2="160" stroke="#f39c12" stroke-width="2" class="connector-line"/>
            <rect x="230" y="170" width="120" height="50" rx="5" fill="#f39c12" opacity="0.9"/>
            <text x="290" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">主机</text>
            <text x="290" y="205" text-anchor="middle" font-size="10" fill="white">Host</text>
            
            <!-- 端口说明 -->
            <line x1="440" y1="130" x2="440" y2="160" stroke="#27ae60" stroke-width="2" class="connector-line"/>
            <rect x="380" y="170" width="120" height="50" rx="5" fill="#27ae60" opacity="0.9"/>
            <text x="440" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">端口</text>
            <text x="440" y="205" text-anchor="middle" font-size="10" fill="white">Port</text>
            
            <!-- 路径说明 -->
            <line x1="560" y1="130" x2="560" y2="160" stroke="#3498db" stroke-width="2" class="connector-line"/>
            <rect x="500" y="170" width="120" height="50" rx="5" fill="#3498db" opacity="0.9"/>
            <text x="560" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">路径</text>
            <text x="560" y="205" text-anchor="middle" font-size="10" fill="white">Path</text>
            
            <!-- 查询说明 -->
            <line x1="730" y1="130" x2="730" y2="160" stroke="#9b59b6" stroke-width="2" class="connector-line"/>
            <rect x="670" y="170" width="120" height="50" rx="5" fill="#9b59b6" opacity="0.9"/>
            <text x="730" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">查询参数</text>
            <text x="730" y="205" text-anchor="middle" font-size="10" fill="white">Query</text>
            
            <!-- 哈希说明 -->
            <line x1="870" y1="130" x2="870" y2="160" stroke="#1abc9c" stroke-width="2" class="connector-line"/>
            <rect x="810" y="170" width="120" height="50" rx="5" fill="#1abc9c" opacity="0.9"/>
            <text x="870" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">哈希</text>
            <text x="870" y="205" text-anchor="middle" font-size="10" fill="white">Hash</text>
            
            <!-- 通用格式 -->
            <text x="500" y="280" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                通用格式：协议://主机:端口/路径?查询参数#哈希
            </text>
            
            <!-- 必需和可选标识 -->
            <text x="500" y="310" text-anchor="middle" font-size="14" fill="#666">
                必需部分：协议、主机　　可选部分：端口、路径、查询参数、哈希
            </text>
            
            <!-- 装饰性元素 -->
            <circle cx="100" cy="350" r="3" fill="#667eea" opacity="0.6"/>
            <circle cx="200" cy="360" r="2" fill="#764ba2" opacity="0.6"/>
            <circle cx="300" cy="340" r="4" fill="#667eea" opacity="0.4"/>
            <circle cx="700" cy="350" r="3" fill="#764ba2" opacity="0.6"/>
            <circle cx="800" cy="360" r="2" fill="#667eea" opacity="0.6"/>
            <circle cx="900" cy="340" r="4" fill="#764ba2" opacity="0.4"/>
        </svg>

        <!-- 交互式演示 -->
        <div class="interactive-demo">
            <h3 style="color: #2c3e50; margin-bottom: 1rem;">🔧 交互式URL解析器</h3>
            <p style="color: #666; margin-bottom: 1rem;">在下面输入任意URL，系统会自动解析各个组成部分：</p>
            <input type="text" class="demo-input" id="urlInput" 
                   placeholder="请输入URL，例如：https://www.example.com:8080/api/users?page=1&size=10#results"
                   value="https://www.example.com:8080/api/users?page=1&size=10#results">
            <div class="demo-result" id="urlResult">
                <!-- 解析结果将在这里显示 -->
            </div>
        </div>

        <!-- 详细说明 -->
        <div class="explanation">
            <div class="part-explanation protocol">
                <h3>🌐 协议 (Protocol)</h3>
                <div class="example">https</div>
                <p><strong>定义：</strong>指定访问资源所使用的协议</p>
                <ul>
                    <li><strong>http://</strong> - 超文本传输协议（不安全）</li>
                    <li><strong>https://</strong> - 安全的HTTP协议（推荐）</li>
                    <li><strong>ftp://</strong> - 文件传输协议</li>
                    <li><strong>file://</strong> - 本地文件协议</li>
                    <li><strong>mailto:</strong> - 电子邮件协议</li>
                </ul>
            </div>

            <div class="part-explanation host">
                <h3>🏠 主机 (Host)</h3>
                <div class="example">www.example.com</div>
                <p><strong>定义：</strong>指定服务器的地址</p>
                <ul>
                    <li><strong>域名</strong> - www.google.com</li>
                    <li><strong>IP地址</strong> - ***********</li>
                    <li><strong>localhost</strong> - 本地主机</li>
                    <li><strong>子域名</strong> - api.example.com</li>
                    <li><strong>国际化域名</strong> - 支持中文域名</li>
                </ul>
            </div>

            <div class="part-explanation port">
                <h3>🚪 端口 (Port)</h3>
                <div class="example">:443</div>
                <p><strong>定义：</strong>指定服务器上的服务端口</p>
                <ul>
                    <li><strong>80</strong> - HTTP默认端口</li>
                    <li><strong>443</strong> - HTTPS默认端口</li>
                    <li><strong>3000</strong> - 开发服务器常用</li>
                    <li><strong>8080</strong> - 备用HTTP端口</li>
                    <li><strong>可省略</strong> - 使用默认端口时</li>
                </ul>
            </div>

            <div class="part-explanation path">
                <h3>📁 路径 (Path)</h3>
                <div class="example">/api/users/123</div>
                <p><strong>定义：</strong>指定服务器上资源的位置</p>
                <ul>
                    <li><strong>/</strong> - 根路径</li>
                    <li><strong>/index.html</strong> - 具体文件</li>
                    <li><strong>/api/users</strong> - API端点</li>
                    <li><strong>/products/123</strong> - 带参数的路径</li>
                    <li><strong>大小写敏感</strong> - 注意路径大小写</li>
                </ul>
            </div>

            <div class="part-explanation query">
                <h3>❓ 查询参数 (Query)</h3>
                <div class="example">?page=1&size=10</div>
                <p><strong>定义：</strong>向服务器传递的参数</p>
                <ul>
                    <li><strong>?</strong> - 查询参数开始标志</li>
                    <li><strong>&</strong> - 多个参数的分隔符</li>
                    <li><strong>key=value</strong> - 参数格式</li>
                    <li><strong>URL编码</strong> - 特殊字符需要编码</li>
                    <li><strong>GET请求</strong> - 主要数据传递方式</li>
                </ul>
            </div>

            <div class="part-explanation hash">
                <h3>🔗 哈希 (Hash)</h3>
                <div class="example">#section1</div>
                <p><strong>定义：</strong>页面内的锚点或客户端路由</p>
                <ul>
                    <li><strong>页面锚点</strong> - 跳转到页面特定位置</li>
                    <li><strong>不发送服务器</strong> - 仅客户端处理</li>
                    <li><strong>SPA路由</strong> - 单页应用路由</li>
                    <li><strong>书签功能</strong> - 保存页面状态</li>
                    <li><strong>SEO影响</strong> - 搜索引擎处理方式</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // URL解析功能
        function parseURL(url) {
            try {
                const urlObj = new URL(url);
                return {
                    protocol: urlObj.protocol.replace(':', ''),
                    host: urlObj.hostname,
                    port: urlObj.port || (urlObj.protocol === 'https:' ? '443' : '80'),
                    path: urlObj.pathname,
                    query: urlObj.search.replace('?', ''),
                    hash: urlObj.hash.replace('#', '')
                };
            } catch (e) {
                return null;
            }
        }

        function displayURLParts(parts) {
            if (!parts) {
                return '<p style="color: #e74c3c;">❌ 无效的URL格式</p>';
            }

            let html = '<div style="margin-bottom: 1rem;"><strong>URL解析结果：</strong></div>';
            
            if (parts.protocol) {
                html += `<span class="url-part" style="background-color: #e74c3c;">协议: ${parts.protocol}</span>`;
            }
            if (parts.host) {
                html += `<span class="url-part" style="background-color: #f39c12;">主机: ${parts.host}</span>`;
            }
            if (parts.port) {
                html += `<span class="url-part" style="background-color: #27ae60;">端口: ${parts.port}</span>`;
            }
            if (parts.path && parts.path !== '/') {
                html += `<span class="url-part" style="background-color: #3498db;">路径: ${parts.path}</span>`;
            }
            if (parts.query) {
                html += `<span class="url-part" style="background-color: #9b59b6;">查询: ${parts.query}</span>`;
            }
            if (parts.hash) {
                html += `<span class="url-part" style="background-color: #1abc9c;">哈希: ${parts.hash}</span>`;
            }

            return html;
        }

        // 输入框事件监听
        const urlInput = document.getElementById('urlInput');
        const urlResult = document.getElementById('urlResult');

        function updateResult() {
            const url = urlInput.value.trim();
            if (url) {
                const parts = parseURL(url);
                urlResult.innerHTML = displayURLParts(parts);
                urlResult.classList.add('fade-in');
                setTimeout(() => urlResult.classList.remove('fade-in'), 500);
            } else {
                urlResult.innerHTML = '<p style="color: #666;">请输入URL进行解析</p>';
            }
        }

        urlInput.addEventListener('input', updateResult);
        
        // 初始化显示
        updateResult();

        // SVG交互
        document.querySelectorAll('.url-part-svg').forEach(part => {
            part.addEventListener('mouseenter', function() {
                this.style.filter = 'brightness(1.2) drop-shadow(0 0 10px rgba(0,0,0,0.3))';
            });
            
            part.addEventListener('mouseleave', function() {
                this.style.filter = '';
            });
            
            part.addEventListener('click', function() {
                const partType = this.getAttribute('data-part');
                const explanation = document.querySelector(`.part-explanation.${partType}`);
                if (explanation) {
                    explanation.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    explanation.style.transform = 'scale(1.02)';
                    explanation.style.boxShadow = '0 10px 30px rgba(102, 126, 234, 0.3)';
                    setTimeout(() => {
                        explanation.style.transform = '';
                        explanation.style.boxShadow = '';
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
