<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDFS文件系统基本架构 - SVG动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
        }

        .fullscreen-svg {
            width: 100vw;
            height: 100vh;
            display: block;
        }

        /* SVG动画样式 */
        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease-in forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 1s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .zoom-in {
            transform: scale(0);
            opacity: 0;
            animation: zoomIn 0.8s ease-out forwards;
        }

        @keyframes zoomIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .data-flow {
            stroke-dasharray: 20;
            stroke-dashoffset: 20;
            animation: dataMove 3s linear infinite;
        }

        @keyframes dataMove {
            to { stroke-dashoffset: 0; }
        }

        .rotate {
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .bounce {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .glow {
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.6));
        }

        .interactive:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 15px rgba(255,255,255,0.8));
            cursor: pointer;
        }

        .heartbeat {
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .wave {
            animation: wave 4s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(10px); }
            75% { transform: translateX(-10px); }
        }
    </style>
</head>
<body>
    <svg class="fullscreen-svg" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
        <!-- 背景渐变定义 -->
        <defs>
            <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#1e3c72"/>
                <stop offset="50%" style="stop-color:#2a5298"/>
                <stop offset="100%" style="stop-color:#1e3c72"/>
            </linearGradient>
            
            <linearGradient id="namenodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ff6b6b"/>
                <stop offset="100%" style="stop-color:#ee5a52"/>
            </linearGradient>
            
            <linearGradient id="datanodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#4ecdc4"/>
                <stop offset="100%" style="stop-color:#45b7d1"/>
            </linearGradient>
            
            <linearGradient id="dataBlockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#f9ca24"/>
                <stop offset="100%" style="stop-color:#f0932b"/>
            </linearGradient>
            
            <!-- NameNode图标 -->
            <g id="namenode-icon">
                <rect x="0" y="0" width="120" height="100" rx="15" fill="url(#namenodeGradient)" stroke="#fff" stroke-width="3"/>
                <circle cx="60" cy="30" r="15" fill="#fff"/>
                <rect x="20" y="55" width="80" height="8" rx="4" fill="#fff"/>
                <rect x="30" y="70" width="60" height="6" rx="3" fill="#fff"/>
                <rect x="25" y="82" width="70" height="6" rx="3" fill="#fff"/>
            </g>
            
            <!-- DataNode图标 -->
            <g id="datanode-icon">
                <rect x="0" y="0" width="80" height="80" rx="10" fill="url(#datanodeGradient)" stroke="#fff" stroke-width="2"/>
                <rect x="10" y="15" width="60" height="8" rx="2" fill="#fff"/>
                <rect x="10" y="30" width="60" height="8" rx="2" fill="#fff"/>
                <rect x="10" y="45" width="60" height="8" rx="2" fill="#fff"/>
                <circle cx="20" cy="65" r="4" fill="#f9ca24"/>
                <circle cx="35" cy="65" r="4" fill="#2ecc71"/>
                <circle cx="50" cy="65" r="4" fill="#e74c3c"/>
                <circle cx="65" cy="65" r="4" fill="#9b59b6"/>
            </g>
            
            <!-- 数据块图标 -->
            <g id="data-block">
                <rect x="0" y="0" width="30" height="20" rx="3" fill="url(#dataBlockGradient)" stroke="#fff" stroke-width="1"/>
                <rect x="3" y="3" width="24" height="3" rx="1" fill="#fff"/>
                <rect x="3" y="8" width="18" height="3" rx="1" fill="#fff"/>
                <rect x="3" y="13" width="20" height="3" rx="1" fill="#fff"/>
            </g>
            
            <!-- 客户端图标 -->
            <g id="client-icon">
                <rect x="0" y="0" width="60" height="50" rx="8" fill="#6c5ce7" stroke="#fff" stroke-width="2"/>
                <rect x="5" y="5" width="50" height="30" rx="3" fill="#fff"/>
                <rect x="8" y="8" width="44" height="5" rx="2" fill="#6c5ce7"/>
                <circle cx="12" cy="10.5" r="1.5" fill="#fff"/>
                <circle cx="17" cy="10.5" r="1.5" fill="#fff"/>
                <circle cx="22" cy="10.5" r="1.5" fill="#fff"/>
                <rect x="10" y="18" width="30" height="2" fill="#bdc3c7"/>
                <rect x="10" y="23" width="25" height="2" fill="#bdc3c7"/>
                <rect x="10" y="28" width="35" height="2" fill="#bdc3c7"/>
                <rect x="20" y="40" width="20" height="8" rx="2" fill="#95a5a6"/>
            </g>
            
            <!-- 箭头标记 -->
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#fff"/>
            </marker>
            
            <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ff6b6b"/>
            </marker>
            
            <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#4ecdc4"/>
            </marker>
        </defs>
        
        <!-- 背景 -->
        <rect width="1920" height="1080" fill="url(#bgGradient)"/>
        
        <!-- 标题 -->
        <g class="fade-in" style="animation-delay: 0.5s">
            <text x="960" y="100" text-anchor="middle" font-size="64" font-weight="bold" fill="#fff">
                HDFS文件系统基本架构
            </text>
            <text x="960" y="140" text-anchor="middle" font-size="28" fill="#ecf0f1">
                Hadoop Distributed File System Architecture
            </text>
        </g>
        
        <!-- 客户端 -->
        <g class="slide-up interactive" style="animation-delay: 1s" transform="translate(100, 300)">
            <use href="#client-icon" class="glow"/>
            <text x="30" y="70" text-anchor="middle" font-size="20" font-weight="bold" fill="#fff">
                客户端
            </text>
            <text x="30" y="90" text-anchor="middle" font-size="16" fill="#ecf0f1">
                Client
            </text>
        </g>
        
        <!-- NameNode -->
        <g class="zoom-in interactive" style="animation-delay: 1.2s" transform="translate(400, 250)">
            <use href="#namenode-icon" class="pulse glow"/>
            <text x="60" y="130" text-anchor="middle" font-size="24" font-weight="bold" fill="#ff6b6b">
                NameNode
            </text>
            <text x="60" y="155" text-anchor="middle" font-size="18" fill="#fff">
                主节点
            </text>
            <text x="60" y="175" text-anchor="middle" font-size="16" fill="#ecf0f1">
                元数据管理
            </text>
        </g>
        
        <!-- Secondary NameNode -->
        <g class="zoom-in interactive" style="animation-delay: 1.4s" transform="translate(400, 450)">
            <rect x="0" y="0" width="120" height="80" rx="15" fill="rgba(255, 107, 107, 0.6)" stroke="#ff6b6b" stroke-width="2"/>
            <circle cx="60" cy="25" r="12" fill="#ff6b6b"/>
            <rect x="20" y="45" width="80" height="6" rx="3" fill="#ff6b6b"/>
            <rect x="30" y="58" width="60" height="5" rx="2" fill="#ff6b6b"/>
            <text x="60" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#ff6b6b">
                Secondary NameNode
            </text>
            <text x="60" y="125" text-anchor="middle" font-size="14" fill="#ecf0f1">
                辅助节点
            </text>
        </g>
        
        <!-- DataNode集群 -->
        <g class="slide-up" style="animation-delay: 1.6s">
            <!-- DataNode 1 -->
            <g class="interactive bounce" style="animation-delay: 2s" transform="translate(800, 200)">
                <use href="#datanode-icon" class="glow"/>
                <text x="40" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#4ecdc4">
                    DataNode 1
                </text>
                <text x="40" y="125" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    数据存储
                </text>
                
                <!-- 数据块 -->
                <g class="wave" style="animation-delay: 3s">
                    <use href="#data-block" x="10" y="140"/>
                    <use href="#data-block" x="45" y="140"/>
                    <use href="#data-block" x="10" y="165"/>
                </g>
            </g>
            
            <!-- DataNode 2 -->
            <g class="interactive bounce" style="animation-delay: 2.2s" transform="translate(950, 300)">
                <use href="#datanode-icon" class="glow"/>
                <text x="40" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#4ecdc4">
                    DataNode 2
                </text>
                <text x="40" y="125" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    副本存储
                </text>
                
                <!-- 数据块 -->
                <g class="wave" style="animation-delay: 3.2s">
                    <use href="#data-block" x="10" y="140"/>
                    <use href="#data-block" x="45" y="140"/>
                    <use href="#data-block" x="10" y="165"/>
                </g>
            </g>
            
            <!-- DataNode 3 -->
            <g class="interactive bounce" style="animation-delay: 2.4s" transform="translate(1100, 200)">
                <use href="#datanode-icon" class="glow"/>
                <text x="40" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#4ecdc4">
                    DataNode 3
                </text>
                <text x="40" y="125" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    容错备份
                </text>
                
                <!-- 数据块 -->
                <g class="wave" style="animation-delay: 3.4s">
                    <use href="#data-block" x="10" y="140"/>
                    <use href="#data-block" x="45" y="140"/>
                    <use href="#data-block" x="10" y="165"/>
                </g>
            </g>
            
            <!-- DataNode 4 -->
            <g class="interactive bounce" style="animation-delay: 2.6s" transform="translate(1250, 300)">
                <use href="#datanode-icon" class="glow"/>
                <text x="40" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#4ecdc4">
                    DataNode 4
                </text>
                <text x="40" y="125" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    扩展节点
                </text>
                
                <!-- 数据块 -->
                <g class="wave" style="animation-delay: 3.6s">
                    <use href="#data-block" x="10" y="140"/>
                    <use href="#data-block" x="45" y="140"/>
                    <use href="#data-block" x="10" y="165"/>
                </g>
            </g>
        </g>
        
        <!-- 连接线和数据流 -->
        <g class="fade-in" style="animation-delay: 2.8s">
            <!-- 客户端到NameNode -->
            <path d="M 190 325 Q 300 280 400 300" stroke="#fff" stroke-width="4" fill="none" 
                  marker-end="url(#arrowhead)" class="data-flow"/>
            <text x="295" y="270" text-anchor="middle" font-size="16" fill="#fff">
                元数据请求
            </text>
            
            <!-- NameNode到DataNode的管理连接 -->
            <path d="M 520 300 L 800 250" stroke="#ff6b6b" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead-red)" class="data-flow" style="animation-delay: 0.5s"/>
            <path d="M 520 320 L 950 350" stroke="#ff6b6b" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead-red)" class="data-flow" style="animation-delay: 1s"/>
            <path d="M 520 300 L 1100 250" stroke="#ff6b6b" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead-red)" class="data-flow" style="animation-delay: 1.5s"/>
            <path d="M 520 320 L 1250 350" stroke="#ff6b6b" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead-red)" class="data-flow" style="animation-delay: 2s"/>
            
            <!-- DataNode之间的副本复制 -->
            <path d="M 880 280 Q 950 260 950 300" stroke="#4ecdc4" stroke-width="2" fill="none" 
                  marker-end="url(#arrowhead-blue)" class="data-flow" style="animation-delay: 2.5s"/>
            <path d="M 1030 350 Q 1100 330 1100 280" stroke="#4ecdc4" stroke-width="2" fill="none" 
                  marker-end="url(#arrowhead-blue)" class="data-flow" style="animation-delay: 3s"/>
            <path d="M 1180 280 Q 1250 300 1250 300" stroke="#4ecdc4" stroke-width="2" fill="none" 
                  marker-end="url(#arrowhead-blue)" class="data-flow" style="animation-delay: 3.5s"/>
        </g>
        
        <!-- 心跳信号 -->
        <g class="fade-in" style="animation-delay: 4s">
            <circle cx="840" cy="240" r="8" fill="#2ecc71" class="heartbeat"/>
            <circle cx="990" cy="340" r="8" fill="#2ecc71" class="heartbeat" style="animation-delay: 0.3s"/>
            <circle cx="1140" cy="240" r="8" fill="#2ecc71" class="heartbeat" style="animation-delay: 0.6s"/>
            <circle cx="1290" cy="340" r="8" fill="#2ecc71" class="heartbeat" style="animation-delay: 0.9s"/>
            <text x="960" y="180" text-anchor="middle" font-size="18" fill="#2ecc71">
                💓 心跳监控
            </text>
        </g>
        
        <!-- 架构特点说明 -->
        <g class="slide-up" style="animation-delay: 3s">
            <rect x="1400" y="200" width="450" height="400" rx="20" fill="rgba(255,255,255,0.1)" 
                  stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
            
            <text x="1625" y="240" text-anchor="middle" font-size="24" font-weight="bold" fill="#f9ca24">
                架构特点
            </text>
            
            <g transform="translate(1420, 270)">
                <circle cx="0" cy="0" r="8" fill="#ff6b6b"/>
                <text x="20" y="8" font-size="18" fill="#fff" font-weight="bold">主从架构</text>
                <text x="20" y="28" font-size="14" fill="#ecf0f1">NameNode管理元数据</text>
                <text x="20" y="45" font-size="14" fill="#ecf0f1">DataNode存储数据</text>
            </g>
            
            <g transform="translate(1420, 340)">
                <circle cx="0" cy="0" r="8" fill="#4ecdc4"/>
                <text x="20" y="8" font-size="18" fill="#fff" font-weight="bold">分布式存储</text>
                <text x="20" y="28" font-size="14" fill="#ecf0f1">数据分块存储</text>
                <text x="20" y="45" font-size="14" fill="#ecf0f1">多副本容错</text>
            </g>
            
            <g transform="translate(1420, 410)">
                <circle cx="0" cy="0" r="8" fill="#2ecc71"/>
                <text x="20" y="8" font-size="18" fill="#fff" font-weight="bold">高可靠性</text>
                <text x="20" y="28" font-size="14" fill="#ecf0f1">自动故障恢复</text>
                <text x="20" y="45" font-size="14" fill="#ecf0f1">数据完整性检查</text>
            </g>
            
            <g transform="translate(1420, 480)">
                <circle cx="0" cy="0" r="8" fill="#f9ca24"/>
                <text x="20" y="8" font-size="18" fill="#fff" font-weight="bold">可扩展性</text>
                <text x="20" y="28" font-size="14" fill="#ecf0f1">水平扩展</text>
                <text x="20" y="45" font-size="14" fill="#ecf0f1">PB级存储</text>
            </g>
        </g>
        
        <!-- 数据流程说明 -->
        <g class="fade-in" style="animation-delay: 4s">
            <rect x="100" y="600" width="1720" height="200" rx="20" fill="rgba(255,255,255,0.1)" 
                  stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
            
            <text x="960" y="640" text-anchor="middle" font-size="28" font-weight="bold" fill="#fff">
                HDFS工作流程
            </text>
            
            <g transform="translate(150, 670)">
                <rect x="0" y="0" width="300" height="80" rx="10" fill="rgba(108, 92, 231, 0.3)" stroke="#6c5ce7" stroke-width="2"/>
                <text x="150" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#6c5ce7">
                    1. 客户端请求
                </text>
                <text x="150" y="45" text-anchor="middle" font-size="14" fill="#fff">
                    向NameNode请求文件操作
                </text>
                <text x="150" y="65" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    获取DataNode位置信息
                </text>
            </g>
            
            <g transform="translate(500, 670)">
                <rect x="0" y="0" width="300" height="80" rx="10" fill="rgba(255, 107, 107, 0.3)" stroke="#ff6b6b" stroke-width="2"/>
                <text x="150" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#ff6b6b">
                    2. 元数据管理
                </text>
                <text x="150" y="45" text-anchor="middle" font-size="14" fill="#fff">
                    NameNode管理文件系统树
                </text>
                <text x="150" y="65" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    维护块到节点的映射
                </text>
            </g>
            
            <g transform="translate(850, 670)">
                <rect x="0" y="0" width="300" height="80" rx="10" fill="rgba(78, 205, 196, 0.3)" stroke="#4ecdc4" stroke-width="2"/>
                <text x="150" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#4ecdc4">
                    3. 数据存储
                </text>
                <text x="150" y="45" text-anchor="middle" font-size="14" fill="#fff">
                    DataNode存储数据块
                </text>
                <text x="150" y="65" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    创建多个副本保证可靠性
                </text>
            </g>
            
            <g transform="translate(1200, 670)">
                <rect x="0" y="0" width="300" height="80" rx="10" fill="rgba(46, 204, 113, 0.3)" stroke="#2ecc71" stroke-width="2"/>
                <text x="150" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2ecc71">
                    4. 监控维护
                </text>
                <text x="150" y="45" text-anchor="middle" font-size="14" fill="#fff">
                    心跳监控节点状态
                </text>
                <text x="150" y="65" text-anchor="middle" font-size="14" fill="#ecf0f1">
                    自动故障恢复和负载均衡
                </text>
            </g>
        </g>
        
        <!-- 副本复制示意 -->
        <g class="fade-in" style="animation-delay: 4.5s">
            <text x="960" y="550" text-anchor="middle" font-size="20" font-weight="bold" fill="#f9ca24">
                数据副本策略
            </text>

            <!-- 副本1 -->
            <g transform="translate(700, 570)">
                <use href="#data-block" class="glow"/>
                <text x="15" y="35" text-anchor="middle" font-size="12" fill="#fff">副本1</text>
            </g>

            <!-- 副本2 -->
            <g transform="translate(850, 570)">
                <use href="#data-block" class="glow"/>
                <text x="15" y="35" text-anchor="middle" font-size="12" fill="#fff">副本2</text>
            </g>

            <!-- 副本3 -->
            <g transform="translate(1000, 570)">
                <use href="#data-block" class="glow"/>
                <text x="15" y="35" text-anchor="middle" font-size="12" fill="#fff">副本3</text>
            </g>

            <!-- 副本连接线 -->
            <path d="M 730 585 L 850 585" stroke="#f9ca24" stroke-width="2" fill="none"
                  stroke-dasharray="5,5" class="data-flow" style="animation-delay: 1s"/>
            <path d="M 880 585 L 1000 585" stroke="#f9ca24" stroke-width="2" fill="none"
                  stroke-dasharray="5,5" class="data-flow" style="animation-delay: 1.5s"/>

            <text x="960" y="620" text-anchor="middle" font-size="16" fill="#ecf0f1">
                默认3副本策略确保数据安全
            </text>
        </g>

        <!-- 底部总结 -->
        <g class="fade-in" style="animation-delay: 5s">
            <text x="960" y="950" text-anchor="middle" font-size="28" font-weight="bold" fill="#f9ca24">
                🏗️ HDFS = 主从架构 + 分布式存储 + 多副本容错 + 水平扩展
            </text>
            <text x="960" y="980" text-anchor="middle" font-size="20" fill="#ecf0f1">
                为大数据处理提供高可靠、高吞吐量的分布式文件系统
            </text>
        </g>

        <!-- 交互提示 -->
        <g class="fade-in" style="animation-delay: 6s">
            <text x="960" y="30" text-anchor="middle" font-size="20" fill="#95a5a6">
                💡 观察数据流动动画和心跳监控效果 | 点击组件查看详细信息
            </text>
        </g>
    </svg>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 为交互元素添加点击事件
            const interactiveElements = document.querySelectorAll('.interactive');
            
            interactiveElements.forEach((element, index) => {
                element.addEventListener('click', function() {
                    // 创建信息提示
                    const messages = [
                        '客户端：发起文件读写请求，通过NameNode获取DataNode位置信息',
                        'NameNode：管理文件系统命名空间，维护文件到数据块的映射关系',
                        'Secondary NameNode：辅助NameNode进行检查点操作，备份元数据',
                        'DataNode 1：存储数据块，定期向NameNode发送心跳和块报告',
                        'DataNode 2：存储数据副本，参与负载均衡和故障恢复',
                        'DataNode 3：提供容错备份，确保数据的高可用性',
                        'DataNode 4：支持集群扩展，增加存储容量和处理能力'
                    ];
                    
                    if (messages[index]) {
                        // 创建临时提示框
                        const tooltip = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                        tooltip.innerHTML = `
                            <rect x="200" y="850" width="1520" height="80" rx="15" fill="rgba(44, 62, 80, 0.95)" stroke="#3498db" stroke-width="2"/>
                            <text x="960" y="885" text-anchor="middle" font-size="20" font-weight="bold" fill="#3498db">组件详情</text>
                            <text x="960" y="910" text-anchor="middle" font-size="16" fill="white">${messages[index]}</text>
                        `;
                        
                        document.querySelector('svg').appendChild(tooltip);
                        
                        // 3秒后移除提示框
                        setTimeout(() => {
                            tooltip.remove();
                        }, 3000);
                    }
                    
                    // 高亮效果
                    this.style.filter = 'drop-shadow(0 0 20px rgba(52, 152, 219, 0.8))';
                    setTimeout(() => {
                        this.style.filter = '';
                    }, 2000);
                });
            });
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (e.key === 'r' || e.key === 'R') {
                    // R键重新播放动画
                    location.reload();
                }
                
                if (e.key === ' ') {
                    // 空格键暂停/恢复动画
                    e.preventDefault();
                    const animatedElements = document.querySelectorAll('[style*="animation"]');
                    animatedElements.forEach(el => {
                        const style = window.getComputedStyle(el);
                        if (style.animationPlayState === 'paused') {
                            el.style.animationPlayState = 'running';
                        } else {
                            el.style.animationPlayState = 'paused';
                        }
                    });
                }
            });
            
            // 添加动态数据流效果
            function createDataPacket() {
                const packet = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                packet.setAttribute('r', '4');
                packet.setAttribute('fill', '#f9ca24');
                packet.setAttribute('opacity', '0');
                
                // 随机选择路径
                const paths = [
                    {start: {x: 190, y: 325}, end: {x: 400, y: 300}},
                    {start: {x: 520, y: 300}, end: {x: 800, y: 250}},
                    {start: {x: 520, y: 320}, end: {x: 950, y: 350}},
                    {start: {x: 880, y: 280}, end: {x: 950, y: 300}}
                ];
                
                const path = paths[Math.floor(Math.random() * paths.length)];
                packet.setAttribute('cx', path.start.x);
                packet.setAttribute('cy', path.start.y);
                
                document.querySelector('svg').appendChild(packet);
                
                // 动画移动
                packet.style.animation = 'fadeIn 0.3s ease-in forwards';
                
                setTimeout(() => {
                    const deltaX = path.end.x - path.start.x;
                    const deltaY = path.end.y - path.start.y;
                    packet.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
                    packet.style.transition = 'transform 2s ease-in-out';
                    
                    setTimeout(() => {
                        packet.style.opacity = '0';
                        setTimeout(() => {
                            packet.remove();
                        }, 300);
                    }, 1800);
                }, 300);
            }
            
            // 每2秒创建一个数据包
            setInterval(createDataPacket, 2000);
            
            // 延迟开始数据包动画
            setTimeout(() => {
                setInterval(createDataPacket, 2000);
            }, 6000);
        });
    </script>
</body>
</html>
