<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML基本骨架结构</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        .main-title {
            font-size: 4.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 300% 300%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .subtitle {
            font-size: 2.2rem;
            opacity: 0.9;
            font-weight: 300;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .content-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            max-width: 1400px;
            gap: 3rem;
        }

        .code-section {
            flex: 1;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            padding: 2rem;
            border: 3px solid #4ecdc4;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out 0.5s both;
            position: relative;
            overflow: hidden;
        }

        .code-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
            transition: left 0.8s ease;
        }

        .code-section:hover::before {
            left: 100%;
        }

        .code-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #4ecdc4;
        }

        .code-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-left: 1rem;
        }

        .code-icon {
            font-size: 3rem;
            color: #4ecdc4;
        }

        .code-block {
            background: #1a1a1a;
            border-radius: 15px;
            padding: 2rem;
            font-family: 'Courier New', monospace;
            font-size: 1.4rem;
            line-height: 1.8;
            border: 2px solid #333;
            position: relative;
            overflow-x: auto;
        }

        .code-line {
            display: block;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
        }

        .code-line:hover {
            background: rgba(78, 205, 196, 0.1);
            transform: translateX(10px);
        }

        .doctype { color: #ff6b6b; }
        .tag { color: #4ecdc4; }
        .attribute { color: #feca57; }
        .value { color: #96ceb4; }
        .comment { color: #95a5a6; font-style: italic; }
        .text { color: #ecf0f1; }

        .explanation-section {
            flex: 1;
            animation: slideInRight 1s ease-out 0.7s both;
        }

        .explanation-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .explanation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .explanation-card:hover::before {
            left: 100%;
        }

        .explanation-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .card-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #feca57;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .card-icon {
            font-size: 2.5rem;
        }

        .card-description {
            font-size: 1.4rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .summary {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-out 1.5s both;
            max-width: 1200px;
            margin-top: 2rem;
        }

        .summary-text {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            line-height: 1.4;
        }

        .highlight {
            color: #feca57;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        /* 动画定义 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-title { font-size: 3.5rem; }
            .subtitle { font-size: 1.8rem; }
            .content-container { flex-direction: column; gap: 2rem; }
            .code-title { font-size: 2rem; }
            .code-block { font-size: 1.2rem; }
            .card-title { font-size: 1.8rem; }
            .summary-text { font-size: 1.8rem; }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2.8rem; }
            .subtitle { font-size: 1.5rem; }
            .code-section, .explanation-section { padding: 1.5rem; }
            .code-title { font-size: 1.8rem; }
            .code-block { font-size: 1rem; padding: 1.5rem; }
            .card-title { font-size: 1.5rem; }
            .card-description { font-size: 1.2rem; }
            .summary-text { font-size: 1.5rem; }
        }

        /* 交互提示 */
        .interaction-hint {
            position: absolute;
            top: 2rem;
            right: 2rem;
            font-size: 1.3rem;
            opacity: 0.7;
            animation: fadeIn 2s ease-out 2s both;
        }

        /* 代码行号 */
        .line-number {
            color: #666;
            margin-right: 1rem;
            user-select: none;
            font-size: 1.2rem;
        }

        /* 特殊效果 */
        .typing-effect {
            overflow: hidden;
            border-right: 2px solid #4ecdc4;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #4ecdc4; }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 交互提示 -->
        <div class="interaction-hint">
            💡 点击代码行或说明卡片查看详细信息
        </div>

        <!-- 标题区域 -->
        <div class="header">
            <h1 class="main-title">HTML基本骨架结构</h1>
            <p class="subtitle">每个网页的标准起始模板</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-container">
            <!-- 代码展示区域 -->
            <div class="code-section">
                <div class="code-header">
                    <div class="code-icon">📄</div>
                    <div class="code-title">标准HTML模板</div>
                </div>
                <div class="code-block">
                    <span class="code-line" onclick="showLineDetails('doctype')">
                        <span class="line-number">1</span>
                        <span class="doctype">&lt;!doctype html&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('html')">
                        <span class="line-number">2</span>
                        <span class="tag">&lt;html</span> <span class="attribute">lang</span>=<span class="value">"zh-CN"</span><span class="tag">&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('head')">
                        <span class="line-number">3</span>
                        <span class="tag">&lt;head&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('charset')">
                        <span class="line-number">4</span>
                        &nbsp;&nbsp;<span class="tag">&lt;meta</span> <span class="attribute">charset</span>=<span class="value">"utf-8"</span><span class="tag">&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('viewport')">
                        <span class="line-number">5</span>
                        &nbsp;&nbsp;<span class="tag">&lt;meta</span> <span class="attribute">name</span>=<span class="value">"viewport"</span> <span class="attribute">content</span>=<span class="value">"width=device-width, initial-scale=1"</span><span class="tag">&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('title')">
                        <span class="line-number">6</span>
                        &nbsp;&nbsp;<span class="tag">&lt;title&gt;</span><span class="text">我的第一个页面</span><span class="tag">&lt;/title&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('head-close')">
                        <span class="line-number">7</span>
                        <span class="tag">&lt;/head&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('body')">
                        <span class="line-number">8</span>
                        <span class="tag">&lt;body&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('comment')">
                        <span class="line-number">9</span>
                        &nbsp;&nbsp;<span class="comment">&lt;!-- 内容放这里 --&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('body-close')">
                        <span class="line-number">10</span>
                        <span class="tag">&lt;/body&gt;</span>
                    </span>
                    <span class="code-line" onclick="showLineDetails('html-close')">
                        <span class="line-number">11</span>
                        <span class="tag">&lt;/html&gt;</span>
                    </span>
                </div>
            </div>

            <!-- 说明区域 -->
            <div class="explanation-section">
                <div class="explanation-card" onclick="showCardDetails('structure')">
                    <div class="card-title">
                        <span class="card-icon">🏗️</span>
                        文档结构
                    </div>
                    <div class="card-description">
                        HTML文档由DOCTYPE声明、html根元素、head头部和body主体四个主要部分组成，形成完整的网页结构。
                    </div>
                </div>

                <div class="explanation-card" onclick="showCardDetails('head')">
                    <div class="card-title">
                        <span class="card-icon">🧠</span>
                        头部信息
                    </div>
                    <div class="card-description">
                        head标签包含页面的元信息，如字符编码、视口设置、标题等，这些信息不会直接显示在页面上。
                    </div>
                </div>

                <div class="explanation-card" onclick="showCardDetails('body')">
                    <div class="card-title">
                        <span class="card-icon">📝</span>
                        页面内容
                    </div>
                    <div class="card-description">
                        body标签包含所有用户可见的页面内容，如文本、图片、链接、表单等元素。
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary">
            <div class="summary-text">
                🎯 这是每个HTML页面的<span class="highlight">标准起始模板</span><br>
                掌握这个基本结构是<span class="highlight">Web开发的第一步</span>
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
        <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 3rem; border-radius: 25px; max-width: 900px; width: 90%; color: white; position: relative; max-height: 80vh; overflow-y: auto;">
            <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 2.5rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
            <div id="modal-content"></div>
        </div>
    </div>

    <script>
        // 代码行详细信息
        const lineDetails = {
            doctype: {
                title: 'DOCTYPE声明',
                icon: '📋',
                description: 'HTML5文档类型声明',
                details: [
                    '告诉浏览器这是一个HTML5文档',
                    '必须放在文档的第一行',
                    '不区分大小写，但建议使用小写',
                    '省略DOCTYPE会导致浏览器进入怪异模式',
                    'HTML5简化了DOCTYPE声明'
                ],
                example: '<!doctype html>',
                note: '这是HTML5的标准声明方式，比之前版本简洁很多'
            },
            html: {
                title: 'HTML根元素',
                icon: '🌳',
                description: 'HTML文档的根容器元素',
                details: [
                    'html是所有HTML元素的父元素',
                    'lang属性指定文档的主要语言',
                    'zh-CN表示简体中文',
                    '有助于搜索引擎和屏幕阅读器',
                    '提高网页的可访问性'
                ],
                example: '<html lang="zh-CN">',
                note: '设置正确的语言属性对SEO和无障碍访问很重要'
            },
            head: {
                title: 'HEAD头部标签',
                icon: '🧠',
                description: '包含页面元信息的容器',
                details: [
                    '包含页面的元数据信息',
                    '内容不会直接显示在页面上',
                    '包含title、meta、link、script等标签',
                    '为搜索引擎和浏览器提供信息',
                    '影响页面的SEO和性能'
                ],
                example: '<head>\n  <!-- 元信息放这里 -->\n</head>',
                note: 'head中的信息虽然不可见，但对网页功能至关重要'
            },
            charset: {
                title: '字符编码设置',
                icon: '🔤',
                description: '指定文档的字符编码',
                details: [
                    'UTF-8是Unicode的一种实现',
                    '支持世界上几乎所有的字符',
                    '包括中文、英文、符号等',
                    '必须放在head标签内的前面',
                    '避免出现乱码问题'
                ],
                example: '<meta charset="utf-8">',
                note: 'UTF-8是现代网页的标准字符编码'
            },
            viewport: {
                title: '视口设置',
                icon: '📱',
                description: '控制页面在移动设备上的显示',
                details: [
                    'width=device-width：宽度等于设备宽度',
                    'initial-scale=1：初始缩放比例为1',
                    '确保页面在移动设备上正常显示',
                    '响应式设计的基础设置',
                    '提高移动端用户体验'
                ],
                example: '<meta name="viewport" content="width=device-width, initial-scale=1">',
                note: '这是响应式网页设计的必备设置'
            },
            title: {
                title: '页面标题',
                icon: '📑',
                description: '定义浏览器标签页显示的标题',
                details: [
                    '显示在浏览器标签页上',
                    '搜索引擎结果中的标题',
                    '书签保存时的默认名称',
                    '对SEO非常重要',
                    '建议长度在50-60个字符'
                ],
                example: '<title>我的第一个页面</title>',
                note: '好的标题能提高网页的搜索排名和点击率'
            },
            body: {
                title: 'BODY主体标签',
                icon: '📄',
                description: '包含所有可见页面内容',
                details: [
                    '包含用户可见的所有内容',
                    '文本、图片、链接、表单等',
                    '页面的主要展示区域',
                    '可以包含各种HTML元素',
                    '是页面交互的主要区域'
                ],
                example: '<body>\n  <!-- 页面内容 -->\n</body>',
                note: 'body中的内容就是用户在浏览器中看到的页面'
            },
            comment: {
                title: 'HTML注释',
                icon: '💬',
                description: '代码注释，不会显示在页面上',
                details: [
                    '用于代码说明和备注',
                    '不会在页面上显示',
                    '帮助开发者理解代码',
                    '可以临时隐藏代码',
                    '提高代码可维护性'
                ],
                example: '<!-- 这是注释 -->',
                note: '良好的注释习惯能让代码更易维护'
            }
        };

        // 卡片详细信息
        const cardDetails = {
            structure: {
                title: 'HTML文档结构详解',
                icon: '🏗️',
                description: 'HTML文档的层次结构和组织方式',
                content: [
                    'DOCTYPE声明：告诉浏览器文档类型',
                    'html根元素：包含整个文档内容',
                    'head头部：包含元信息和资源引用',
                    'body主体：包含所有可见内容',
                    '标签必须正确嵌套和闭合',
                    '遵循语义化HTML的最佳实践'
                ],
                tips: '良好的文档结构是网页可访问性和SEO的基础'
            },
            head: {
                title: 'HEAD标签详细说明',
                icon: '🧠',
                description: 'head标签中常见元素的作用和用法',
                content: [
                    'meta标签：定义元数据信息',
                    'title标签：设置页面标题',
                    'link标签：引入外部资源（CSS等）',
                    'script标签：引入JavaScript代码',
                    'style标签：内联CSS样式',
                    'base标签：设置基础URL'
                ],
                tips: 'head中的设置直接影响页面的性能、SEO和用户体验'
            },
            body: {
                title: 'BODY标签内容组织',
                icon: '📝',
                description: 'body标签中内容的组织和最佳实践',
                content: [
                    '语义化标签：header、nav、main、section、article、aside、footer',
                    '文本内容：h1-h6、p、span、strong、em等',
                    '媒体元素：img、video、audio等',
                    '交互元素：a、button、form、input等',
                    '布局元素：div、section、article等',
                    '列表元素：ul、ol、li等'
                ],
                tips: '使用语义化标签能提高网页的可访问性和搜索引擎友好度'
            }
        };

        function showLineDetails(lineType) {
            const data = lineDetails[lineType];
            if (data) {
                showModal(data, 'line');
            }
        }

        function showCardDetails(cardType) {
            const data = cardDetails[cardType];
            if (data) {
                showModal(data, 'card');
            }
        }

        function showModal(data, type) {
            const modal = document.getElementById('modal');
            const content = document.getElementById('modal-content');
            
            let html = `
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">${data.icon}</div>
                    <h2 style="font-size: 2.8rem; margin-bottom: 1rem;">${data.title}</h2>
                    <p style="font-size: 1.6rem; opacity: 0.9;">${data.description}</p>
                </div>
            `;
            
            if (type === 'line' && data.details) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #feca57;">详细说明</h3>
                        <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                            ${data.details.map(detail => `<li style="margin-bottom: 0.5rem;">${detail}</li>`).join('')}
                        </ul>
                    </div>
                `;
                
                if (data.example) {
                    html += `
                        <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(0,0,0,0.3); border-radius: 15px;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #feca57;">代码示例</h3>
                            <pre style="font-size: 1.2rem; line-height: 1.4; color: #4ecdc4; font-family: 'Courier New', monospace; white-space: pre-wrap;">${data.example}</pre>
                        </div>
                    `;
                }
                
                if (data.note) {
                    html += `
                        <div style="padding: 1.5rem; background: rgba(78, 205, 196, 0.2); border-radius: 15px; border-left: 4px solid #4ecdc4;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #4ecdc4;">💡 ${data.note}</h3>
                        </div>
                    `;
                }
            }
            
            if (type === 'card' && data.content) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #feca57;">主要内容</h3>
                        <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                            ${data.content.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                        </ul>
                    </div>
                `;
                
                if (data.tips) {
                    html += `
                        <div style="padding: 1.5rem; background: rgba(254, 202, 87, 0.2); border-radius: 15px; border-left: 4px solid #feca57;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #feca57;">💡 ${data.tips}</h3>
                        </div>
                    `;
                }
            }
            
            content.innerHTML = html;
            modal.style.display = 'flex';
            
            // 添加动画效果
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
            
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
        });

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 代码行高亮效果
        document.addEventListener('DOMContentLoaded', function() {
            const codeLines = document.querySelectorAll('.code-line');
            let currentIndex = 0;
            
            function highlightNextLine() {
                // 重置所有行
                codeLines.forEach(line => {
                    line.style.background = '';
                    line.style.transform = '';
                });
                
                // 高亮当前行
                if (codeLines[currentIndex]) {
                    codeLines[currentIndex].style.background = 'rgba(78, 205, 196, 0.2)';
                    codeLines[currentIndex].style.transform = 'translateX(10px)';
                }
                
                currentIndex = (currentIndex + 1) % codeLines.length;
            }
            
            // 每2秒高亮下一行
            setInterval(highlightNextLine, 2000);
            
            // 初始高亮第一行
            setTimeout(highlightNextLine, 3000);
        });
    </script>
</body>
</html>
