<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1周：Web开发概述与环境搭建</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <section class="slide active" id="slide-1">
            <div class="slide-content">
                <h1>Web程序设计</h1>
                <h2>第1周：Web开发概述与环境搭建</h2>
                <div class="course-info">
                    <p>课程目标：</p>
                    <ul>
                        <li>了解Web发展历史与基本原理</li>
                        <li>掌握HTTP协议基础知识</li>
                        <li>配置Web开发环境</li>
                        <li>创建第一个HTML页面</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Web发展历史 -->
        <section class="slide" id="slide-2">
            <div class="slide-content">
                <h2>Web发展历史</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="year">1989</div>
                        <div class="content">Tim Berners-Lee 发明万维网(WWW)</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">1993</div>
                        <div class="content">第一个图形化浏览器 Mosaic 发布</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">1995</div>
                        <div class="content">JavaScript 诞生，网页开始具备交互性</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2004</div>
                        <div class="content">AJAX 技术兴起，Web 2.0 时代开始</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2008</div>
                        <div class="content">HTML5 标准制定，现代Web开发开始</div>
                    </div>
                    <div class="timeline-item">
                        <div class="year">2010+</div>
                        <div class="content">移动互联网、响应式设计、前端框架时代</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Web基本原理 -->
        <section class="slide" id="slide-3">
            <div class="slide-content">
                <h2>Web基本原理</h2>
                <div class="web-architecture">
                    <div class="component client">
                        <h3>客户端 (Client)</h3>
                        <ul>
                            <li>浏览器 (Browser)</li>
                            <li>移动应用</li>
                            <li>桌面应用</li>
                        </ul>
                    </div>
                    <div class="arrow">→</div>
                    <div class="component server">
                        <h3>服务器 (Server)</h3>
                        <ul>
                            <li>Web服务器</li>
                            <li>应用服务器</li>
                            <li>数据库服务器</li>
                        </ul>
                    </div>
                </div>
                <div class="protocol">
                    <h3>通信协议：HTTP/HTTPS</h3>
                    <p>客户端通过HTTP协议向服务器发送请求，服务器返回响应</p>
                </div>
            </div>
        </section>

        <!-- HTTP协议介绍 -->
        <section class="slide" id="slide-4">
            <div class="slide-content">
                <h2>什么是HTTP协议？</h2>
                <div class="http-intro">
                    <div class="http-definition">
                        <h3>HTTP (HyperText Transfer Protocol)</h3>
                        <p>超文本传输协议，是Web通信的基础协议</p>
                    </div>
                    <div class="http-characteristics">
                        <h3>主要特点</h3>
                        <ul>
                            <li><strong>无状态</strong> - 每个请求都是独立的</li>
                            <li><strong>基于TCP/IP</strong> - 可靠的传输层协议</li>
                            <li><strong>请求-响应模式</strong> - 客户端发起，服务器响应</li>
                            <li><strong>文本协议</strong> - 人类可读的协议格式</li>
                        </ul>
                    </div>
                    <div class="http-versions">
                        <h3>版本演进</h3>
                        <ul>
                            <li><strong>HTTP/0.9</strong> (1991) - 只支持GET方法</li>
                            <li><strong>HTTP/1.0</strong> (1996) - 添加头部信息</li>
                            <li><strong>HTTP/1.1</strong> (1997) - 持久连接</li>
                            <li><strong>HTTP/2</strong> (2015) - 多路复用</li>
                            <li><strong>HTTP/3</strong> (2022) - 基于QUIC</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP请求响应模型 -->
        <section class="slide" id="slide-21">
            <div class="slide-content">
                <h2>HTTP请求-响应模型</h2>
                <div class="request-response-model">
                    <div class="model-diagram">
                        <div class="client-side">
                            <div class="client-box">
                                <h4>客户端 (浏览器)</h4>
                                <div class="request-arrow">
                                    <span>HTTP请求</span>
                                    <div class="arrow-right">→</div>
                                </div>
                            </div>
                        </div>
                        <div class="server-side">
                            <div class="server-box">
                                <h4>服务器</h4>
                                <div class="response-arrow">
                                    <div class="arrow-left">←</div>
                                    <span>HTTP响应</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="communication-steps">
                        <h3>通信步骤</h3>
                        <ol>
                            <li>用户在浏览器中输入URL或点击链接</li>
                            <li>浏览器解析URL，建立TCP连接</li>
                            <li>浏览器发送HTTP请求到服务器</li>
                            <li>服务器处理请求，生成响应</li>
                            <li>服务器发送HTTP响应给浏览器</li>
                            <li>浏览器接收响应，渲染页面</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP请求结构 -->
        <section class="slide" id="slide-22">
            <div class="slide-content">
                <h2>HTTP请求结构</h2>
                <div class="request-structure">
                    <div class="request-example">
                        <h3>完整的HTTP请求示例</h3>
                        <pre><code>GET /api/users/123 HTTP/1.1
Host: www.example.com
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)
Accept: application/json
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Connection: keep-alive
Cookie: sessionId=abc123

</code></pre>
                    </div>
                    <div class="request-parts">
                        <div class="part">
                            <h4>1. 请求行 (Request Line)</h4>
                            <p><code>GET /api/users/123 HTTP/1.1</code></p>
                            <ul>
                                <li><strong>方法</strong>: GET</li>
                                <li><strong>路径</strong>: /api/users/123</li>
                                <li><strong>版本</strong>: HTTP/1.1</li>
                            </ul>
                        </div>
                        <div class="part">
                            <h4>2. 请求头 (Request Headers)</h4>
                            <ul>
                                <li><strong>Host</strong>: 目标服务器</li>
                                <li><strong>User-Agent</strong>: 客户端信息</li>
                                <li><strong>Accept</strong>: 可接受的内容类型</li>
                            </ul>
                        </div>
                        <div class="part">
                            <h4>3. 空行</h4>
                            <p>分隔头部和主体</p>
                        </div>
                        <div class="part">
                            <h4>4. 请求主体 (可选)</h4>
                            <p>POST/PUT请求的数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP响应结构 -->
        <section class="slide" id="slide-23">
            <div class="slide-content">
                <h2>HTTP响应结构</h2>
                <div class="response-structure">
                    <div class="response-example">
                        <h3>完整的HTTP响应示例</h3>
                        <pre><code>HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 85
Server: nginx/1.18.0
Date: Mon, 12 Aug 2024 10:30:00 GMT
Connection: keep-alive

{
  "id": 123,
  "name": "张三",
  "email": "<EMAIL>"
}</code></pre>
                    </div>
                    <div class="response-parts">
                        <div class="part">
                            <h4>1. 状态行 (Status Line)</h4>
                            <p><code>HTTP/1.1 200 OK</code></p>
                            <ul>
                                <li><strong>版本</strong>: HTTP/1.1</li>
                                <li><strong>状态码</strong>: 200</li>
                                <li><strong>状态描述</strong>: OK</li>
                            </ul>
                        </div>
                        <div class="part">
                            <h4>2. 响应头 (Response Headers)</h4>
                            <ul>
                                <li><strong>Content-Type</strong>: 内容类型</li>
                                <li><strong>Content-Length</strong>: 内容长度</li>
                                <li><strong>Server</strong>: 服务器信息</li>
                            </ul>
                        </div>
                        <div class="part">
                            <h4>3. 空行</h4>
                            <p>分隔头部和主体</p>
                        </div>
                        <div class="part">
                            <h4>4. 响应主体</h4>
                            <p>实际的内容数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP方法详解 -->
        <section class="slide" id="slide-24">
            <div class="slide-content">
                <h2>HTTP方法详解</h2>
                <div class="http-methods-detail">
                    <div class="method-item">
                        <h3>GET - 获取资源</h3>
                        <ul>
                            <li><strong>用途</strong>: 请求指定资源</li>
                            <li><strong>特点</strong>: 安全、幂等、可缓存</li>
                            <li><strong>参数</strong>: 通过URL查询字符串传递</li>
                            <li><strong>示例</strong>: <code>GET /users?page=1&size=10</code></li>
                        </ul>
                    </div>
                    <div class="method-item">
                        <h3>POST - 提交数据</h3>
                        <ul>
                            <li><strong>用途</strong>: 向服务器提交数据</li>
                            <li><strong>特点</strong>: 不安全、非幂等、不可缓存</li>
                            <li><strong>参数</strong>: 通过请求主体传递</li>
                            <li><strong>示例</strong>: 创建新用户、提交表单</li>
                        </ul>
                    </div>
                    <div class="method-item">
                        <h3>PUT - 更新资源</h3>
                        <ul>
                            <li><strong>用途</strong>: 更新或创建指定资源</li>
                            <li><strong>特点</strong>: 不安全、幂等</li>
                            <li><strong>参数</strong>: 通过请求主体传递</li>
                            <li><strong>示例</strong>: <code>PUT /users/123</code></li>
                        </ul>
                    </div>
                    <div class="method-item">
                        <h3>DELETE - 删除资源</h3>
                        <ul>
                            <li><strong>用途</strong>: 删除指定资源</li>
                            <li><strong>特点</strong>: 不安全、幂等</li>
                            <li><strong>参数</strong>: 通常无需主体</li>
                            <li><strong>示例</strong>: <code>DELETE /users/123</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP状态码详解 -->
        <section class="slide" id="slide-25">
            <div class="slide-content">
                <h2>HTTP状态码详解</h2>
                <div class="status-codes">
                    <div class="status-category">
                        <h3>1xx - 信息性状态码</h3>
                        <ul>
                            <li><strong>100 Continue</strong> - 继续请求</li>
                            <li><strong>101 Switching Protocols</strong> - 切换协议</li>
                        </ul>
                    </div>
                    <div class="status-category">
                        <h3>2xx - 成功状态码</h3>
                        <ul>
                            <li><strong>200 OK</strong> - 请求成功</li>
                            <li><strong>201 Created</strong> - 资源已创建</li>
                            <li><strong>204 No Content</strong> - 无内容返回</li>
                        </ul>
                    </div>
                    <div class="status-category">
                        <h3>3xx - 重定向状态码</h3>
                        <ul>
                            <li><strong>301 Moved Permanently</strong> - 永久重定向</li>
                            <li><strong>302 Found</strong> - 临时重定向</li>
                            <li><strong>304 Not Modified</strong> - 未修改</li>
                        </ul>
                    </div>
                    <div class="status-category">
                        <h3>4xx - 客户端错误</h3>
                        <ul>
                            <li><strong>400 Bad Request</strong> - 请求错误</li>
                            <li><strong>401 Unauthorized</strong> - 未授权</li>
                            <li><strong>403 Forbidden</strong> - 禁止访问</li>
                            <li><strong>404 Not Found</strong> - 资源未找到</li>
                        </ul>
                    </div>
                    <div class="status-category">
                        <h3>5xx - 服务器错误</h3>
                        <ul>
                            <li><strong>500 Internal Server Error</strong> - 服务器内部错误</li>
                            <li><strong>502 Bad Gateway</strong> - 网关错误</li>
                            <li><strong>503 Service Unavailable</strong> - 服务不可用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTP头部字段 -->
        <section class="slide" id="slide-26">
            <div class="slide-content">
                <h2>常用HTTP头部字段</h2>
                <div class="http-headers">
                    <div class="header-category">
                        <h3>请求头部</h3>
                        <div class="header-list">
                            <div class="header-item">
                                <strong>Host</strong>
                                <p>指定服务器的域名和端口</p>
                                <code>Host: www.example.com:8080</code>
                            </div>
                            <div class="header-item">
                                <strong>User-Agent</strong>
                                <p>客户端的信息</p>
                                <code>User-Agent: Mozilla/5.0...</code>
                            </div>
                            <div class="header-item">
                                <strong>Accept</strong>
                                <p>客户端可接受的内容类型</p>
                                <code>Accept: text/html,application/json</code>
                            </div>
                            <div class="header-item">
                                <strong>Authorization</strong>
                                <p>身份验证信息</p>
                                <code>Authorization: Bearer token123</code>
                            </div>
                        </div>
                    </div>
                    <div class="header-category">
                        <h3>响应头部</h3>
                        <div class="header-list">
                            <div class="header-item">
                                <strong>Content-Type</strong>
                                <p>响应内容的类型</p>
                                <code>Content-Type: text/html; charset=utf-8</code>
                            </div>
                            <div class="header-item">
                                <strong>Content-Length</strong>
                                <p>响应内容的长度</p>
                                <code>Content-Length: 1024</code>
                            </div>
                            <div class="header-item">
                                <strong>Set-Cookie</strong>
                                <p>设置Cookie</p>
                                <code>Set-Cookie: sessionId=abc123</code>
                            </div>
                            <div class="header-item">
                                <strong>Cache-Control</strong>
                                <p>缓存控制</p>
                                <code>Cache-Control: max-age=3600</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTTPS安全协议 -->
        <section class="slide" id="slide-27">
            <div class="slide-content">
                <h2>HTTPS - 安全的HTTP</h2>
                <div class="https-info">
                    <div class="https-intro">
                        <h3>什么是HTTPS？</h3>
                        <p>HTTPS = HTTP + SSL/TLS</p>
                        <p>在HTTP基础上加入SSL/TLS加密层，确保数据传输安全</p>
                    </div>
                    <div class="https-benefits">
                        <h3>HTTPS的优势</h3>
                        <ul>
                            <li><strong>数据加密</strong> - 防止数据被窃听</li>
                            <li><strong>身份验证</strong> - 确认服务器身份</li>
                            <li><strong>数据完整性</strong> - 防止数据被篡改</li>
                            <li><strong>SEO优势</strong> - 搜索引擎优先排名</li>
                        </ul>
                    </div>
                    <div class="https-process">
                        <h3>HTTPS握手过程</h3>
                        <ol>
                            <li>客户端发送ClientHello消息</li>
                            <li>服务器发送ServerHello和证书</li>
                            <li>客户端验证证书</li>
                            <li>生成会话密钥</li>
                            <li>开始加密通信</li>
                        </ol>
                    </div>
                    <div class="https-visual">
                        <div class="http-flow">
                            <h4>HTTP (不安全)</h4>
                            <div class="flow-item">客户端 → 明文数据 → 服务器</div>
                            <p class="warning">⚠️ 数据可被窃听和篡改</p>
                        </div>
                        <div class="https-flow">
                            <h4>HTTPS (安全)</h4>
                            <div class="flow-item">客户端 → 加密数据 → 服务器</div>
                            <p class="secure">🔒 数据经过加密保护</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 开发环境配置介绍 -->
        <!-- 开发环境概述 -->
        <section class="slide" id="slide-28">
            <div class="slide-content">
                <h2>Web开发环境概述</h2>
                <div class="dev-environment">
                    <div class="env-components">
                        <h3>开发环境组成</h3>
                        <div class="component-grid">
                            <div class="component">
                                <div class="component-icon">💻</div>
                                <h4>代码编辑器</h4>
                                <p>编写和编辑代码的工具</p>
                            </div>
                            <div class="component">
                                <div class="component-icon">🌐</div>
                                <h4>浏览器</h4>
                                <p>运行和测试Web应用</p>
                            </div>
                            <div class="component">
                                <div class="component-icon">🔧</div>
                                <h4>开发工具</h4>
                                <p>调试和优化代码</p>
                            </div>
                            <div class="component">
                                <div class="component-icon">📦</div>
                                <h4>包管理器</h4>
                                <p>管理项目依赖</p>
                            </div>
                        </div>
                    </div>
                    <div class="env-workflow">
                        <h3>开发工作流</h3>
                        <div class="workflow-steps">
                            <div class="step">1. 编写代码</div>
                            <div class="arrow">→</div>
                            <div class="step">2. 保存文件</div>
                            <div class="arrow">→</div>
                            <div class="step">3. 浏览器预览</div>
                            <div class="arrow">→</div>
                            <div class="step">4. 调试优化</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VS Code详细介绍 -->
        <section class="slide" id="slide-29">
            <div class="slide-content">
                <h2>Visual Studio Code</h2>
                <div class="vscode-intro">
                    <div class="vscode-features">
                        <h3>为什么选择VS Code？</h3>
                        <ul>
                            <li><strong>免费开源</strong> - 完全免费使用</li>
                            <li><strong>跨平台</strong> - Windows、Mac、Linux</li>
                            <li><strong>轻量快速</strong> - 启动速度快，占用资源少</li>
                            <li><strong>丰富插件</strong> - 强大的扩展生态系统</li>
                            <li><strong>智能提示</strong> - IntelliSense代码补全</li>
                            <li><strong>集成终端</strong> - 内置命令行工具</li>
                        </ul>
                    </div>
                    <div class="vscode-interface">
                        <h3>界面布局</h3>
                        <div class="interface-parts">
                            <div class="part">
                                <strong>活动栏</strong> - 左侧图标栏
                            </div>
                            <div class="part">
                                <strong>侧边栏</strong> - 文件资源管理器
                            </div>
                            <div class="part">
                                <strong>编辑器</strong> - 代码编辑区域
                            </div>
                            <div class="part">
                                <strong>面板</strong> - 终端和输出
                            </div>
                            <div class="part">
                                <strong>状态栏</strong> - 底部状态信息
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VS Code安装配置 -->
        <section class="slide" id="slide-30">
            <div class="slide-content">
                <h2>VS Code安装与配置</h2>
                <div class="vscode-setup">
                    <div class="installation">
                        <h3>安装步骤</h3>
                        <ol>
                            <li>访问 <strong>code.visualstudio.com</strong></li>
                            <li>下载适合你操作系统的版本</li>
                            <li>运行安装程序</li>
                            <li>选择安装选项：
                                <ul>
                                    <li>✅ 添加到PATH环境变量</li>
                                    <li>✅ 注册为支持的文件类型的编辑器</li>
                                    <li>✅ 添加"通过Code打开"操作</li>
                                </ul>
                            </li>
                            <li>完成安装并启动</li>
                        </ol>
                    </div>
                    <div class="basic-config">
                        <h3>基本配置</h3>
                        <div class="config-item">
                            <h4>设置中文界面</h4>
                            <p>安装 <strong>Chinese (Simplified)</strong> 语言包</p>
                        </div>
                        <div class="config-item">
                            <h4>调整字体大小</h4>
                            <p>File → Preferences → Settings → Font Size</p>
                        </div>
                        <div class="config-item">
                            <h4>选择主题</h4>
                            <p>File → Preferences → Color Theme</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VS Code必装插件 -->
        <section class="slide" id="slide-31">
            <div class="slide-content">
                <h2>VS Code必装插件</h2>
                <div class="essential-extensions">
                    <div class="extension-category">
                        <h3>Web开发核心插件</h3>
                        <div class="extension-list">
                            <div class="extension-item">
                                <h4>Live Server</h4>
                                <p>实时预览网页，自动刷新</p>
                                <div class="extension-usage">
                                    <strong>使用：</strong>右键HTML文件 → Open with Live Server
                                </div>
                            </div>
                            <div class="extension-item">
                                <h4>HTML CSS Support</h4>
                                <p>增强HTML和CSS的智能提示</p>
                                <div class="extension-usage">
                                    <strong>功能：</strong>自动补全、语法检查
                                </div>
                            </div>
                            <div class="extension-item">
                                <h4>Auto Rename Tag</h4>
                                <p>自动重命名配对的HTML标签</p>
                                <div class="extension-usage">
                                    <strong>效果：</strong>修改开始标签，结束标签自动更新
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="extension-category">
                        <h3>代码质量插件</h3>
                        <div class="extension-list">
                            <div class="extension-item">
                                <h4>Prettier - Code formatter</h4>
                                <p>代码格式化工具</p>
                                <div class="extension-usage">
                                    <strong>快捷键：</strong>Alt + Shift + F
                                </div>
                            </div>
                            <div class="extension-item">
                                <h4>Bracket Pair Colorizer</h4>
                                <p>括号配对着色</p>
                                <div class="extension-usage">
                                    <strong>效果：</strong>不同层级括号显示不同颜色
                                </div>
                            </div>
                            <div class="extension-item">
                                <h4>indent-rainbow</h4>
                                <p>缩进彩虹线</p>
                                <div class="extension-usage">
                                    <strong>效果：</strong>不同缩进级别显示不同颜色
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VS Code快捷键 -->
        <section class="slide" id="slide-32">
            <div class="slide-content">
                <h2>VS Code常用快捷键</h2>
                <div class="shortcuts">
                    <div class="shortcut-category">
                        <h3>文件操作</h3>
                        <div class="shortcut-list">
                            <div class="shortcut-item">
                                <span class="key">Ctrl + N</span>
                                <span class="action">新建文件</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + O</span>
                                <span class="action">打开文件</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + S</span>
                                <span class="action">保存文件</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + Shift + S</span>
                                <span class="action">另存为</span>
                            </div>
                        </div>
                    </div>
                    <div class="shortcut-category">
                        <h3>编辑操作</h3>
                        <div class="shortcut-list">
                            <div class="shortcut-item">
                                <span class="key">Ctrl + Z</span>
                                <span class="action">撤销</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + Y</span>
                                <span class="action">重做</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + F</span>
                                <span class="action">查找</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + H</span>
                                <span class="action">替换</span>
                            </div>
                        </div>
                    </div>
                    <div class="shortcut-category">
                        <h3>代码操作</h3>
                        <div class="shortcut-list">
                            <div class="shortcut-item">
                                <span class="key">Ctrl + /</span>
                                <span class="action">注释/取消注释</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Alt + Shift + F</span>
                                <span class="action">格式化代码</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Ctrl + D</span>
                                <span class="action">选择相同内容</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="key">Alt + ↑/↓</span>
                                <span class="action">移动行</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 浏览器选择 -->
        <section class="slide" id="slide-33">
            <div class="slide-content">
                <h2>选择合适的浏览器</h2>
                <div class="browser-comparison">
                    <div class="browser-item">
                        <div class="browser-logo">🌐</div>
                        <h3>Google Chrome</h3>
                        <div class="pros-cons">
                            <div class="pros">
                                <h4>优势</h4>
                                <ul>
                                    <li>强大的开发者工具</li>
                                    <li>最新Web标准支持</li>
                                    <li>丰富的扩展程序</li>
                                    <li>性能优秀</li>
                                </ul>
                            </div>
                            <div class="cons">
                                <h4>劣势</h4>
                                <ul>
                                    <li>内存占用较高</li>
                                    <li>隐私保护一般</li>
                                </ul>
                            </div>
                        </div>
                        <div class="recommendation">推荐用于：Web开发和调试</div>
                    </div>
                    <div class="browser-item">
                        <div class="browser-logo">🦊</div>
                        <h3>Mozilla Firefox</h3>
                        <div class="pros-cons">
                            <div class="pros">
                                <h4>优势</h4>
                                <ul>
                                    <li>优秀的开发者工具</li>
                                    <li>注重隐私保护</li>
                                    <li>开源免费</li>
                                    <li>CSS Grid调试工具</li>
                                </ul>
                            </div>
                            <div class="cons">
                                <h4>劣势</h4>
                                <ul>
                                    <li>市场份额较小</li>
                                    <li>某些新特性支持较慢</li>
                                </ul>
                            </div>
                        </div>
                        <div class="recommendation">推荐用于：CSS调试和隐私保护</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 浏览器开发者工具概述 -->
        <section class="slide" id="slide-34">
            <div class="slide-content">
                <h2>浏览器开发者工具概述</h2>
                <div class="devtools-intro">
                    <div class="devtools-purpose">
                        <h3>开发者工具的作用</h3>
                        <ul>
                            <li><strong>调试代码</strong> - 查找和修复错误</li>
                            <li><strong>检查元素</strong> - 查看HTML结构和CSS样式</li>
                            <li><strong>性能分析</strong> - 优化网页加载速度</li>
                            <li><strong>网络监控</strong> - 查看资源加载情况</li>
                            <li><strong>移动端测试</strong> - 模拟不同设备</li>
                        </ul>
                    </div>
                    <div class="devtools-access">
                        <h3>如何打开开发者工具</h3>
                        <div class="access-methods">
                            <div class="method">
                                <h4>键盘快捷键</h4>
                                <ul>
                                    <li><strong>F12</strong> - 最常用</li>
                                    <li><strong>Ctrl + Shift + I</strong> - 通用快捷键</li>
                                    <li><strong>Ctrl + Shift + C</strong> - 直接进入元素选择模式</li>
                                </ul>
                            </div>
                            <div class="method">
                                <h4>右键菜单</h4>
                                <ul>
                                    <li>右键点击页面元素</li>
                                    <li>选择"检查"或"检查元素"</li>
                                </ul>
                            </div>
                            <div class="method">
                                <h4>浏览器菜单</h4>
                                <ul>
                                    <li>Chrome: 更多工具 → 开发者工具</li>
                                    <li>Firefox: Web开发者 → 切换工具箱</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Elements面板详解 -->
        <section class="slide" id="slide-35">
            <div class="slide-content">
                <h2>Elements面板 - HTML和CSS检查</h2>
                <div class="elements-panel">
                    <div class="panel-features">
                        <h3>主要功能</h3>
                        <ul>
                            <li><strong>查看HTML结构</strong> - 实时DOM树</li>
                            <li><strong>编辑HTML</strong> - 双击即可修改</li>
                            <li><strong>查看CSS样式</strong> - 所有应用的样式</li>
                            <li><strong>修改CSS</strong> - 实时预览效果</li>
                            <li><strong>计算样式</strong> - 最终生效的样式</li>
                        </ul>
                    </div>
                    <div class="panel-layout">
                        <h3>面板布局</h3>
                        <div class="layout-parts">
                            <div class="part">
                                <h4>左侧：DOM树</h4>
                                <ul>
                                    <li>显示HTML元素层次结构</li>
                                    <li>可以展开/折叠元素</li>
                                    <li>高亮显示选中元素</li>
                                </ul>
                            </div>
                            <div class="part">
                                <h4>右侧：样式面板</h4>
                                <ul>
                                    <li>Styles - 应用的CSS规则</li>
                                    <li>Computed - 计算后的样式</li>
                                    <li>Event Listeners - 事件监听器</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="practical-tips">
                        <h3>实用技巧</h3>
                        <ul>
                            <li><strong>元素选择器</strong> - 点击左上角箭头图标选择元素</li>
                            <li><strong>搜索元素</strong> - Ctrl+F 在DOM中搜索</li>
                            <li><strong>复制元素</strong> - 右键复制HTML或CSS</li>
                            <li><strong>删除元素</strong> - 选中后按Delete键</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Console面板详解 -->
        <section class="slide" id="slide-36">
            <div class="slide-content">
                <h2>Console面板 - JavaScript控制台</h2>
                <div class="console-panel">
                    <div class="console-functions">
                        <h3>主要功能</h3>
                        <ul>
                            <li><strong>执行JavaScript代码</strong> - 实时运行</li>
                            <li><strong>查看错误信息</strong> - 语法和运行时错误</li>
                            <li><strong>调试输出</strong> - console.log()等</li>
                            <li><strong>查看警告</strong> - 性能和兼容性警告</li>
                        </ul>
                    </div>
                    <div class="console-commands">
                        <h3>常用Console命令</h3>
                        <div class="command-list">
                            <div class="command-item">
                                <code>console.log("Hello World")</code>
                                <p>输出信息到控制台</p>
                            </div>
                            <div class="command-item">
                                <code>console.error("错误信息")</code>
                                <p>输出错误信息</p>
                            </div>
                            <div class="command-item">
                                <code>console.warn("警告信息")</code>
                                <p>输出警告信息</p>
                            </div>
                            <div class="command-item">
                                <code>console.table(data)</code>
                                <p>以表格形式显示数据</p>
                            </div>
                            <div class="command-item">
                                <code>console.clear()</code>
                                <p>清空控制台</p>
                            </div>
                        </div>
                    </div>
                    <div class="console-tips">
                        <h3>使用技巧</h3>
                        <ul>
                            <li><strong>多行输入</strong> - Shift + Enter 换行</li>
                            <li><strong>历史记录</strong> - ↑↓ 键查看历史命令</li>
                            <li><strong>自动补全</strong> - Tab 键自动补全</li>
                            <li><strong>过滤信息</strong> - 按类型过滤显示</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Network面板详解 -->
        <section class="slide" id="slide-37">
            <div class="slide-content">
                <h2>Network面板 - 网络请求监控</h2>
                <div class="network-panel">
                    <div class="network-features">
                        <h3>主要功能</h3>
                        <ul>
                            <li><strong>监控网络请求</strong> - 所有HTTP请求</li>
                            <li><strong>查看请求详情</strong> - 头部、响应、时间</li>
                            <li><strong>性能分析</strong> - 加载时间和大小</li>
                            <li><strong>缓存状态</strong> - 资源缓存情况</li>
                        </ul>
                    </div>
                    <div class="network-info">
                        <h3>请求信息解读</h3>
                        <div class="info-columns">
                            <div class="column">
                                <h4>Name</h4>
                                <p>请求的资源名称</p>
                            </div>
                            <div class="column">
                                <h4>Status</h4>
                                <p>HTTP状态码</p>
                            </div>
                            <div class="column">
                                <h4>Type</h4>
                                <p>资源类型(HTML, CSS, JS等)</p>
                            </div>
                            <div class="column">
                                <h4>Size</h4>
                                <p>资源大小</p>
                            </div>
                            <div class="column">
                                <h4>Time</h4>
                                <p>加载时间</p>
                            </div>
                        </div>
                    </div>
                    <div class="network-tips">
                        <h3>使用技巧</h3>
                        <ul>
                            <li><strong>刷新页面</strong> - 查看完整加载过程</li>
                            <li><strong>过滤请求</strong> - 按类型过滤(JS, CSS, XHR等)</li>
                            <li><strong>禁用缓存</strong> - 勾选"Disable cache"</li>
                            <li><strong>模拟网速</strong> - 选择网络条件</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第一个HTML页面介绍 -->
        <section class="slide" id="slide-38">
            <div class="slide-content">
                <h2>第一个HTML页面</h2>
                <div class="code-demo">
                    <h3>基本HTML结构</h3>
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;我的第一个网页&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;欢迎来到Web世界！&lt;/h1&gt;
    &lt;p&gt;这是我的第一个HTML页面。&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <div class="html-explanation">
                    <h3>结构说明</h3>
                    <ul>
                        <li><code>&lt;!DOCTYPE html&gt;</code> - 声明文档类型</li>
                        <li><code>&lt;html&gt;</code> - 根元素</li>
                        <li><code>&lt;head&gt;</code> - 文档头部，包含元数据</li>
                        <li><code>&lt;body&gt;</code> - 文档主体，包含可见内容</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 课堂练习 -->
        <section class="slide" id="slide-7">
            <div class="slide-content">
                <h2>课堂练习</h2>
                <div class="exercise">
                    <h3>练习1：环境配置</h3>
                    <ol>
                        <li>安装VS Code编辑器</li>
                        <li>安装Live Server插件</li>
                        <li>打开浏览器开发者工具</li>
                    </ol>
                    
                    <h3>练习2：创建第一个网页</h3>
                    <ol>
                        <li>创建一个新的HTML文件</li>
                        <li>编写基本的HTML结构</li>
                        <li>添加标题和段落内容</li>
                        <li>使用Live Server预览页面</li>
                    </ol>
                    
                    <h3>练习3：使用开发者工具</h3>
                    <ol>
                        <li>在浏览器中打开你的网页</li>
                        <li>使用F12打开开发者工具</li>
                        <li>在Elements面板中查看HTML结构</li>
                        <li>在Console面板中输入简单的JavaScript代码</li>
                    </ol>
                </div>
            </div>
        </section>

        <!-- Web发展历史详细介绍 -->
        <section class="slide" id="slide-8">
            <div class="slide-content">
                <h2>Web诞生的背景</h2>
                <div class="background-info">
                    <div class="timeline-context">
                        <h3>1980年代末的计算机世界</h3>
                        <ul>
                            <li>个人计算机开始普及</li>
                            <li>互联网基础设施初步建立</li>
                            <li>信息孤岛问题严重</li>
                            <li>缺乏统一的信息共享标准</li>
                        </ul>
                    </div>
                    <div class="problem-solution">
                        <h3>Tim Berners-Lee面临的挑战</h3>
                        <div class="challenge-box">
                            <p><strong>问题：</strong>CERN（欧洲核子研究中心）的科学家们需要一种简单的方式来共享研究文档和数据</p>
                            <p><strong>解决方案：</strong>创建一个基于超文本的信息系统</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- WWW的三大核心技术 -->
        <section class="slide" id="slide-9">
            <div class="slide-content">
                <h2>WWW的三大核心技术</h2>
                <div class="core-technologies">
                    <div class="tech-item">
                        <div class="tech-icon">🌐</div>
                        <h3>URL (统一资源定位符)</h3>
                        <p>为每个网页分配唯一的地址</p>
                        <div class="example">
                            <code>https://www.example.com/page.html</code>
                        </div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">📄</div>
                        <h3>HTML (超文本标记语言)</h3>
                        <p>描述网页内容和结构的标记语言</p>
                        <div class="example">
                            <code>&lt;h1&gt;标题&lt;/h1&gt;</code>
                        </div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-icon">🔄</div>
                        <h3>HTTP (超文本传输协议)</h3>
                        <p>客户端和服务器之间的通信协议</p>
                        <div class="example">
                            <code>GET /index.html HTTP/1.1</code>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第一个网站 -->
        <section class="slide" id="slide-10">
            <div class="slide-content">
                <h2>世界上第一个网站</h2>
                <div class="first-website">
                    <div class="website-info">
                        <h3>基本信息</h3>
                        <ul>
                            <li><strong>URL：</strong>http://info.cern.ch/hypertext/WWW/TheProject.html</li>
                            <li><strong>上线时间：</strong>1991年8月6日</li>
                            <li><strong>创建者：</strong>Tim Berners-Lee</li>
                            <li><strong>内容：</strong>介绍万维网项目</li>
                        </ul>
                    </div>
                    <div class="website-screenshot">
                        <div class="mock-browser">
                            <div class="browser-header">
                                <div class="browser-buttons"></div>
                                <div class="address-bar">http://info.cern.ch/hypertext/WWW/TheProject.html</div>
                            </div>
                            <div class="browser-content">
                                <h4>World Wide Web</h4>
                                <p>The WorldWideWeb (W3) is a wide-area hypermedia information retrieval initiative aiming to give universal access to a large universe of documents.</p>
                                <p><a href="#">What's out there?</a></p>
                                <p><a href="#">Help</a></p>
                                <p><a href="#">Software Products</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Web 1.0时代 -->
        <section class="slide" id="slide-11">
            <div class="slide-content">
                <h2>Web 1.0时代 (1991-2004)</h2>
                <div class="web-era">
                    <div class="era-characteristics">
                        <h3>主要特征</h3>
                        <ul>
                            <li>静态网页为主</li>
                            <li>单向信息传播</li>
                            <li>用户只能浏览，不能交互</li>
                            <li>网站由专业人员维护</li>
                        </ul>
                    </div>
                    <div class="era-technologies">
                        <h3>主要技术</h3>
                        <ul>
                            <li>HTML 1.0 - 4.01</li>
                            <li>CSS 1.0 - 2.0</li>
                            <li>简单的JavaScript</li>
                            <li>CGI脚本</li>
                        </ul>
                    </div>
                    <div class="era-examples">
                        <h3>代表网站</h3>
                        <ul>
                            <li>Yahoo! (目录式搜索)</li>
                            <li>早期的新闻网站</li>
                            <li>公司官方网站</li>
                            <li>个人主页</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 浏览器大战第一轮 -->
        <section class="slide" id="slide-12">
            <div class="slide-content">
                <h2>第一次浏览器大战 (1995-2001)</h2>
                <div class="browser-war">
                    <div class="war-timeline">
                        <div class="war-phase">
                            <h3>Netscape的崛起</h3>
                            <ul>
                                <li>1994年：Netscape Navigator发布</li>
                                <li>市场份额一度达到80%</li>
                                <li>引入了JavaScript</li>
                                <li>支持插件和动画</li>
                            </ul>
                        </div>
                        <div class="war-phase">
                            <h3>微软的反击</h3>
                            <ul>
                                <li>1995年：Internet Explorer发布</li>
                                <li>与Windows系统捆绑销售</li>
                                <li>免费提供给用户</li>
                                <li>快速迭代更新</li>
                            </ul>
                        </div>
                        <div class="war-phase">
                            <h3>战争结果</h3>
                            <ul>
                                <li>IE最终获胜，市场份额超过90%</li>
                                <li>Netscape逐渐衰落</li>
                                <li>Web标准发展受阻</li>
                                <li>创新停滞</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- JavaScript的诞生 -->
        <section class="slide" id="slide-13">
            <div class="slide-content">
                <h2>JavaScript的诞生</h2>
                <div class="js-birth">
                    <div class="js-story">
                        <h3>创造背景</h3>
                        <ul>
                            <li><strong>时间：</strong>1995年5月</li>
                            <li><strong>创造者：</strong>Brendan Eich (Netscape)</li>
                            <li><strong>开发时间：</strong>仅用了10天</li>
                            <li><strong>目标：</strong>让网页具备交互能力</li>
                        </ul>
                    </div>
                    <div class="js-features">
                        <h3>早期特性</h3>
                        <ul>
                            <li>动态类型语言</li>
                            <li>基于原型的面向对象</li>
                            <li>函数式编程支持</li>
                            <li>事件驱动编程</li>
                        </ul>
                    </div>
                    <div class="js-impact">
                        <h3>历史影响</h3>
                        <ul>
                            <li>让静态网页变得动态</li>
                            <li>催生了Web应用的概念</li>
                            <li>成为Web开发的核心技术</li>
                            <li>影响了后续编程语言设计</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- CSS的发展 -->
        <section class="slide" id="slide-14">
            <div class="slide-content">
                <h2>CSS的发展历程</h2>
                <div class="css-evolution">
                    <div class="css-problem">
                        <h3>CSS出现前的问题</h3>
                        <div class="problem-example">
                            <pre><code>&lt;font color="red" size="4"&gt;
    &lt;b&gt;&lt;i&gt;标题文字&lt;/i&gt;&lt;/b&gt;
&lt;/font&gt;</code></pre>
                            <p>样式和内容混合，难以维护</p>
                        </div>
                    </div>
                    <div class="css-solution">
                        <h3>CSS的解决方案</h3>
                        <div class="solution-example">
                            <pre><code>/* CSS */
h1 {
    color: red;
    font-size: 24px;
    font-weight: bold;
}

/* HTML */
&lt;h1&gt;标题文字&lt;/h1&gt;</code></pre>
                            <p>样式和内容分离，易于维护</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Web 2.0时代 -->
        <section class="slide" id="slide-15">
            <div class="slide-content">
                <h2>Web 2.0时代 (2004-2010)</h2>
                <div class="web2-era">
                    <div class="web2-concept">
                        <h3>核心理念</h3>
                        <ul>
                            <li><strong>用户生成内容</strong> - UGC</li>
                            <li><strong>社交网络</strong> - 人与人的连接</li>
                            <li><strong>协作与分享</strong> - 集体智慧</li>
                            <li><strong>平台化思维</strong> - 开放API</li>
                        </ul>
                    </div>
                    <div class="web2-technologies">
                        <h3>关键技术</h3>
                        <ul>
                            <li><strong>AJAX</strong> - 异步数据交换</li>
                            <li><strong>RSS</strong> - 内容聚合</li>
                            <li><strong>REST API</strong> - 服务接口</li>
                            <li><strong>Web Services</strong> - 服务化架构</li>
                        </ul>
                    </div>
                    <div class="web2-examples">
                        <h3>代表产品</h3>
                        <ul>
                            <li>Facebook (2004)</li>
                            <li>YouTube (2005)</li>
                            <li>Twitter (2006)</li>
                            <li>Wikipedia (2001)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- AJAX技术革命 -->
        <section class="slide" id="slide-16">
            <div class="slide-content">
                <h2>AJAX技术革命</h2>
                <div class="ajax-revolution">
                    <div class="before-ajax">
                        <h3>AJAX之前</h3>
                        <div class="traditional-web">
                            <div class="step">1. 用户点击链接</div>
                            <div class="arrow">↓</div>
                            <div class="step">2. 浏览器发送请求</div>
                            <div class="arrow">↓</div>
                            <div class="step">3. 服务器返回完整页面</div>
                            <div class="arrow">↓</div>
                            <div class="step">4. 浏览器重新加载页面</div>
                        </div>
                        <p class="limitation">每次交互都需要刷新整个页面</p>
                    </div>
                    <div class="after-ajax">
                        <h3>AJAX之后</h3>
                        <div class="ajax-web">
                            <div class="step">1. 用户触发事件</div>
                            <div class="arrow">↓</div>
                            <div class="step">2. JavaScript发送异步请求</div>
                            <div class="arrow">↓</div>
                            <div class="step">3. 服务器返回数据</div>
                            <div class="arrow">↓</div>
                            <div class="step">4. 动态更新页面部分内容</div>
                        </div>
                        <p class="advantage">无需刷新页面，用户体验更流畅</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 移动互联网时代 -->
        <section class="slide" id="slide-17">
            <div class="slide-content">
                <h2>移动互联网时代 (2007-至今)</h2>
                <div class="mobile-era">
                    <div class="mobile-catalyst">
                        <h3>催化剂</h3>
                        <ul>
                            <li><strong>2007年：</strong>iPhone发布</li>
                            <li><strong>2008年：</strong>Android发布</li>
                            <li><strong>触摸屏普及</strong></li>
                            <li><strong>移动网络发展</strong> (3G/4G/5G)</li>
                        </ul>
                    </div>
                    <div class="mobile-challenges">
                        <h3>新挑战</h3>
                        <ul>
                            <li>屏幕尺寸多样化</li>
                            <li>触摸交互方式</li>
                            <li>网络带宽限制</li>
                            <li>电池续航考虑</li>
                        </ul>
                    </div>
                    <div class="mobile-solutions">
                        <h3>解决方案</h3>
                        <ul>
                            <li><strong>响应式设计</strong> - 适配不同屏幕</li>
                            <li><strong>移动优先</strong> - Mobile First</li>
                            <li><strong>PWA</strong> - 渐进式Web应用</li>
                            <li><strong>AMP</strong> - 加速移动页面</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTML5的革命 -->
        <section class="slide" id="slide-18">
            <div class="slide-content">
                <h2>HTML5的革命</h2>
                <div class="html5-revolution">
                    <div class="html5-goals">
                        <h3>设计目标</h3>
                        <ul>
                            <li>减少对插件的依赖</li>
                            <li>增强语义化</li>
                            <li>提供更好的多媒体支持</li>
                            <li>改善Web应用开发体验</li>
                        </ul>
                    </div>
                    <div class="html5-features">
                        <h3>主要特性</h3>
                        <div class="feature-grid">
                            <div class="feature-item">
                                <h4>语义化标签</h4>
                                <code>&lt;header&gt;, &lt;nav&gt;, &lt;article&gt;</code>
                            </div>
                            <div class="feature-item">
                                <h4>多媒体支持</h4>
                                <code>&lt;video&gt;, &lt;audio&gt;, &lt;canvas&gt;</code>
                            </div>
                            <div class="feature-item">
                                <h4>表单增强</h4>
                                <code>type="email", type="date"</code>
                            </div>
                            <div class="feature-item">
                                <h4>本地存储</h4>
                                <code>localStorage, sessionStorage</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 现代Web开发时代 -->
        <section class="slide" id="slide-19">
            <div class="slide-content">
                <h2>现代Web开发时代 (2010-至今)</h2>
                <div class="modern-web">
                    <div class="frontend-frameworks">
                        <h3>前端框架兴起</h3>
                        <div class="framework-timeline">
                            <div class="framework-item">
                                <span class="year">2010</span>
                                <span class="name">AngularJS</span>
                            </div>
                            <div class="framework-item">
                                <span class="year">2013</span>
                                <span class="name">React</span>
                            </div>
                            <div class="framework-item">
                                <span class="year">2014</span>
                                <span class="name">Vue.js</span>
                            </div>
                            <div class="framework-item">
                                <span class="year">2016</span>
                                <span class="name">Angular 2+</span>
                            </div>
                        </div>
                    </div>
                    <div class="development-trends">
                        <h3>发展趋势</h3>
                        <ul>
                            <li><strong>组件化开发</strong> - 可复用的UI组件</li>
                            <li><strong>单页应用</strong> - SPA架构</li>
                            <li><strong>前后端分离</strong> - API驱动开发</li>
                            <li><strong>构建工具链</strong> - Webpack, Vite等</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Web技术栈演进 -->
        <section class="slide" id="slide-20">
            <div class="slide-content">
                <h2>Web技术栈的演进</h2>
                <div class="tech-stack-evolution">
                    <div class="stack-era">
                        <h3>早期 (1990s)</h3>
                        <div class="stack-layers">
                            <div class="layer">HTML</div>
                        </div>
                    </div>
                    <div class="stack-era">
                        <h3>发展期 (2000s)</h3>
                        <div class="stack-layers">
                            <div class="layer">JavaScript</div>
                            <div class="layer">CSS</div>
                            <div class="layer">HTML</div>
                        </div>
                    </div>
                    <div class="stack-era">
                        <h3>现代 (2010s+)</h3>
                        <div class="stack-layers">
                            <div class="layer">框架/库 (React, Vue)</div>
                            <div class="layer">构建工具 (Webpack, Vite)</div>
                            <div class="layer">预处理器 (Sass, TypeScript)</div>
                            <div class="layer">JavaScript</div>
                            <div class="layer">CSS3</div>
                            <div class="layer">HTML5</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button id="prevBtn" onclick="changeSlide(-1)">← 上一页</button>
        <span id="slideCounter">1 / 8</span>
        <button id="nextBtn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress" id="progress"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
