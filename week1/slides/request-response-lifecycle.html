<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP请求-响应生命周期 - SVG动画演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .lifecycle-svg {
            width: 100%;
            height: auto;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .controls {
            text-align: center;
            margin: 2rem 0;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 1rem;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .step-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .step-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid var(--step-color);
            transition: all 0.3s ease;
            opacity: 0.3;
        }

        .step-card.active {
            opacity: 1;
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .step-card h3 {
            color: var(--step-color);
            margin-bottom: 1rem;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--step-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .step-duration {
            background: var(--step-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .step-details-list {
            list-style: none;
            padding: 0;
        }

        .step-details-list li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .step-details-list li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--step-color);
            font-weight: bold;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }

        .performance-tips {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 2rem;
            margin-top: 3rem;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .tip-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
        }

        /* 步骤颜色主题 */
        .step-1 { --step-color: #e74c3c; }
        .step-2 { --step-color: #f39c12; }
        .step-3 { --step-color: #3498db; }
        .step-4 { --step-color: #9b59b6; }
        .step-5 { --step-color: #27ae60; }

        /* SVG动画样式 */
        .data-packet {
            opacity: 0;
        }

        .data-packet.animate {
            opacity: 1;
            animation: movePacket 2s ease-in-out;
        }

        @keyframes movePacket {
            0% { transform: translateX(0); }
            50% { transform: translateX(400px); }
            100% { transform: translateX(800px); }
        }

        .processing-indicator {
            opacity: 0;
        }

        .processing-indicator.animate {
            opacity: 1;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .response-packet {
            opacity: 0;
        }

        .response-packet.animate {
            opacity: 1;
            animation: moveResponse 2s ease-in-out;
        }

        @keyframes moveResponse {
            0% { transform: translateX(800px); }
            50% { transform: translateX(400px); }
            100% { transform: translateX(0); }
        }

        .render-effect {
            opacity: 0;
        }

        .render-effect.animate {
            opacity: 1;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .step-details {
                grid-template-columns: 1fr;
            }
            
            .control-btn {
                padding: 0.8rem 1.5rem;
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTTP请求-响应生命周期</h1>
        <p class="subtitle">从用户点击到页面渲染的完整过程可视化演示</p>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="control-btn" id="startBtn">🚀 开始演示</button>
            <button class="control-btn" id="resetBtn">🔄 重置</button>
            <button class="control-btn" id="pauseBtn" disabled>⏸️ 暂停</button>
        </div>

        <!-- SVG生命周期图 -->
        <svg class="lifecycle-svg" viewBox="0 0 1200 500" xmlns="http://www.w3.org/2000/svg">
            <!-- 背景 -->
            <defs>
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f8f9fa"/>
                    <stop offset="100%" style="stop-color:#ffffff"/>
                </linearGradient>
                
                <!-- 数据包图标 -->
                <g id="packet-icon">
                    <rect width="30" height="20" rx="3" fill="#3498db"/>
                    <rect x="3" y="3" width="24" height="3" fill="white"/>
                    <rect x="3" y="8" width="18" height="3" fill="white"/>
                    <rect x="3" y="13" width="21" height="3" fill="white"/>
                </g>
                
                <!-- 服务器图标 -->
                <g id="server-icon">
                    <rect width="60" height="80" rx="5" fill="#34495e"/>
                    <rect x="5" y="10" width="50" height="8" rx="2" fill="#2ecc71"/>
                    <rect x="5" y="25" width="50" height="8" rx="2" fill="#e74c3c"/>
                    <rect x="5" y="40" width="50" height="8" rx="2" fill="#f39c12"/>
                    <circle cx="15" cy="60" r="3" fill="#3498db"/>
                    <circle cx="25" cy="60" r="3" fill="#9b59b6"/>
                    <circle cx="35" cy="60" r="3" fill="#1abc9c"/>
                    <circle cx="45" cy="60" r="3" fill="#e67e22"/>
                </g>
                
                <!-- 浏览器图标 -->
                <g id="browser-icon">
                    <rect width="80" height="60" rx="5" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
                    <rect x="5" y="5" width="70" height="12" rx="2" fill="#3498db"/>
                    <circle cx="12" cy="11" r="2" fill="white"/>
                    <circle cx="20" cy="11" r="2" fill="white"/>
                    <circle cx="28" cy="11" r="2" fill="white"/>
                    <rect x="10" y="25" width="60" height="25" rx="2" fill="white"/>
                </g>
            </defs>
            
            <rect width="1200" height="500" fill="url(#bgGradient)"/>
            
            <!-- 标题 -->
            <text x="600" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                HTTP请求-响应生命周期动画演示
            </text>
            
            <!-- 客户端（浏览器） -->
            <g id="client" transform="translate(50, 200)">
                <use href="#browser-icon"/>
                <text x="40" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">客户端</text>
                <text x="40" y="95" text-anchor="middle" font-size="12" fill="#666">浏览器</text>
            </g>
            
            <!-- 服务器 -->
            <g id="server" transform="translate(1070, 180)">
                <use href="#server-icon"/>
                <text x="30" y="100" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">服务器</text>
                <text x="30" y="115" text-anchor="middle" font-size="12" fill="#666">Web Server</text>
            </g>
            
            <!-- 网络连接线 -->
            <line x1="150" y1="230" x2="1050" y2="230" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="600" y="220" text-anchor="middle" font-size="12" fill="#666">网络传输</text>
            
            <!-- 步骤指示器 -->
            <g id="step-indicators">
                <!-- 步骤1：构造请求 -->
                <g id="step1-indicator" class="step-indicator">
                    <circle cx="200" cy="150" r="25" fill="#e74c3c" opacity="0.3"/>
                    <text x="200" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="white">1</text>
                    <text x="200" y="130" text-anchor="middle" font-size="12" fill="#e74c3c">构造请求</text>
                </g>
                
                <!-- 步骤2：传输 -->
                <g id="step2-indicator" class="step-indicator">
                    <circle cx="400" cy="150" r="25" fill="#f39c12" opacity="0.3"/>
                    <text x="400" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="white">2</text>
                    <text x="400" y="130" text-anchor="middle" font-size="12" fill="#f39c12">网络传输</text>
                </g>
                
                <!-- 步骤3：服务器处理 -->
                <g id="step3-indicator" class="step-indicator">
                    <circle cx="600" cy="150" r="25" fill="#3498db" opacity="0.3"/>
                    <text x="600" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="white">3</text>
                    <text x="600" y="130" text-anchor="middle" font-size="12" fill="#3498db">服务器处理</text>
                </g>
                
                <!-- 步骤4：返回响应 -->
                <g id="step4-indicator" class="step-indicator">
                    <circle cx="800" cy="150" r="25" fill="#9b59b6" opacity="0.3"/>
                    <text x="800" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="white">4</text>
                    <text x="800" y="130" text-anchor="middle" font-size="12" fill="#9b59b6">返回响应</text>
                </g>
                
                <!-- 步骤5：渲染页面 -->
                <g id="step5-indicator" class="step-indicator">
                    <circle cx="1000" cy="150" r="25" fill="#27ae60" opacity="0.3"/>
                    <text x="1000" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="white">5</text>
                    <text x="1000" y="130" text-anchor="middle" font-size="12" fill="#27ae60">页面渲染</text>
                </g>
            </g>
            
            <!-- 动画元素 -->
            <!-- 请求数据包 -->
            <g id="request-packet" class="data-packet" transform="translate(150, 210)">
                <use href="#packet-icon"/>
                <text x="15" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">HTTP Request</text>
            </g>
            
            <!-- 服务器处理指示器 -->
            <g id="processing" class="processing-indicator" transform="translate(1070, 140)">
                <circle cx="30" cy="30" r="20" fill="#3498db" opacity="0.7"/>
                <text x="30" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="white">处理中</text>
            </g>
            
            <!-- 响应数据包 -->
            <g id="response-packet" class="response-packet" transform="translate(1020, 250)">
                <use href="#packet-icon"/>
                <text x="15" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">HTTP Response</text>
            </g>
            
            <!-- 渲染效果 -->
            <g id="render-effect" class="render-effect" transform="translate(60, 280)">
                <rect width="60" height="40" rx="3" fill="#2ecc71" opacity="0.8"/>
                <text x="30" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">页面渲染</text>
            </g>
            
            <!-- 时间轴 -->
            <g id="timeline" transform="translate(50, 400)">
                <line x1="0" y1="0" x2="1100" y2="0" stroke="#667eea" stroke-width="3"/>
                <text x="0" y="20" font-size="12" fill="#666">0ms</text>
                <text x="275" y="20" font-size="12" fill="#666">100ms</text>
                <text x="550" y="20" font-size="12" fill="#666">200ms</text>
                <text x="825" y="20" font-size="12" fill="#666">300ms</text>
                <text x="1100" y="20" font-size="12" fill="#666">400ms</text>
            </g>
            
            <!-- 性能指标 -->
            <g id="metrics" transform="translate(50, 450)">
                <text x="0" y="0" font-size="12" fill="#666">DNS解析: 20ms</text>
                <text x="150" y="0" font-size="12" fill="#666">TCP连接: 30ms</text>
                <text x="300" y="0" font-size="12" fill="#666">请求发送: 10ms</text>
                <text x="450" y="0" font-size="12" fill="#666">服务器处理: 100ms</text>
                <text x="600" y="0" font-size="12" fill="#666">响应接收: 50ms</text>
                <text x="800" y="0" font-size="12" fill="#666">DOM解析: 80ms</text>
                <text x="950" y="0" font-size="12" fill="#666">渲染: 60ms</text>
            </g>
        </svg>

        <!-- 步骤详细说明 -->
        <div class="step-details">
            <div class="step-card step-1" data-step="1">
                <h3>
                    <div class="step-number">1</div>
                    构造请求
                </h3>
                <div class="step-duration">~50ms</div>
                <p><strong>浏览器准备HTTP请求</strong></p>
                <ul class="step-details-list">
                    <li>解析URL地址</li>
                    <li>DNS域名解析</li>
                    <li>建立TCP连接</li>
                    <li>构造HTTP请求头</li>
                    <li>准备请求数据</li>
                </ul>
                <div class="code-example">
GET /api/users HTTP/1.1
Host: www.example.com
User-Agent: Mozilla/5.0
Accept: application/json
                </div>
            </div>

            <div class="step-card step-2" data-step="2">
                <h3>
                    <div class="step-number">2</div>
                    网络传输
                </h3>
                <div class="step-duration">~30ms</div>
                <p><strong>请求通过网络发送到服务器</strong></p>
                <ul class="step-details-list">
                    <li>数据包封装</li>
                    <li>路由选择</li>
                    <li>网络传输</li>
                    <li>到达目标服务器</li>
                    <li>TCP确认机制</li>
                </ul>
                <div class="code-example">
# 网络路径示例
客户端 → 路由器 → ISP → 互联网 → 服务器
延迟: 5ms + 10ms + 15ms = 30ms
                </div>
            </div>

            <div class="step-card step-3" data-step="3">
                <h3>
                    <div class="step-number">3</div>
                    服务器处理
                </h3>
                <div class="step-duration">~100ms</div>
                <p><strong>服务器接收请求并处理业务逻辑</strong></p>
                <ul class="step-details-list">
                    <li>解析HTTP请求</li>
                    <li>路由匹配</li>
                    <li>执行业务逻辑</li>
                    <li>数据库查询</li>
                    <li>生成响应数据</li>
                </ul>
                <div class="code-example">
// 服务器处理示例
app.get('/api/users', async (req, res) => {
  const users = await db.users.findAll();
  res.json(users);
});
                </div>
            </div>

            <div class="step-card step-4" data-step="4">
                <h3>
                    <div class="step-number">4</div>
                    返回响应
                </h3>
                <div class="step-duration">~50ms</div>
                <p><strong>服务器发送HTTP响应给客户端</strong></p>
                <ul class="step-details-list">
                    <li>构造响应头</li>
                    <li>设置状态码</li>
                    <li>添加响应数据</li>
                    <li>网络传输回客户端</li>
                    <li>TCP连接管理</li>
                </ul>
                <div class="code-example">
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 1024

{"users": [...]}
                </div>
            </div>

            <div class="step-card step-5" data-step="5">
                <h3>
                    <div class="step-number">5</div>
                    页面渲染
                </h3>
                <div class="step-duration">~140ms</div>
                <p><strong>浏览器接收响应并渲染页面</strong></p>
                <ul class="step-details-list">
                    <li>接收响应数据</li>
                    <li>解析HTML/CSS/JS</li>
                    <li>构建DOM树</li>
                    <li>样式计算</li>
                    <li>页面渲染显示</li>
                </ul>
                <div class="code-example">
// 渲染过程
HTML解析 → DOM构建 → CSS解析 → 
样式计算 → 布局 → 绘制 → 合成
                </div>
            </div>
        </div>

        <!-- 性能优化建议 -->
        <div class="performance-tips">
            <h3 style="color: #2c3e50; margin-bottom: 1rem;">⚡ 性能优化建议</h3>
            <div class="tips-grid">
                <div class="tip-item">
                    <h4 style="color: #27ae60; margin-bottom: 0.5rem;">🚀 减少请求时间</h4>
                    <ul style="font-size: 0.9rem;">
                        <li>使用CDN加速</li>
                        <li>DNS预解析</li>
                        <li>HTTP/2多路复用</li>
                        <li>Keep-Alive连接</li>
                    </ul>
                </div>
                <div class="tip-item">
                    <h4 style="color: #27ae60; margin-bottom: 0.5rem;">⚙️ 服务器优化</h4>
                    <ul style="font-size: 0.9rem;">
                        <li>数据库索引优化</li>
                        <li>缓存策略</li>
                        <li>负载均衡</li>
                        <li>异步处理</li>
                    </ul>
                </div>
                <div class="tip-item">
                    <h4 style="color: #27ae60; margin-bottom: 0.5rem;">🎨 渲染优化</h4>
                    <ul style="font-size: 0.9rem;">
                        <li>资源压缩</li>
                        <li>关键路径优化</li>
                        <li>懒加载</li>
                        <li>代码分割</li>
                    </ul>
                </div>
                <div class="tip-item">
                    <h4 style="color: #27ae60; margin-bottom: 0.5rem;">📊 监控分析</h4>
                    <ul style="font-size: 0.9rem;">
                        <li>性能监控</li>
                        <li>用户体验分析</li>
                        <li>错误追踪</li>
                        <li>A/B测试</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        class LifecycleAnimation {
            constructor() {
                this.currentStep = 0;
                this.isPlaying = false;
                this.isPaused = false;
                this.animationTimeout = null;
                
                this.steps = [
                    { duration: 1000, element: 'step1-indicator' },
                    { duration: 2000, element: 'step2-indicator' },
                    { duration: 2000, element: 'step3-indicator' },
                    { duration: 2000, element: 'step4-indicator' },
                    { duration: 1500, element: 'step5-indicator' }
                ];
                
                this.initializeElements();
                this.bindEvents();
            }
            
            initializeElements() {
                this.startBtn = document.getElementById('startBtn');
                this.resetBtn = document.getElementById('resetBtn');
                this.pauseBtn = document.getElementById('pauseBtn');
                this.stepCards = document.querySelectorAll('.step-card');
                this.stepIndicators = document.querySelectorAll('.step-indicator circle');
            }
            
            bindEvents() {
                this.startBtn.addEventListener('click', () => this.start());
                this.resetBtn.addEventListener('click', () => this.reset());
                this.pauseBtn.addEventListener('click', () => this.pause());
            }
            
            start() {
                if (this.isPaused) {
                    this.resume();
                    return;
                }
                
                this.isPlaying = true;
                this.startBtn.disabled = true;
                this.pauseBtn.disabled = false;
                this.currentStep = 0;
                
                this.playStep(0);
            }
            
            playStep(stepIndex) {
                if (!this.isPlaying || stepIndex >= this.steps.length) {
                    this.complete();
                    return;
                }
                
                // 激活当前步骤
                this.activateStep(stepIndex);
                
                // 播放对应动画
                this.playAnimation(stepIndex);
                
                // 设置下一步
                this.animationTimeout = setTimeout(() => {
                    if (this.isPlaying) {
                        this.playStep(stepIndex + 1);
                    }
                }, this.steps[stepIndex].duration);
            }
            
            activateStep(stepIndex) {
                // 重置所有步骤
                this.stepCards.forEach(card => card.classList.remove('active'));
                this.stepIndicators.forEach(indicator => {
                    indicator.style.opacity = '0.3';
                });
                
                // 激活当前步骤
                const currentCard = document.querySelector(`[data-step="${stepIndex + 1}"]`);
                if (currentCard) {
                    currentCard.classList.add('active');
                    currentCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                
                const currentIndicator = document.querySelector(`#step${stepIndex + 1}-indicator circle`);
                if (currentIndicator) {
                    currentIndicator.style.opacity = '1';
                }
            }
            
            playAnimation(stepIndex) {
                const animations = {
                    0: () => this.animateRequest(),
                    1: () => this.animateTransmission(),
                    2: () => this.animateProcessing(),
                    3: () => this.animateResponse(),
                    4: () => this.animateRendering()
                };
                
                if (animations[stepIndex]) {
                    animations[stepIndex]();
                }
            }
            
            animateRequest() {
                // 高亮浏览器
                const client = document.getElementById('client');
                client.style.filter = 'drop-shadow(0 0 20px #e74c3c)';
                setTimeout(() => {
                    client.style.filter = '';
                }, 800);
            }
            
            animateTransmission() {
                const packet = document.getElementById('request-packet');
                packet.classList.add('animate');
                setTimeout(() => {
                    packet.classList.remove('animate');
                }, 2000);
            }
            
            animateProcessing() {
                const server = document.getElementById('server');
                const processing = document.getElementById('processing');
                
                server.style.filter = 'drop-shadow(0 0 20px #3498db)';
                processing.classList.add('animate');
                
                setTimeout(() => {
                    server.style.filter = '';
                    processing.classList.remove('animate');
                }, 1800);
            }
            
            animateResponse() {
                const responsePacket = document.getElementById('response-packet');
                responsePacket.classList.add('animate');
                setTimeout(() => {
                    responsePacket.classList.remove('animate');
                }, 2000);
            }
            
            animateRendering() {
                const client = document.getElementById('client');
                const renderEffect = document.getElementById('render-effect');
                
                client.style.filter = 'drop-shadow(0 0 20px #27ae60)';
                renderEffect.classList.add('animate');
                
                setTimeout(() => {
                    client.style.filter = '';
                }, 1200);
            }
            
            pause() {
                this.isPaused = true;
                this.isPlaying = false;
                this.pauseBtn.textContent = '▶️ 继续';
                this.pauseBtn.onclick = () => this.resume();
                
                if (this.animationTimeout) {
                    clearTimeout(this.animationTimeout);
                }
            }
            
            resume() {
                this.isPaused = false;
                this.isPlaying = true;
                this.pauseBtn.textContent = '⏸️ 暂停';
                this.pauseBtn.onclick = () => this.pause();
                
                this.playStep(this.currentStep);
            }
            
            reset() {
                this.isPlaying = false;
                this.isPaused = false;
                this.currentStep = 0;
                
                this.startBtn.disabled = false;
                this.pauseBtn.disabled = true;
                this.pauseBtn.textContent = '⏸️ 暂停';
                
                if (this.animationTimeout) {
                    clearTimeout(this.animationTimeout);
                }
                
                // 重置所有状态
                this.stepCards.forEach(card => card.classList.remove('active'));
                this.stepIndicators.forEach(indicator => {
                    indicator.style.opacity = '0.3';
                });
                
                // 重置动画元素
                document.querySelectorAll('.data-packet, .processing-indicator, .response-packet, .render-effect')
                    .forEach(el => el.classList.remove('animate'));
                
                // 重置滤镜效果
                document.getElementById('client').style.filter = '';
                document.getElementById('server').style.filter = '';
            }
            
            complete() {
                this.isPlaying = false;
                this.startBtn.disabled = false;
                this.pauseBtn.disabled = true;
                
                // 显示完成效果
                setTimeout(() => {
                    alert('🎉 HTTP请求-响应生命周期演示完成！\n\n总耗时约: 370ms\n包含: DNS解析、TCP连接、请求发送、服务器处理、响应接收、页面渲染');
                }, 500);
            }
        }
        
        // 初始化动画
        document.addEventListener('DOMContentLoaded', () => {
            new LifecycleAnimation();
        });
    </script>
</body>
</html>
