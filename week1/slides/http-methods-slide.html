<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP方法：幂等性与安全性</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        .main-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .concepts-section {
            display: flex;
            justify-content: space-around;
            width: 100%;
            max-width: 1400px;
            margin-bottom: 3rem;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .concept-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            width: 45%;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .concept-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .concept-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .safe-icon {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .idempotent-icon {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .concept-description {
            font-size: 1.5rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .methods-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            width: 100%;
            max-width: 1400px;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out 1s both;
        }

        .method-category {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            border: 3px solid;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .method-category::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .method-category:hover::before {
            left: 100%;
        }

        .method-category:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .both-safe-idempotent {
            border-color: #9b59b6;
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.2));
        }

        .only-idempotent {
            border-color: #3498db;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
        }

        .neither {
            border-color: #e74c3c;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
        }

        .category-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .category-subtitle {
            font-size: 1.3rem;
            opacity: 0.8;
            margin-bottom: 1.5rem;
        }

        .methods-list {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .method-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-size: 1.4rem;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .method-tag:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .category-description {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .summary {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-out 1.5s both;
        }

        .summary-text {
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .highlight {
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        /* 动画定义 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-title { font-size: 3rem; }
            .subtitle { font-size: 1.5rem; }
            .concept-title { font-size: 2rem; }
            .concept-description { font-size: 1.2rem; }
            .methods-grid { grid-template-columns: 1fr; gap: 1.5rem; }
            .category-title { font-size: 1.8rem; }
            .summary-text { font-size: 1.5rem; }
        }

        @media (max-width: 768px) {
            .concepts-section { flex-direction: column; gap: 2rem; }
            .concept-card { width: 100%; }
            .main-title { font-size: 2.5rem; }
            .methods-list { flex-direction: column; align-items: center; }
        }

        /* 交互提示 */
        .interaction-hint {
            position: absolute;
            top: 2rem;
            right: 2rem;
            font-size: 1.2rem;
            opacity: 0.7;
            animation: fadeIn 2s ease-out 2s both;
        }

        /* 特殊效果 */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
            animation: sparkle 3s infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        .method-category {
            position: relative;
        }

        .method-category .sparkle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
        .method-category .sparkle:nth-child(2) { top: 30%; right: 25%; animation-delay: 1s; }
        .method-category .sparkle:nth-child(3) { bottom: 25%; left: 30%; animation-delay: 2s; }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 交互提示 -->
        <div class="interaction-hint">
            💡 点击方法分类卡片查看详细信息
        </div>

        <!-- 标题区域 -->
        <div class="header">
            <h1 class="main-title">HTTP方法的幂等性与安全性</h1>
            <p class="subtitle">Idempotent & Safe HTTP Methods</p>
        </div>

        <!-- 概念定义区域 -->
        <div class="concepts-section">
            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon safe-icon">🔒</div>
                    安全性 (Safe)
                </div>
                <div class="concept-description">
                    不会修改服务器状态的HTTP方法<br>
                    只进行读取操作，不产生副作用<br>
                    可以被安全地缓存和重复调用
                </div>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon idempotent-icon">🔄</div>
                    幂等性 (Idempotent)
                </div>
                <div class="concept-description">
                    多次执行与单次执行效果相同<br>
                    重复调用不会产生额外的副作用<br>
                    网络故障时可以安全地重试
                </div>
            </div>
        </div>

        <!-- HTTP方法分类 -->
        <div class="methods-grid">
            <div class="method-category both-safe-idempotent" onclick="showDetails('both')">
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="category-title">既安全又幂等</div>
                <div class="category-subtitle">Safe + Idempotent</div>
                <div class="methods-list">
                    <span class="method-tag">GET</span>
                    <span class="method-tag">HEAD</span>
                    <span class="method-tag">OPTIONS</span>
                </div>
                <div class="category-description">
                    只读操作，可重复执行<br>
                    最安全的HTTP方法
                </div>
            </div>

            <div class="method-category only-idempotent" onclick="showDetails('idempotent')">
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="category-title">仅幂等</div>
                <div class="category-subtitle">Idempotent Only</div>
                <div class="methods-list">
                    <span class="method-tag">PUT</span>
                    <span class="method-tag">DELETE</span>
                </div>
                <div class="category-description">
                    会修改状态，但可重复执行<br>
                    多次调用结果一致
                </div>
            </div>

            <div class="method-category neither" onclick="showDetails('neither')">
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="sparkle"></div>
                <div class="category-title">都不是</div>
                <div class="category-subtitle">Neither Safe nor Idempotent</div>
                <div class="methods-list">
                    <span class="method-tag">POST</span>
                </div>
                <div class="category-description">
                    会修改状态，重复执行有副作用<br>
                    需要防止重复提交
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary">
            <div class="summary-text">
                💡 记忆要点：<span class="highlight">GET/HEAD/OPTIONS</span> 安全且幂等，
                <span class="highlight">PUT/DELETE</span> 仅幂等，<span class="highlight">POST</span> 都不是
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
        <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 3rem; border-radius: 20px; max-width: 800px; width: 90%; color: white; position: relative;">
            <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 2rem; cursor: pointer;">×</button>
            <div id="modal-content"></div>
        </div>
    </div>

    <script>
        // 详细信息数据
        const detailsData = {
            both: {
                title: '既安全又幂等的HTTP方法',
                icon: '🔒🔄',
                methods: [
                    {
                        name: 'GET',
                        description: '获取资源信息，不修改服务器状态',
                        example: 'GET /api/users/123 - 获取用户信息',
                        characteristics: ['可缓存', '可重复调用', '搜索引擎友好']
                    },
                    {
                        name: 'HEAD',
                        description: '类似GET但只返回响应头',
                        example: 'HEAD /api/users/123 - 检查用户是否存在',
                        characteristics: ['检查资源状态', '获取元信息', '缓存验证']
                    },
                    {
                        name: 'OPTIONS',
                        description: '获取服务器支持的HTTP方法',
                        example: 'OPTIONS /api/users - 查看支持的操作',
                        characteristics: ['CORS预检', '能力发现', 'API文档']
                    }
                ]
            },
            idempotent: {
                title: '仅幂等的HTTP方法',
                icon: '🔄',
                methods: [
                    {
                        name: 'PUT',
                        description: '创建或完全更新资源',
                        example: 'PUT /api/users/123 - 更新用户信息',
                        characteristics: ['完整替换', '多次调用结果相同', '可重试']
                    },
                    {
                        name: 'DELETE',
                        description: '删除指定资源',
                        example: 'DELETE /api/users/123 - 删除用户',
                        characteristics: ['删除操作', '重复删除无影响', '可重试']
                    }
                ]
            },
            neither: {
                title: '既不安全也不幂等的HTTP方法',
                icon: '⚠️',
                methods: [
                    {
                        name: 'POST',
                        description: '创建新资源或提交数据',
                        example: 'POST /api/users - 创建新用户',
                        characteristics: ['创建操作', '每次调用可能不同结果', '需防重复提交']
                    }
                ]
            }
        };

        function showDetails(category) {
            const data = detailsData[category];
            const modal = document.getElementById('modal');
            const content = document.getElementById('modal-content');
            
            let html = `
                <h2 style="font-size: 2.5rem; margin-bottom: 2rem; text-align: center;">
                    ${data.icon} ${data.title}
                </h2>
            `;
            
            data.methods.forEach(method => {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 2rem; color: #f1c40f; margin-bottom: 1rem;">${method.name}</h3>
                        <p style="font-size: 1.4rem; margin-bottom: 1rem;">${method.description}</p>
                        <p style="font-size: 1.2rem; font-style: italic; margin-bottom: 1rem; opacity: 0.8;">
                            示例: ${method.example}
                        </p>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            ${method.characteristics.map(char => 
                                `<span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; font-size: 1rem;">
                                    ${char}
                                </span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            });
            
            content.innerHTML = html;
            modal.style.display = 'flex';
            
            // 添加动画效果
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
            
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
            
            // 数字键快速查看
            const categoryMap = {
                '1': 'both',
                '2': 'idempotent', 
                '3': 'neither'
            };
            
            if (categoryMap[e.key]) {
                showDetails(categoryMap[e.key]);
            }
        });

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为方法标签添加随机延迟动画
            const methodTags = document.querySelectorAll('.method-tag');
            methodTags.forEach((tag, index) => {
                tag.style.animationDelay = `${2 + index * 0.2}s`;
                tag.classList.add('fadeInUp');
            });
            
            // 添加鼠标跟随效果
            document.addEventListener('mousemove', function(e) {
                const sparkles = document.querySelectorAll('.sparkle');
                sparkles.forEach((sparkle, index) => {
                    const delay = index * 100;
                    setTimeout(() => {
                        const rect = sparkle.closest('.method-category').getBoundingClientRect();
                        const x = ((e.clientX - rect.left) / rect.width) * 100;
                        const y = ((e.clientY - rect.top) / rect.height) * 100;
                        
                        if (x >= 0 && x <= 100 && y >= 0 && y <= 100) {
                            sparkle.style.opacity = '1';
                        }
                    }, delay);
                });
            });
        });
    </script>
</body>
</html>
