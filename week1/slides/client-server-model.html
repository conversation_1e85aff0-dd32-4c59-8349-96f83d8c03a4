<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端-服务器模型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fullscreen-svg {
            width: 100vw;
            height: 100vh;
            display: block;
        }

        /* 动画样式 */
        .request-arrow {
            stroke-dasharray: 300;
            stroke-dashoffset: 300;
            animation: drawRequest 2s ease-in-out forwards;
        }

        @keyframes drawRequest {
            to { stroke-dashoffset: 0; }
        }

        .response-arrow {
            stroke-dasharray: 300;
            stroke-dashoffset: 300;
            animation: drawResponse 2s ease-in-out forwards;
        }

        @keyframes drawResponse {
            to { stroke-dashoffset: 0; }
        }

        .data-packet {
            opacity: 0;
            animation: packetMove 3s ease-in-out forwards;
        }

        @keyframes packetMove {
            0% { opacity: 0; transform: translateX(0); }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; transform: translateX(600px); }
        }

        .response-packet {
            opacity: 0;
            animation: responseMove 3s ease-in-out forwards;
        }

        @keyframes responseMove {
            0% { opacity: 0; transform: translateX(600px); }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; transform: translateX(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.05); opacity: 1; }
        }

        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease-in forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 1s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .zoom-in {
            transform: scale(0);
            opacity: 0;
            animation: zoomIn 0.8s ease-out forwards;
        }

        @keyframes zoomIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .glow {
            filter: drop-shadow(0 0 15px rgba(255,255,255,0.8));
        }

        .interactive:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 20px rgba(52, 152, 219, 0.8));
            cursor: pointer;
        }

        .server-processing {
            animation: serverWork 2s ease-in-out infinite;
        }

        @keyframes serverWork {
            0%, 100% { fill: #34495e; }
            50% { fill: #3498db; }
        }
    </style>
</head>
<body>
    <svg class="fullscreen-svg" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
        <!-- 背景渐变定义 -->
        <defs>
            <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#2c3e50"/>
                <stop offset="50%" style="stop-color:#34495e"/>
                <stop offset="100%" style="stop-color:#2c3e50"/>
            </linearGradient>
            
            <linearGradient id="clientGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3498db"/>
                <stop offset="100%" style="stop-color:#2980b9"/>
            </linearGradient>
            
            <linearGradient id="serverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#e74c3c"/>
                <stop offset="100%" style="stop-color:#c0392b"/>
            </linearGradient>
            
            <!-- 客户端图标 -->
            <g id="client-icon">
                <rect x="0" y="0" width="200" height="150" rx="15" fill="url(#clientGradient)" stroke="#2980b9" stroke-width="4"/>
                <!-- 屏幕 -->
                <rect x="15" y="15" width="170" height="100" rx="8" fill="#ecf0f1"/>
                <!-- 浏览器窗口 -->
                <rect x="25" y="25" width="150" height="80" rx="5" fill="white" stroke="#bdc3c7" stroke-width="2"/>
                <!-- 地址栏 -->
                <rect x="30" y="30" width="140" height="15" rx="3" fill="#3498db"/>
                <!-- 按钮 -->
                <circle cx="40" cy="37.5" r="3" fill="white"/>
                <circle cx="50" cy="37.5" r="3" fill="white"/>
                <circle cx="60" cy="37.5" r="3" fill="white"/>
                <!-- 内容区域 -->
                <rect x="35" y="50" width="120" height="8" rx="2" fill="#bdc3c7"/>
                <rect x="35" y="65" width="100" height="8" rx="2" fill="#bdc3c7"/>
                <rect x="35" y="80" width="130" height="8" rx="2" fill="#bdc3c7"/>
                <!-- 键盘 -->
                <rect x="40" y="125" width="120" height="20" rx="5" fill="#95a5a6"/>
            </g>
            
            <!-- 服务器图标 -->
            <g id="server-icon">
                <rect x="0" y="0" width="180" height="200" rx="15" fill="url(#serverGradient)" stroke="#c0392b" stroke-width="4"/>
                <!-- 服务器层1 -->
                <rect x="15" y="20" width="150" height="35" rx="5" fill="#34495e"/>
                <rect x="25" y="30" width="130" height="15" rx="3" fill="#2ecc71"/>
                <circle cx="140" cy="37.5" r="4" fill="#f39c12"/>
                <circle cx="155" cy="37.5" r="4" fill="#e74c3c"/>
                <!-- 服务器层2 -->
                <rect x="15" y="65" width="150" height="35" rx="5" fill="#34495e"/>
                <rect x="25" y="75" width="130" height="15" rx="3" fill="#3498db"/>
                <circle cx="140" cy="82.5" r="4" fill="#f39c12"/>
                <circle cx="155" cy="82.5" r="4" fill="#2ecc71"/>
                <!-- 服务器层3 -->
                <rect x="15" y="110" width="150" height="35" rx="5" fill="#34495e"/>
                <rect x="25" y="120" width="130" height="15" rx="3" fill="#9b59b6"/>
                <circle cx="140" cy="127.5" r="4" fill="#f39c12"/>
                <circle cx="155" cy="127.5" r="4" fill="#2ecc71"/>
                <!-- 底部端口 -->
                <rect x="30" y="160" width="20" height="15" rx="3" fill="#f39c12"/>
                <rect x="60" y="160" width="20" height="15" rx="3" fill="#2ecc71"/>
                <rect x="90" y="160" width="20" height="15" rx="3" fill="#3498db"/>
                <rect x="120" y="160" width="20" height="15" rx="3" fill="#e74c3c"/>
            </g>
            
            <!-- 请求数据包 -->
            <g id="request-packet">
                <rect x="0" y="0" width="80" height="50" rx="8" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                <text x="40" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="white">HTTP</text>
                <text x="40" y="35" text-anchor="middle" font-size="12" fill="white">Request</text>
            </g>
            
            <!-- 响应数据包 -->
            <g id="response-packet">
                <rect x="0" y="0" width="80" height="50" rx="8" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                <text x="40" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="white">HTTP</text>
                <text x="40" y="35" text-anchor="middle" font-size="12" fill="white">Response</text>
            </g>
            
            <!-- 箭头标记 -->
            <marker id="arrowhead-blue" markerWidth="15" markerHeight="10" refX="12" refY="5" orient="auto">
                <polygon points="0 0, 15 5, 0 10" fill="#3498db"/>
            </marker>
            
            <marker id="arrowhead-red" markerWidth="15" markerHeight="10" refX="12" refY="5" orient="auto">
                <polygon points="0 0, 15 5, 0 10" fill="#e74c3c"/>
            </marker>
        </defs>
        
        <!-- 背景 -->
        <rect width="1920" height="1080" fill="url(#bgGradient)"/>
        
        <!-- 标题 -->
        <g class="fade-in" style="animation-delay: 0.5s">
            <text x="960" y="120" text-anchor="middle" font-size="72" font-weight="bold" fill="white">
                客户端-服务器模型
            </text>
            <text x="960" y="180" text-anchor="middle" font-size="36" fill="#ecf0f1">
                Client-Server Architecture
            </text>
        </g>
        
        <!-- 客户端 -->
        <g class="slide-up" style="animation-delay: 1s" transform="translate(200, 400)">
            <use href="#client-icon" class="interactive"/>
            <text x="100" y="200" text-anchor="middle" font-size="32" font-weight="bold" fill="#3498db">
                客户端 (Client)
            </text>
            <text x="100" y="240" text-anchor="middle" font-size="24" fill="white">
                浏览器
            </text>
        </g>
        
        <!-- 服务器 -->
        <g class="slide-up" style="animation-delay: 1.2s" transform="translate(1520, 380)">
            <use href="#server-icon" class="interactive"/>
            <text x="90" y="250" text-anchor="middle" font-size="32" font-weight="bold" fill="#e74c3c">
                服务器 (Server)
            </text>
            <text x="90" y="290" text-anchor="middle" font-size="24" fill="white">
                Web Server
            </text>
        </g>
        
        <!-- 请求箭头 -->
        <g class="fade-in" style="animation-delay: 2s">
            <path d="M 450 480 Q 960 420 1470 480" stroke="#3498db" stroke-width="8" fill="none" 
                  marker-end="url(#arrowhead-blue)" class="request-arrow" style="animation-delay: 2.5s"/>
            <text x="960" y="400" text-anchor="middle" font-size="28" font-weight="bold" fill="#3498db">
                HTTP 请求
            </text>
            <text x="960" y="430" text-anchor="middle" font-size="20" fill="#ecf0f1">
                浏览器发送请求
            </text>
        </g>
        
        <!-- 响应箭头 -->
        <g class="fade-in" style="animation-delay: 3s">
            <path d="M 1470 580 Q 960 640 450 580" stroke="#e74c3c" stroke-width="8" fill="none" 
                  marker-end="url(#arrowhead-red)" class="response-arrow" style="animation-delay: 3.5s"/>
            <text x="960" y="680" text-anchor="middle" font-size="28" font-weight="bold" fill="#e74c3c">
                HTTP 响应
            </text>
            <text x="960" y="710" text-anchor="middle" font-size="20" fill="#ecf0f1">
                服务器返回响应
            </text>
        </g>
        
        <!-- 请求数据包动画 -->
        <g class="data-packet" style="animation-delay: 4s" transform="translate(450, 455)">
            <use href="#request-packet"/>
        </g>
        
        <!-- 响应数据包动画 -->
        <g class="response-packet" style="animation-delay: 5s" transform="translate(1390, 555)">
            <use href="#response-packet"/>
        </g>
        
        <!-- 特点说明 -->
        <g class="zoom-in" style="animation-delay: 4s">
            <rect x="100" y="750" width="1720" height="280" rx="20" fill="rgba(52, 73, 94, 0.9)" stroke="#34495e" stroke-width="3"/>
            
            <!-- 标题 -->
            <text x="960" y="800" text-anchor="middle" font-size="36" font-weight="bold" fill="white">
                模型特点
            </text>
            
            <!-- 三个特点 -->
            <g transform="translate(150, 850)">
                <circle cx="0" cy="0" r="15" fill="#3498db" class="pulse"/>
                <text x="40" y="10" font-size="28" font-weight="bold" fill="#3498db">
                    请求-响应模式
                </text>
                <text x="40" y="45" font-size="22" fill="white">
                    客户端主动发起请求，服务器被动响应
                </text>
                <text x="40" y="75" font-size="22" fill="white">
                    一问一答的通信方式
                </text>
            </g>
            
            <g transform="translate(650, 850)">
                <circle cx="0" cy="0" r="15" fill="#f39c12" class="pulse" style="animation-delay: 0.5s"/>
                <text x="40" y="10" font-size="28" font-weight="bold" fill="#f39c12">
                    无状态交互
                </text>
                <text x="40" y="45" font-size="22" fill="white">
                    每个请求都是独立的
                </text>
                <text x="40" y="75" font-size="22" fill="white">
                    服务器不保存客户端状态
                </text>
            </g>
            
            <g transform="translate(1150, 850)">
                <circle cx="0" cy="0" r="15" fill="#2ecc71" class="pulse" style="animation-delay: 1s"/>
                <text x="40" y="10" font-size="28" font-weight="bold" fill="#2ecc71">
                    分工明确
                </text>
                <text x="40" y="45" font-size="22" fill="white">
                    客户端：用户界面和体验
                </text>
                <text x="40" y="75" font-size="22" fill="white">
                    服务器：业务逻辑和数据
                </text>
            </g>
        </g>
        
        <!-- 交互流程说明 -->
        <g class="fade-in" style="animation-delay: 6s">
            <text x="960" y="280" text-anchor="middle" font-size="24" fill="#95a5a6">
                💡 点击客户端或服务器图标查看详细信息 | 按空格键重新播放动画
            </text>
        </g>
        
        <!-- 装饰性网络连接线 -->
        <g class="fade-in" style="animation-delay: 5s" opacity="0.3">
            <line x1="0" y1="540" x2="1920" y2="540" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="10,5"/>
            <text x="960" y="560" text-anchor="middle" font-size="18" fill="#7f8c8d">
                网络连接 (Internet)
            </text>
        </g>
    </svg>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let animationPlaying = false;
            
            // 交互式元素点击事件
            const interactiveElements = document.querySelectorAll('.interactive');
            
            interactiveElements.forEach((element, index) => {
                element.addEventListener('click', function() {
                    const messages = [
                        {
                            title: '🖥️ 客户端特点',
                            content: [
                                '• 用户界面和交互',
                                '• 发起HTTP请求',
                                '• 接收和渲染响应',
                                '• 处理用户操作',
                                '• 缓存和本地存储'
                            ]
                        },
                        {
                            title: '🖥️ 服务器特点', 
                            content: [
                                '• 业务逻辑处理',
                                '• 数据库操作',
                                '• 用户认证授权',
                                '• 并发请求处理',
                                '• 资源管理和分配'
                            ]
                        }
                    ];
                    
                    if (messages[index]) {
                        // 创建信息弹窗
                        const infoBox = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                        infoBox.innerHTML = `
                            <rect x="660" y="300" width="600" height="250" rx="15" fill="rgba(44, 62, 80, 0.95)" stroke="#3498db" stroke-width="3"/>
                            <text x="960" y="340" text-anchor="middle" font-size="24" font-weight="bold" fill="white">${messages[index].title}</text>
                            ${messages[index].content.map((line, i) => 
                                `<text x="690" y="${380 + i * 30}" font-size="20" fill="#ecf0f1">${line}</text>`
                            ).join('')}
                        `;
                        
                        document.querySelector('svg').appendChild(infoBox);
                        
                        // 4秒后移除
                        setTimeout(() => {
                            infoBox.remove();
                        }, 4000);
                    }
                    
                    // 高亮效果
                    this.style.filter = 'drop-shadow(0 0 25px rgba(52, 152, 219, 1))';
                    setTimeout(() => {
                        this.style.filter = '';
                    }, 3000);
                });
            });
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (e.key === ' ') {
                    // 空格键重新播放动画
                    e.preventDefault();
                    if (!animationPlaying) {
                        playAnimation();
                    }
                }
                
                if (e.key === 'r' || e.key === 'R') {
                    // R键重新加载页面
                    location.reload();
                }
            });
            
            // 播放动画序列
            function playAnimation() {
                animationPlaying = true;
                
                // 重置所有动画元素
                const animatedElements = document.querySelectorAll('.data-packet, .response-packet');
                animatedElements.forEach(el => {
                    el.style.animation = 'none';
                    el.offsetHeight; // 触发重排
                });
                
                // 重新播放动画
                setTimeout(() => {
                    document.querySelector('.data-packet').style.animation = 'packetMove 3s ease-in-out forwards';
                }, 100);
                
                setTimeout(() => {
                    document.querySelector('.response-packet').style.animation = 'responseMove 3s ease-in-out forwards';
                }, 1000);
                
                // 动画完成后重置状态
                setTimeout(() => {
                    animationPlaying = false;
                }, 4000);
            }
            
            // 自动播放动画循环
            function autoPlay() {
                if (!animationPlaying) {
                    playAnimation();
                }
                setTimeout(autoPlay, 8000); // 每8秒播放一次
            }
            
            // 页面加载完成后开始自动播放
            setTimeout(autoPlay, 7000);
            
            // 服务器处理动画
            const serverIcon = document.querySelector('#server-icon');
            if (serverIcon) {
                setInterval(() => {
                    const serverLayers = serverIcon.querySelectorAll('rect[fill="#34495e"]');
                    serverLayers.forEach((layer, index) => {
                        setTimeout(() => {
                            layer.style.fill = '#3498db';
                            setTimeout(() => {
                                layer.style.fill = '#34495e';
                            }, 200);
                        }, index * 100);
                    });
                }, 3000);
            }
        });
    </script>
</body>
</html>
