<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一个HTML页面 - 详细教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
            height: 100vh;
        }

        .presentation {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s ease-in-out;
            padding: 2rem;
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .slide-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            max-width: 1200px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-height: 90vh;
            overflow-y: auto;
        }

        h1 {
            font-size: 3rem;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: 2.5rem;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }

        h3 {
            font-size: 1.8rem;
            color: #34495e;
            margin-bottom: 1rem;
        }

        h4 {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 0.8rem;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 2rem;
            border-radius: 10px;
            margin: 1.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            line-height: 1.6;
            overflow-x: auto;
            position: relative;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 0.5rem;
            right: 1rem;
            background: #667eea;
            color: white;
            padding: 0.2rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .html-structure {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }

        .structure-part {
            background: rgba(102, 126, 234, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .tag-explanation {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .tag-name {
            font-family: 'Courier New', monospace;
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }

        .step-by-step {
            counter-reset: step-counter;
        }

        .step {
            counter-increment: step-counter;
            background: rgba(102, 126, 234, 0.1);
            margin: 1rem 0;
            padding: 1.5rem;
            border-radius: 10px;
            position: relative;
            padding-left: 4rem;
        }

        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 2rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem 2rem;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .navigation button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .navigation button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .navigation button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 1000;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
            width: 10%;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #721c24;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #155724;
        }

        .demo-preview {
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            background: white;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: start;
        }

        @media (max-width: 768px) {
            .slide-content {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 { font-size: 2rem; }
            h2 { font-size: 1.8rem; }
            
            .html-structure,
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                bottom: 1rem;
                padding: 0.8rem 1.5rem;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <section class="slide active" id="slide-1">
            <div class="slide-content">
                <h1>第一个HTML页面</h1>
                <div style="text-align: center; margin: 2rem 0;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">🌐</div>
                    <h3>从零开始创建你的第一个网页</h3>
                    <p style="font-size: 1.2rem; color: #666; margin-top: 2rem;">
                        学习HTML的基本结构，理解每个标签的作用，<br>
                        掌握创建网页的基本步骤
                    </p>
                </div>
                <div class="highlight">
                    <strong>学习目标：</strong>
                    <ul style="margin-top: 1rem;">
                        <li>理解HTML文档的基本结构</li>
                        <li>掌握必需的HTML标签</li>
                        <li>能够创建一个完整的HTML页面</li>
                        <li>学会在浏览器中预览网页</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- HTML是什么 -->
        <section class="slide" id="slide-2">
            <div class="slide-content">
                <h2>HTML是什么？</h2>
                <div class="two-column">
                    <div>
                        <h3>HTML全称</h3>
                        <p><strong>H</strong>yper<strong>T</strong>ext <strong>M</strong>arkup <strong>L</strong>anguage</p>
                        <p>超文本标记语言</p>
                        
                        <h3 style="margin-top: 2rem;">主要特点</h3>
                        <ul>
                            <li><strong>标记语言</strong> - 使用标签描述内容</li>
                            <li><strong>结构化</strong> - 定义网页的结构和层次</li>
                            <li><strong>语义化</strong> - 标签具有特定含义</li>
                            <li><strong>跨平台</strong> - 任何浏览器都能解析</li>
                        </ul>
                    </div>
                    <div>
                        <h3>HTML的作用</h3>
                        <div class="tag-explanation">
                            <h4>📄 定义内容结构</h4>
                            <p>告诉浏览器哪些是标题、段落、链接等</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>🔗 创建超链接</h4>
                            <p>连接不同的网页和资源</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>📋 构建表单</h4>
                            <p>收集用户输入的信息</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>🖼️ 嵌入媒体</h4>
                            <p>显示图片、视频、音频等</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- HTML文档结构 -->
        <section class="slide" id="slide-3">
            <div class="slide-content">
                <h2>HTML文档的基本结构</h2>
                <div class="code-block" data-lang="HTML">
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;页面标题&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;!-- 页面内容写在这里 --&gt;
    &lt;h1&gt;欢迎来到我的网页&lt;/h1&gt;
    &lt;p&gt;这是我的第一个HTML页面。&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
                </div>
                <div class="html-structure">
                    <div class="structure-part">
                        <h4>文档声明部分</h4>
                        <p><span class="tag-name">&lt;!DOCTYPE html&gt;</span></p>
                        <p>告诉浏览器这是HTML5文档</p>
                    </div>
                    <div class="structure-part">
                        <h4>根元素</h4>
                        <p><span class="tag-name">&lt;html&gt;</span></p>
                        <p>包含整个HTML文档的根元素</p>
                    </div>
                    <div class="structure-part">
                        <h4>文档头部</h4>
                        <p><span class="tag-name">&lt;head&gt;</span></p>
                        <p>包含页面的元信息，不显示在页面上</p>
                    </div>
                    <div class="structure-part">
                        <h4>文档主体</h4>
                        <p><span class="tag-name">&lt;body&gt;</span></p>
                        <p>包含页面的可见内容</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- DOCTYPE详解 -->
        <section class="slide" id="slide-4">
            <div class="slide-content">
                <h2>DOCTYPE声明详解</h2>
                <div class="code-block" data-lang="HTML">
&lt;!DOCTYPE html&gt;
                </div>
                <div class="two-column">
                    <div>
                        <h3>什么是DOCTYPE？</h3>
                        <ul>
                            <li><strong>Document Type Declaration</strong></li>
                            <li>文档类型声明</li>
                            <li>必须放在HTML文档的第一行</li>
                            <li>告诉浏览器使用哪种HTML版本</li>
                        </ul>
                        
                        <h3 style="margin-top: 2rem;">为什么需要DOCTYPE？</h3>
                        <ul>
                            <li>确保浏览器正确解析HTML</li>
                            <li>避免浏览器进入"怪异模式"</li>
                            <li>保证页面在不同浏览器中一致显示</li>
                        </ul>
                    </div>
                    <div>
                        <h3>HTML版本对比</h3>
                        <div class="tag-explanation">
                            <h4>HTML5 (推荐)</h4>
                            <code>&lt;!DOCTYPE html&gt;</code>
                            <p>简洁明了，现代浏览器标准</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>HTML 4.01</h4>
                            <code>&lt;!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"&gt;</code>
                            <p>较老的标准，代码冗长</p>
                        </div>
                        <div class="warning">
                            <strong>注意：</strong>如果没有DOCTYPE声明，浏览器可能进入"怪异模式"，导致页面显示异常。
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- html标签详解 -->
        <section class="slide" id="slide-5">
            <div class="slide-content">
                <h2>&lt;html&gt;标签详解</h2>
                <div class="code-block" data-lang="HTML">
&lt;html lang="zh-CN"&gt;
    &lt;!-- 整个HTML文档的内容 --&gt;
&lt;/html&gt;
                </div>
                <div class="two-column">
                    <div>
                        <h3>html标签的作用</h3>
                        <ul>
                            <li><strong>根元素</strong> - 包含所有其他HTML元素</li>
                            <li><strong>文档容器</strong> - 定义HTML文档的边界</li>
                            <li><strong>语言声明</strong> - 通过lang属性指定语言</li>
                        </ul>
                        
                        <h3 style="margin-top: 2rem;">常用属性</h3>
                        <div class="tag-explanation">
                            <h4>lang属性</h4>
                            <ul>
                                <li><code>lang="zh-CN"</code> - 简体中文</li>
                                <li><code>lang="en"</code> - 英语</li>
                                <li><code>lang="ja"</code> - 日语</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>为什么要设置lang属性？</h3>
                        <div class="tag-explanation">
                            <h4>🔍 搜索引擎优化</h4>
                            <p>帮助搜索引擎理解页面语言</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>♿ 无障碍访问</h4>
                            <p>屏幕阅读器能正确发音</p>
                        </div>
                        <div class="tag-explanation">
                            <h4>🌐 浏览器功能</h4>
                            <p>浏览器可提供翻译等功能</p>
                        </div>
                        <div class="success">
                            <strong>最佳实践：</strong>始终为html标签添加适当的lang属性
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- head标签详解 -->
        <section class="slide" id="slide-6">
            <div class="slide-content">
                <h2>&lt;head&gt;标签详解</h2>
                <div class="code-block" data-lang="HTML">
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;页面标题&lt;/title&gt;
    &lt;meta name="description" content="页面描述"&gt;
    &lt;meta name="keywords" content="关键词1,关键词2"&gt;
&lt;/head&gt;
                </div>
                <h3>head标签的作用</h3>
                <p>包含页面的<strong>元信息</strong>（metadata），这些信息不会直接显示在页面上，但对浏览器和搜索引擎很重要。</p>
                
                <div class="html-structure">
                    <div class="structure-part">
                        <h4>&lt;meta charset="UTF-8"&gt;</h4>
                        <p><strong>字符编码声明</strong></p>
                        <p>确保中文等特殊字符正确显示</p>
                    </div>
                    <div class="structure-part">
                        <h4>&lt;meta name="viewport"&gt;</h4>
                        <p><strong>视口设置</strong></p>
                        <p>控制页面在移动设备上的显示</p>
                    </div>
                    <div class="structure-part">
                        <h4>&lt;title&gt;</h4>
                        <p><strong>页面标题</strong></p>
                        <p>显示在浏览器标签页和搜索结果中</p>
                    </div>
                    <div class="structure-part">
                        <h4>&lt;meta name="description"&gt;</h4>
                        <p><strong>页面描述</strong></p>
                        <p>搜索引擎结果中的描述文字</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- body标签详解 -->
        <section class="slide" id="slide-7">
            <div class="slide-content">
                <h2>&lt;body&gt;标签详解</h2>
                <div class="code-block" data-lang="HTML">
&lt;body&gt;
    &lt;h1&gt;这是主标题&lt;/h1&gt;
    &lt;h2&gt;这是副标题&lt;/h2&gt;
    &lt;p&gt;这是一个段落。&lt;/p&gt;
    &lt;a href="https://www.example.com"&gt;这是一个链接&lt;/a&gt;
    &lt;img src="image.jpg" alt="图片描述"&gt;
&lt;/body&gt;
                </div>
                <h3>body标签的作用</h3>
                <p>包含页面的<strong>可见内容</strong>，用户在浏览器中看到的所有内容都在body标签内。</p>
                
                <h3>常用的body子元素</h3>
                <div class="html-structure">
                    <div class="structure-part">
                        <h4>标题标签</h4>
                        <p><span class="tag-name">&lt;h1&gt;</span> 到 <span class="tag-name">&lt;h6&gt;</span></p>
                        <p>定义不同级别的标题</p>
                    </div>
                    <div class="structure-part">
                        <h4>段落标签</h4>
                        <p><span class="tag-name">&lt;p&gt;</span></p>
                        <p>定义文本段落</p>
                    </div>
                    <div class="structure-part">
                        <h4>链接标签</h4>
                        <p><span class="tag-name">&lt;a&gt;</span></p>
                        <p>创建超链接</p>
                    </div>
                    <div class="structure-part">
                        <h4>图片标签</h4>
                        <p><span class="tag-name">&lt;img&gt;</span></p>
                        <p>插入图片</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 创建第一个HTML页面 -->
        <section class="slide" id="slide-8">
            <div class="slide-content">
                <h2>创建第一个HTML页面 - 步骤详解</h2>
                <div class="step-by-step">
                    <div class="step">
                        <h4>打开VS Code</h4>
                        <p>启动Visual Studio Code编辑器</p>
                    </div>
                    <div class="step">
                        <h4>创建新文件</h4>
                        <p>按 <strong>Ctrl + N</strong> 或点击 File → New File</p>
                    </div>
                    <div class="step">
                        <h4>保存文件</h4>
                        <p>按 <strong>Ctrl + S</strong>，文件名输入 <strong>index.html</strong></p>
                        <div class="warning">
                            <strong>注意：</strong>文件扩展名必须是 .html 或 .htm
                        </div>
                    </div>
                    <div class="step">
                        <h4>输入HTML结构</h4>
                        <p>在VS Code中输入 <strong>!</strong> 然后按 <strong>Tab</strong> 键，自动生成HTML模板</p>
                    </div>
                    <div class="step">
                        <h4>添加内容</h4>
                        <p>在 &lt;body&gt; 标签内添加你的内容</p>
                    </div>
                    <div class="step">
                        <h4>预览页面</h4>
                        <p>右键点击文件，选择 "Open with Live Server"</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 完整示例 -->
        <section class="slide" id="slide-9">
            <div class="slide-content">
                <h2>完整的第一个HTML页面示例</h2>
                <div class="code-block" data-lang="HTML">
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;我的第一个网页&lt;/title&gt;
    &lt;meta name="description" content="这是我学习Web开发创建的第一个HTML页面"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;欢迎来到我的网页！&lt;/h1&gt;
    &lt;h2&gt;关于我&lt;/h2&gt;
    &lt;p&gt;我是一名正在学习Web开发的学生。这是我创建的第一个HTML页面。&lt;/p&gt;
    
    &lt;h2&gt;我的兴趣&lt;/h2&gt;
    &lt;p&gt;我喜欢编程、阅读和音乐。&lt;/p&gt;
    
    &lt;h2&gt;联系方式&lt;/h2&gt;
    &lt;p&gt;邮箱: &lt;a href="mailto:<EMAIL>"&gt;<EMAIL>&lt;/a&gt;&lt;/p&gt;
    
    &lt;p&gt;&lt;strong&gt;感谢您访问我的网页！&lt;/strong&gt;&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
                </div>
                <div class="demo-preview">
                    <h3>预览效果：</h3>
                    <h1 style="color: #2c3e50; font-size: 2rem;">欢迎来到我的网页！</h1>
                    <h2 style="color: #34495e; font-size: 1.5rem;">关于我</h2>
                    <p>我是一名正在学习Web开发的学生。这是我创建的第一个HTML页面。</p>
                    <h2 style="color: #34495e; font-size: 1.5rem;">我的兴趣</h2>
                    <p>我喜欢编程、阅读和音乐。</p>
                </div>
            </div>
        </section>

        <!-- 常见错误和解决方案 -->
        <section class="slide" id="slide-10">
            <div class="slide-content">
                <h2>常见错误和解决方案</h2>
                <div class="html-structure">
                    <div class="structure-part">
                        <h4>❌ 忘记DOCTYPE声明</h4>
                        <div class="warning">
                            <p><strong>错误：</strong>直接从 &lt;html&gt; 开始</p>
                            <p><strong>后果：</strong>浏览器可能进入怪异模式</p>
                            <p><strong>解决：</strong>始终在第一行添加 &lt;!DOCTYPE html&gt;</p>
                        </div>
                    </div>
                    <div class="structure-part">
                        <h4>❌ 标签不匹配</h4>
                        <div class="warning">
                            <p><strong>错误：</strong>&lt;h1&gt;标题&lt;/h2&gt;</p>
                            <p><strong>后果：</strong>页面结构混乱</p>
                            <p><strong>解决：</strong>确保开始和结束标签匹配</p>
                        </div>
                    </div>
                    <div class="structure-part">
                        <h4>❌ 忘记字符编码</h4>
                        <div class="warning">
                            <p><strong>错误：</strong>没有 &lt;meta charset="UTF-8"&gt;</p>
                            <p><strong>后果：</strong>中文显示乱码</p>
                            <p><strong>解决：</strong>在head中添加字符编码声明</p>
                        </div>
                    </div>
                    <div class="structure-part">
                        <h4>❌ 文件扩展名错误</h4>
                        <div class="warning">
                            <p><strong>错误：</strong>保存为 .txt 或其他格式</p>
                            <p><strong>后果：</strong>浏览器无法正确解析</p>
                            <p><strong>解决：</strong>确保文件扩展名为 .html</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结 -->
        <section class="slide" id="slide-11">
            <div class="slide-content">
                <h2>总结</h2>
                <div class="two-column">
                    <div>
                        <h3>✅ 你已经学会了</h3>
                        <ul>
                            <li>HTML文档的基本结构</li>
                            <li>DOCTYPE声明的重要性</li>
                            <li>html、head、body标签的作用</li>
                            <li>必需的meta标签</li>
                            <li>如何创建和保存HTML文件</li>
                            <li>如何在浏览器中预览页面</li>
                        </ul>
                        
                        <div class="success">
                            <strong>恭喜！</strong>你已经成功创建了第一个HTML页面！
                        </div>
                    </div>
                    <div>
                        <h3>🎯 下一步学习</h3>
                        <ul>
                            <li>学习更多HTML标签</li>
                            <li>了解HTML语义化</li>
                            <li>学习CSS样式设计</li>
                            <li>添加JavaScript交互</li>
                        </ul>
                        
                        <h3>📝 练习建议</h3>
                        <ul>
                            <li>创建一个个人介绍页面</li>
                            <li>尝试使用不同的HTML标签</li>
                            <li>练习使用VS Code的快捷键</li>
                            <li>熟悉浏览器开发者工具</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button id="prevBtn" onclick="changeSlide(-1)">← 上一页</button>
        <span id="slideCounter">1 / 11</span>
        <button id="nextBtn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress" id="progress"></div>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 11;
        const slides = document.querySelectorAll('.slide');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const slideCounter = document.getElementById('slideCounter');
        const progress = document.getElementById('progress');

        function updateSlide() {
            slides.forEach((slide, index) => {
                slide.classList.remove('active', 'prev');
                if (index === currentSlide - 1) {
                    slide.classList.add('active');
                } else if (index < currentSlide - 1) {
                    slide.classList.add('prev');
                }
            });

            slideCounter.textContent = `${currentSlide} / ${totalSlides}`;
            progress.style.width = `${(currentSlide / totalSlides) * 100}%`;
            
            prevBtn.disabled = currentSlide === 1;
            nextBtn.disabled = currentSlide === totalSlides;
        }

        function changeSlide(direction) {
            const newSlide = currentSlide + direction;
            if (newSlide >= 1 && newSlide <= totalSlides) {
                currentSlide = newSlide;
                updateSlide();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    changeSlide(-1);
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    changeSlide(1);
                    break;
                case 'Home':
                    e.preventDefault();
                    currentSlide = 1;
                    updateSlide();
                    break;
                case 'End':
                    e.preventDefault();
                    currentSlide = totalSlides;
                    updateSlide();
                    break;
            }
        });

        // 初始化
        updateSlide();
    </script>
</body>
</html>
