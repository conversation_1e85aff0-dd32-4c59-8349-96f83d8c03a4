<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDFS文件系统基本架构</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
            color: white;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        .main-title {
            font-size: 4.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24, #6c5ce7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 300% 300%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .subtitle {
            font-size: 2.2rem;
            opacity: 0.9;
            font-weight: 300;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .architecture-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            max-width: 1400px;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .namenode-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: slideInLeft 1s ease-out 0.5s both;
        }

        .datanode-section {
            flex: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: slideInRight 1s ease-out 0.7s both;
        }

        .node-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 2rem;
            text-align: center;
            border: 3px solid;
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
            width: 100%;
        }

        .node-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .node-card:hover::before {
            left: 100%;
        }

        .node-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .namenode-card {
            border-color: #ff6b6b;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(238, 82, 83, 0.2));
        }

        .namenode-card:hover {
            border-color: #ff6b6b;
            box-shadow: 0 25px 50px rgba(255, 107, 107, 0.3);
        }

        .datanode-card {
            border-color: #4ecdc4;
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(69, 183, 209, 0.2));
        }

        .datanode-card:hover {
            border-color: #4ecdc4;
            box-shadow: 0 25px 50px rgba(78, 205, 196, 0.3);
        }

        .node-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        .node-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .node-description {
            font-size: 1.4rem;
            opacity: 0.9;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        .node-features {
            list-style: none;
            font-size: 1.2rem;
            text-align: left;
        }

        .node-features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .node-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #f9ca24;
            font-weight: bold;
        }

        .datanodes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            width: 100%;
        }

        .datanode-item {
            background: rgba(78, 205, 196, 0.1);
            border: 2px solid #4ecdc4;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .datanode-item:hover {
            transform: scale(1.05);
            background: rgba(78, 205, 196, 0.2);
            box-shadow: 0 10px 20px rgba(78, 205, 196, 0.3);
        }

        .datanode-item h4 {
            font-size: 1.6rem;
            margin-bottom: 0.5rem;
            color: #4ecdc4;
        }

        .datanode-item p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .connections {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 2px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            animation: dataFlow 3s ease-in-out infinite;
            opacity: 0.7;
        }

        .connection-arrow {
            position: absolute;
            right: -10px;
            top: -8px;
            width: 0;
            height: 0;
            border-left: 15px solid #4ecdc4;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .features-section {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            width: 100%;
            max-width: 1400px;
            animation: fadeInUp 1s ease-out 1s both;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #f9ca24;
        }

        .feature-description {
            font-size: 1.3rem;
            line-height: 1.4;
            opacity: 0.9;
        }

        .summary {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-out 2s both;
            max-width: 1200px;
            margin-top: 2rem;
        }

        .summary-text {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            line-height: 1.4;
        }

        .highlight {
            color: #f9ca24;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        /* 动画定义 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes dataFlow {
            0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scaleX(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scaleX(1.1); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-title { font-size: 3.5rem; }
            .subtitle { font-size: 1.8rem; }
            .architecture-container { flex-direction: column; gap: 2rem; }
            .features-section { grid-template-columns: repeat(2, 1fr); }
            .datanodes-grid { grid-template-columns: repeat(2, 1fr); }
            .node-title { font-size: 2rem; }
            .summary-text { font-size: 1.8rem; }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2.8rem; }
            .subtitle { font-size: 1.5rem; }
            .features-section { grid-template-columns: 1fr; }
            .datanodes-grid { grid-template-columns: 1fr; }
            .node-card { padding: 1.5rem; }
            .node-title { font-size: 1.8rem; }
            .node-description { font-size: 1.2rem; }
            .summary-text { font-size: 1.5rem; }
        }

        /* 交互提示 */
        .interaction-hint {
            position: absolute;
            top: 2rem;
            right: 2rem;
            font-size: 1.3rem;
            opacity: 0.7;
            animation: fadeIn 2s ease-out 2.5s both;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 交互提示 -->
        <div class="interaction-hint">
            💡 点击组件卡片查看详细信息
        </div>

        <!-- 标题区域 -->
        <div class="header">
            <h1 class="main-title">HDFS文件系统基本架构</h1>
            <p class="subtitle">Hadoop分布式文件系统核心组件</p>
        </div>

        <!-- 架构组件区域 -->
        <div class="architecture-container">
            <!-- NameNode区域 -->
            <div class="namenode-section">
                <div class="node-card namenode-card" onclick="showNodeDetails('namenode')">
                    <div class="node-icon">🧠</div>
                    <div class="node-title">NameNode</div>
                    <div class="node-description">
                        HDFS的主节点<br>
                        管理文件系统元数据
                    </div>
                    <ul class="node-features">
                        <li>维护文件系统树</li>
                        <li>管理文件块映射</li>
                        <li>处理客户端请求</li>
                        <li>监控DataNode状态</li>
                    </ul>
                </div>
            </div>

            <!-- 连接线 -->
            <div class="connections">
                <div class="connection-arrow"></div>
            </div>

            <!-- DataNode区域 -->
            <div class="datanode-section">
                <div class="node-card datanode-card" onclick="showNodeDetails('datanode')">
                    <div class="node-icon">💾</div>
                    <div class="node-title">DataNode集群</div>
                    <div class="node-description">
                        HDFS的工作节点<br>
                        存储实际的文件数据块
                    </div>
                </div>
                
                <div class="datanodes-grid">
                    <div class="datanode-item" onclick="showDataNodeDetails(1)">
                        <h4>DataNode 1</h4>
                        <p>存储数据块<br>定期心跳</p>
                    </div>
                    <div class="datanode-item" onclick="showDataNodeDetails(2)">
                        <h4>DataNode 2</h4>
                        <p>数据副本<br>负载均衡</p>
                    </div>
                    <div class="datanode-item" onclick="showDataNodeDetails(3)">
                        <h4>DataNode 3</h4>
                        <p>容错恢复<br>扩展性</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 特性展示区域 -->
        <div class="features-section">
            <div class="feature-card" onclick="showFeatureDetails('fault-tolerance')">
                <span class="feature-icon">🛡️</span>
                <div class="feature-title">容错性</div>
                <div class="feature-description">
                    数据多副本存储<br>
                    自动故障恢复
                </div>
            </div>

            <div class="feature-card" onclick="showFeatureDetails('scalability')">
                <span class="feature-icon">📈</span>
                <div class="feature-title">可扩展性</div>
                <div class="feature-description">
                    水平扩展能力<br>
                    PB级数据存储
                </div>
            </div>

            <div class="feature-card" onclick="showFeatureDetails('high-throughput')">
                <span class="feature-icon">⚡</span>
                <div class="feature-title">高吞吐量</div>
                <div class="feature-description">
                    大文件优化<br>
                    流式数据访问
                </div>
            </div>

            <div class="feature-card" onclick="showFeatureDetails('data-locality')">
                <span class="feature-icon">📍</span>
                <div class="feature-title">数据本地性</div>
                <div class="feature-description">
                    计算靠近数据<br>
                    减少网络传输
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary">
            <div class="summary-text">
                🏗️ HDFS采用<span class="highlight">主从架构</span>：NameNode管理元数据，DataNode存储数据<br>
                通过<span class="highlight">数据副本</span>和<span class="highlight">分布式存储</span>实现高可靠性和可扩展性
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
        <div style="background: linear-gradient(135deg, #1e3c72, #2a5298); padding: 3rem; border-radius: 25px; max-width: 900px; width: 90%; color: white; position: relative; max-height: 80vh; overflow-y: auto;">
            <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 2.5rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
            <div id="modal-content"></div>
        </div>
    </div>

    <script>
        // 组件详细信息
        const nodeDetails = {
            namenode: {
                title: 'NameNode - HDFS主节点',
                icon: '🧠',
                description: 'HDFS的核心组件，负责管理整个文件系统的元数据',
                responsibilities: [
                    '维护文件系统命名空间（文件和目录树）',
                    '管理文件到数据块的映射关系',
                    '处理客户端的文件系统操作请求',
                    '监控DataNode的健康状态',
                    '管理数据块的副本策略',
                    '执行文件系统的检查点操作'
                ],
                architecture: [
                    'FSImage：文件系统元数据的持久化存储',
                    'EditLog：记录文件系统的所有变更操作',
                    'BlockManager：管理数据块和副本',
                    'LeaseManager：管理文件写入租约',
                    'SafeMode：系统启动时的安全模式'
                ],
                importance: 'NameNode是HDFS的单点，其可用性直接影响整个文件系统的运行'
            },
            datanode: {
                title: 'DataNode - HDFS工作节点',
                icon: '💾',
                description: 'HDFS的工作节点，负责实际存储文件数据块',
                responsibilities: [
                    '存储文件数据块到本地磁盘',
                    '定期向NameNode发送心跳和块报告',
                    '执行NameNode的数据块操作指令',
                    '为客户端提供数据块读写服务',
                    '参与数据块的副本复制',
                    '检测和报告数据块损坏'
                ],
                features: [
                    '多磁盘存储：支持多个磁盘并行存储',
                    '数据完整性：通过校验和检测数据损坏',
                    '负载均衡：自动平衡各节点的存储负载',
                    '热插拔：支持在线添加和移除节点',
                    '数据本地性：优先为本地计算提供数据'
                ],
                importance: 'DataNode的数量决定了HDFS的存储容量和并行处理能力'
            }
        };

        // DataNode详细信息
        const dataNodeDetails = {
            1: {
                title: 'DataNode 存储机制',
                content: [
                    '数据块存储：将大文件分割成固定大小的块（默认128MB）',
                    '本地文件系统：数据块作为普通文件存储在本地磁盘',
                    '元数据管理：维护块ID到本地文件路径的映射',
                    '校验和验证：为每个数据块计算和存储校验和',
                    '存储目录：支持配置多个存储目录提高I/O性能'
                ]
            },
            2: {
                title: 'DataNode 副本管理',
                content: [
                    '副本策略：默认3副本，第一个副本在本地，第二个在不同机架',
                    '副本放置：考虑网络拓扑和负载均衡',
                    '副本同步：写入时同步复制到所有副本节点',
                    '副本修复：检测到副本丢失时自动创建新副本',
                    '副本验证：定期验证副本的完整性和一致性'
                ]
            },
            3: {
                title: 'DataNode 容错恢复',
                content: [
                    '心跳机制：定期向NameNode发送心跳证明存活',
                    '块报告：定期报告本地存储的所有数据块',
                    '故障检测：NameNode检测DataNode故障并标记',
                    '数据恢复：从其他副本恢复丢失的数据块',
                    '节点替换：支持故障节点的在线替换和数据迁移'
                ]
            }
        };

        // 特性详细信息
        const featureDetails = {
            'fault-tolerance': {
                title: '容错性 - 高可靠性保障',
                icon: '🛡️',
                description: 'HDFS通过多种机制确保数据的高可靠性',
                mechanisms: [
                    '数据副本：每个数据块默认存储3个副本',
                    '副本放置：智能的副本放置策略避免单点故障',
                    '故障检测：实时监控节点和数据块状态',
                    '自动恢复：故障时自动从其他副本恢复数据',
                    '校验和：检测和修复数据损坏',
                    '安全模式：启动时的数据完整性检查'
                ],
                benefits: 'HDFS可以容忍多个节点同时故障而不丢失数据'
            },
            'scalability': {
                title: '可扩展性 - 水平扩展能力',
                icon: '📈',
                description: 'HDFS支持从几个节点扩展到数千个节点',
                features: [
                    '水平扩展：通过添加更多DataNode增加存储容量',
                    '线性扩展：存储容量和吞吐量近似线性增长',
                    '在线扩展：支持在不停机的情况下添加节点',
                    '负载均衡：自动平衡新旧节点的数据分布',
                    'PB级存储：支持PB级别的数据存储',
                    '数千节点：单个集群可支持数千个DataNode'
                ],
                benefits: '随着数据增长可以灵活扩展存储和计算资源'
            },
            'high-throughput': {
                title: '高吞吐量 - 大数据处理优化',
                icon: '⚡',
                description: 'HDFS针对大文件和批处理进行了优化',
                optimizations: [
                    '大块大小：128MB的大块减少元数据开销',
                    '流式访问：优化顺序读取大文件的性能',
                    '并行I/O：多个DataNode并行提供数据',
                    '网络优化：减少网络往返次数',
                    '缓存机制：智能的数据缓存策略',
                    '管道复制：写入时的流水线复制机制'
                ],
                benefits: '适合大数据分析和批处理应用的高吞吐量需求'
            },
            'data-locality': {
                title: '数据本地性 - 计算靠近数据',
                icon: '📍',
                description: '将计算任务调度到数据所在的节点执行',
                principles: [
                    '本地计算：优先在存储数据的节点执行计算',
                    '机架感知：考虑网络拓扑结构的任务调度',
                    '网络优化：减少跨网络的数据传输',
                    '调度策略：MapReduce等框架的本地性调度',
                    '性能提升：显著提高大数据处理性能',
                    '带宽节省：减少网络带宽消耗'
                ],
                benefits: '大幅提高大数据处理的性能和效率'
            }
        };

        function showNodeDetails(nodeType) {
            const data = nodeDetails[nodeType];
            showModal(data, 'node');
        }

        function showDataNodeDetails(nodeId) {
            const data = dataNodeDetails[nodeId];
            showModal(data, 'datanode');
        }

        function showFeatureDetails(featureType) {
            const data = featureDetails[featureType];
            showModal(data, 'feature');
        }

        function showModal(data, type) {
            const modal = document.getElementById('modal');
            const content = document.getElementById('modal-content');
            
            let html = `
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">${data.icon || '📊'}</div>
                    <h2 style="font-size: 2.8rem; margin-bottom: 1rem;">${data.title}</h2>
                    <p style="font-size: 1.6rem; opacity: 0.9;">${data.description}</p>
                </div>
            `;
            
            if (type === 'node') {
                if (data.responsibilities) {
                    html += `
                        <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">主要职责</h3>
                            <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                                ${data.responsibilities.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
                
                if (data.architecture) {
                    html += `
                        <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">架构组件</h3>
                            <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                                ${data.architecture.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
                
                if (data.features) {
                    html += `
                        <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">主要特性</h3>
                            <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                                ${data.features.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
            }
            
            if (type === 'datanode' && data.content) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">详细说明</h3>
                        <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                            ${data.content.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (type === 'feature') {
                const listKey = data.mechanisms || data.features || data.optimizations || data.principles;
                if (listKey) {
                    html += `
                        <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                            <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">核心机制</h3>
                            <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                                ${listKey.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
            }
            
            if (data.importance || data.benefits) {
                html += `
                    <div style="padding: 1.5rem; background: rgba(249, 202, 36, 0.2); border-radius: 15px; border-left: 4px solid #f9ca24;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f9ca24;">💡 ${data.importance || data.benefits}</h3>
                    </div>
                `;
            }
            
            content.innerHTML = html;
            modal.style.display = 'flex';
            
            // 添加动画效果
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
            
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
        });

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 添加动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 数据流动效果
            setInterval(() => {
                const connections = document.querySelector('.connections');
                if (connections) {
                    connections.style.animation = 'none';
                    setTimeout(() => {
                        connections.style.animation = 'dataFlow 3s ease-in-out infinite';
                    }, 10);
                }
            }, 5000);
        });
    </script>
</body>
</html>
