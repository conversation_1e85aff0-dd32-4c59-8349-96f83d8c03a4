/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
    height: 100vh;
}

/* 演示文稿容器 */
.presentation {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* 幻灯片样式 */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    padding: 2rem;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

/* 幻灯片内容 */
.slide-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    max-width: 1000px;
    width: 100%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* 标题样式 */
h1 {
    font-size: 3rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

h3 {
    font-size: 1.5rem;
    color: #34495e;
    margin-bottom: 1rem;
}

/* 课程信息 */
.course-info {
    text-align: left;
    margin-top: 2rem;
}

.course-info p {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.course-info ul {
    list-style: none;
    padding-left: 0;
}

.course-info li {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
    padding-left: 2rem;
    position: relative;
}

.course-info li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.2rem;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 3rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background: #667eea;
    border-radius: 50%;
    border: 3px solid white;
}

.year {
    font-weight: bold;
    color: #667eea;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.content {
    font-size: 1rem;
    color: #555;
    line-height: 1.5;
}

/* Web架构样式 */
.web-architecture {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.component {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    min-width: 200px;
    margin: 1rem;
}

.component h3 {
    color: white;
    margin-bottom: 1rem;
}

.component ul {
    list-style: none;
    text-align: left;
}

.component li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.component li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: rgba(255, 255, 255, 0.8);
}

.arrow {
    font-size: 2rem;
    color: #667eea;
    font-weight: bold;
}

.protocol {
    text-align: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
}

/* HTTP信息样式 */
.http-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.http-methods, .http-status {
    background: rgba(102, 126, 234, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
}

.http-example {
    background: #2c3e50;
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
}

.http-example pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    line-height: 1.5;
}

/* 工具网格 */
.tools-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.tool {
    background: rgba(102, 126, 234, 0.1);
    padding: 2rem;
    border-radius: 15px;
}

.tool-item ul {
    list-style: none;
    margin-top: 1rem;
}

.tool-item li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.tool-item li::before {
    content: "→";
    position: absolute;
    left: 0;
    color: #667eea;
}

/* 代码演示 */
.code-demo {
    margin-bottom: 2rem;
}

.code-demo pre {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 1.5rem;
    border-radius: 10px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    line-height: 1.5;
}

.html-explanation {
    background: rgba(102, 126, 234, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
}

.html-explanation code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

/* 练习样式 */
.exercise {
    text-align: left;
}

.exercise h3 {
    color: #667eea;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.exercise ol {
    margin-left: 2rem;
    margin-bottom: 1.5rem;
}

.exercise li {
    margin-bottom: 0.8rem;
    line-height: 1.5;
}

/* 总结样式 */
.summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.summary-item {
    background: rgba(102, 126, 234, 0.1);
    padding: 2rem;
    border-radius: 15px;
}

.summary-item h3 {
    color: #667eea;
    margin-bottom: 1rem;
}

.summary-item ul {
    list-style: none;
}

.summary-item li {
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
}

.summary-item li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.next-week {
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
}

.next-week h3 {
    color: white;
    margin-bottom: 1rem;
}

/* 导航控制 */
.navigation {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 2rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem 2rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.navigation button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.navigation button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.navigation button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

#slideCounter {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* 进度条 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    z-index: 1000;
}

.progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.5s ease;
    width: 12.5%;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .slide-content {
        padding: 2rem;
        margin: 1rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .web-architecture {
        flex-direction: column;
    }
    
    .http-info,
    .tools-grid,
    .summary {
        grid-template-columns: 1fr;
    }
    
    .navigation {
        bottom: 1rem;
        padding: 0.8rem 1.5rem;
        gap: 1rem;
    }
    
    .navigation button {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}
