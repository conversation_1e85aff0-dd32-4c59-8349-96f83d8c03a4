<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP请求-响应生命周期</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            color: white;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        .main-title {
            font-size: 4.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #3498db, #2ecc71, #f1c40f, #e74c3c, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 300% 300%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .subtitle {
            font-size: 2.2rem;
            opacity: 0.9;
            font-weight: 300;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .lifecycle-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1400px;
            margin-bottom: 3rem;
            position: relative;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            flex: 1;
            position: relative;
            animation: fadeInUp 1s ease-out both;
        }

        .step:nth-child(1) { animation-delay: 1s; }
        .step:nth-child(2) { animation-delay: 1.2s; }
        .step:nth-child(3) { animation-delay: 1.4s; }
        .step:nth-child(4) { animation-delay: 1.6s; }
        .step:nth-child(5) { animation-delay: 1.8s; }

        .step-number {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            border: 4px solid;
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .step-number:hover::before {
            left: 100%;
        }

        .step-number:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(255,255,255,0.3);
        }

        .step-1 .step-number {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-color: #3498db;
            color: white;
        }

        .step-2 .step-number {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border-color: #f39c12;
            color: white;
        }

        .step-3 .step-number {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-color: #e74c3c;
            color: white;
        }

        .step-4 .step-number {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            border-color: #9b59b6;
            color: white;
        }

        .step-5 .step-number {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            border-color: #2ecc71;
            color: white;
        }

        .step-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .step-description {
            font-size: 1.4rem;
            opacity: 0.9;
            line-height: 1.4;
            max-width: 200px;
        }

        .step-time {
            font-size: 1.2rem;
            color: #f1c40f;
            font-weight: bold;
            margin-top: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .arrow {
            position: absolute;
            top: 40px;
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
            animation: flowRight 2s ease-in-out infinite;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -6px;
            width: 0;
            height: 0;
            border-left: 12px solid #2ecc71;
            border-top: 7px solid transparent;
            border-bottom: 7px solid transparent;
        }

        .arrow-1 { right: -30px; animation-delay: 0s; }
        .arrow-2 { right: -30px; animation-delay: 0.5s; }
        .arrow-3 { right: -30px; animation-delay: 1s; }
        .arrow-4 { right: -30px; animation-delay: 1.5s; }

        .details-section {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1.5rem;
            width: 100%;
            max-width: 1400px;
            animation: fadeInUp 1s ease-out 2s both;
        }

        .detail-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .detail-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .detail-card:hover::before {
            left: 100%;
        }

        .detail-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .detail-card h3 {
            font-size: 1.6rem;
            margin-bottom: 1rem;
            color: #f1c40f;
        }

        .detail-card p {
            font-size: 1.2rem;
            line-height: 1.4;
            opacity: 0.9;
        }

        .summary {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-out 2.5s both;
            max-width: 1200px;
            margin-top: 2rem;
        }

        .summary-text {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            line-height: 1.4;
        }

        .highlight {
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        /* 动画定义 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes flowRight {
            0%, 100% { opacity: 0.6; transform: translateX(0); }
            50% { opacity: 1; transform: translateX(10px); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-title { font-size: 3.5rem; }
            .subtitle { font-size: 1.8rem; }
            .lifecycle-container { flex-direction: column; gap: 2rem; }
            .arrow { display: none; }
            .details-section { grid-template-columns: repeat(2, 1fr); }
            .step-title { font-size: 1.8rem; }
            .summary-text { font-size: 1.8rem; }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2.8rem; }
            .subtitle { font-size: 1.5rem; }
            .details-section { grid-template-columns: 1fr; }
            .step-number { width: 60px; height: 60px; font-size: 2rem; }
            .step-title { font-size: 1.5rem; }
            .step-description { font-size: 1.2rem; }
            .summary-text { font-size: 1.5rem; }
        }

        /* 交互提示 */
        .interaction-hint {
            position: absolute;
            top: 2rem;
            right: 2rem;
            font-size: 1.3rem;
            opacity: 0.7;
            animation: fadeIn 2s ease-out 3s both;
        }

        /* 数据流动效果 */
        .data-flow {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
            animation: dataMove 3s ease-in-out infinite;
            opacity: 0.6;
        }

        @keyframes dataMove {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 交互提示 -->
        <div class="interaction-hint">
            💡 点击步骤圆圈或详情卡片查看更多信息
        </div>

        <!-- 标题区域 -->
        <div class="header">
            <h1 class="main-title">HTTP请求-响应生命周期</h1>
            <p class="subtitle">从用户点击到页面渲染的完整过程</p>
        </div>

        <!-- 生命周期步骤 -->
        <div class="lifecycle-container">
            <div class="data-flow"></div>
            
            <div class="step step-1" onclick="showStepDetails(1)">
                <div class="step-number">1</div>
                <div class="step-title">构造请求</div>
                <div class="step-description">
                    浏览器准备<br>
                    HTTP请求
                </div>
                <div class="step-time">~50ms</div>
                <div class="arrow arrow-1"></div>
            </div>

            <div class="step step-2" onclick="showStepDetails(2)">
                <div class="step-number">2</div>
                <div class="step-title">网络传输</div>
                <div class="step-description">
                    请求通过网络<br>
                    发送到服务器
                </div>
                <div class="step-time">~30ms</div>
                <div class="arrow arrow-2"></div>
            </div>

            <div class="step step-3" onclick="showStepDetails(3)">
                <div class="step-number">3</div>
                <div class="step-title">服务器处理</div>
                <div class="step-description">
                    服务器执行<br>
                    业务逻辑
                </div>
                <div class="step-time">~100ms</div>
                <div class="arrow arrow-3"></div>
            </div>

            <div class="step step-4" onclick="showStepDetails(4)">
                <div class="step-number">4</div>
                <div class="step-title">返回响应</div>
                <div class="step-description">
                    服务器发送<br>
                    HTTP响应
                </div>
                <div class="step-time">~50ms</div>
                <div class="arrow arrow-4"></div>
            </div>

            <div class="step step-5" onclick="showStepDetails(5)">
                <div class="step-number">5</div>
                <div class="step-title">页面渲染</div>
                <div class="step-description">
                    浏览器渲染<br>
                    页面内容
                </div>
                <div class="step-time">~140ms</div>
            </div>
        </div>

        <!-- 详细信息卡片 -->
        <div class="details-section">
            <div class="detail-card" onclick="showDetailInfo('dns')">
                <h3>DNS解析</h3>
                <p>域名转换为IP地址<br>通常需要20ms</p>
            </div>

            <div class="detail-card" onclick="showDetailInfo('tcp')">
                <h3>TCP连接</h3>
                <p>建立三次握手连接<br>通常需要30ms</p>
            </div>

            <div class="detail-card" onclick="showDetailInfo('processing')">
                <h3>业务处理</h3>
                <p>数据库查询和逻辑处理<br>通常需要100ms</p>
            </div>

            <div class="detail-card" onclick="showDetailInfo('parsing')">
                <h3>DOM解析</h3>
                <p>解析HTML构建DOM树<br>通常需要80ms</p>
            </div>

            <div class="detail-card" onclick="showDetailInfo('rendering')">
                <h3>页面渲染</h3>
                <p>样式计算和页面绘制<br>通常需要60ms</p>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary">
            <div class="summary-text">
                ⚡ 总耗时约 <span class="highlight">370ms</span>，优化每个环节都能提升用户体验<br>
                关键优化点：<span class="highlight">缓存</span>、<span class="highlight">CDN</span>、<span class="highlight">数据库索引</span>、<span class="highlight">代码分割</span>
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
        <div style="background: linear-gradient(135deg, #2c3e50, #34495e); padding: 3rem; border-radius: 25px; max-width: 900px; width: 90%; color: white; position: relative; max-height: 80vh; overflow-y: auto;">
            <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 2.5rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
            <div id="modal-content"></div>
        </div>
    </div>

    <script>
        // 步骤详细信息
        const stepDetails = {
            1: {
                title: '步骤1：构造请求 (~50ms)',
                icon: '🔧',
                description: '浏览器准备发送HTTP请求的阶段',
                details: [
                    '解析URL地址和参数',
                    'DNS域名解析 (20ms)',
                    '建立TCP连接 (30ms)',
                    '构造HTTP请求头',
                    '准备请求数据和参数'
                ],
                example: 'GET /api/users HTTP/1.1\nHost: www.example.com\nUser-Agent: Mozilla/5.0'
            },
            2: {
                title: '步骤2：网络传输 (~30ms)',
                icon: '🌐',
                description: '请求通过网络基础设施传输到目标服务器',
                details: [
                    '数据包封装和路由选择',
                    '通过ISP网络传输',
                    '经过多个路由器转发',
                    '到达目标服务器',
                    'TCP确认和重传机制'
                ],
                example: '客户端 → 路由器 → ISP → 互联网 → 目标服务器\n平均延迟: 5ms + 10ms + 15ms = 30ms'
            },
            3: {
                title: '步骤3：服务器处理 (~100ms)',
                icon: '⚙️',
                description: '服务器接收请求并执行相应的业务逻辑',
                details: [
                    '解析HTTP请求内容',
                    '路由匹配和权限验证',
                    '执行业务逻辑代码',
                    '数据库查询和操作',
                    '生成响应数据'
                ],
                example: 'app.get("/api/users", async (req, res) => {\n  const users = await db.users.findAll();\n  res.json(users);\n});'
            },
            4: {
                title: '步骤4：返回响应 (~50ms)',
                icon: '📤',
                description: '服务器将处理结果发送回客户端',
                details: [
                    '构造HTTP响应头',
                    '设置状态码和元信息',
                    '添加响应数据内容',
                    '通过网络传输回客户端',
                    'TCP连接管理和优化'
                ],
                example: 'HTTP/1.1 200 OK\nContent-Type: application/json\nContent-Length: 1024\n\n{"users": [...]}'
            },
            5: {
                title: '步骤5：页面渲染 (~140ms)',
                icon: '🎨',
                description: '浏览器接收响应并渲染页面内容',
                details: [
                    '接收和解析响应数据',
                    '解析HTML构建DOM树 (80ms)',
                    '解析CSS计算样式',
                    '执行JavaScript代码',
                    '页面布局和绘制 (60ms)'
                ],
                example: 'HTML解析 → DOM构建 → CSS解析 → 样式计算 → 布局 → 绘制 → 合成'
            }
        };

        // 详细信息数据
        const detailInfo = {
            dns: {
                title: 'DNS域名解析',
                icon: '🔍',
                description: 'DNS (Domain Name System) 将人类可读的域名转换为IP地址',
                content: [
                    '浏览器首先检查本地DNS缓存',
                    '如果缓存未命中，查询操作系统DNS缓存',
                    '向本地DNS服务器发送查询请求',
                    'DNS服务器递归查询根服务器、顶级域服务器',
                    '最终获得目标域名的IP地址',
                    '整个过程通常需要20ms，缓存命中时几乎为0'
                ],
                optimization: '优化建议：DNS预解析、使用CDN、减少域名数量'
            },
            tcp: {
                title: 'TCP连接建立',
                icon: '🤝',
                description: 'TCP三次握手建立可靠的网络连接',
                content: [
                    '客户端发送SYN包到服务器',
                    '服务器回复SYN-ACK包',
                    '客户端发送ACK包确认连接',
                    '连接建立完成，可以开始数据传输',
                    '整个握手过程通常需要30ms',
                    'HTTPS还需要额外的TLS握手时间'
                ],
                optimization: '优化建议：Keep-Alive连接复用、HTTP/2多路复用'
            },
            processing: {
                title: '服务器业务处理',
                icon: '💻',
                description: '服务器执行应用逻辑和数据处理',
                content: [
                    '请求路由匹配和参数解析',
                    '用户认证和权限验证',
                    '业务逻辑代码执行',
                    '数据库查询和数据处理',
                    '生成响应数据和格式化',
                    '处理时间取决于业务复杂度，通常100ms'
                ],
                optimization: '优化建议：数据库索引、缓存策略、异步处理、负载均衡'
            },
            parsing: {
                title: 'DOM解析构建',
                icon: '🏗️',
                description: '浏览器解析HTML并构建DOM树',
                content: [
                    '接收HTML字节流并解码',
                    '词法分析生成HTML标记',
                    '语法分析构建DOM树',
                    '处理CSS样式表构建CSSOM',
                    '合并DOM和CSSOM生成渲染树',
                    'DOM解析通常需要80ms'
                ],
                optimization: '优化建议：减少DOM深度、避免阻塞资源、使用流式解析'
            },
            rendering: {
                title: '页面渲染绘制',
                icon: '🎨',
                description: '浏览器将渲染树转换为屏幕像素',
                content: [
                    '布局计算元素位置和大小',
                    '绘制元素的视觉样式',
                    '合成多个图层',
                    '光栅化转换为像素',
                    '显示最终页面内容',
                    '渲染过程通常需要60ms'
                ],
                optimization: '优化建议：避免重排重绘、使用CSS3硬件加速、图片懒加载'
            }
        };

        function showStepDetails(step) {
            const data = stepDetails[step];
            showModal(data);
        }

        function showDetailInfo(type) {
            const data = detailInfo[type];
            showModal(data);
        }

        function showModal(data) {
            const modal = document.getElementById('modal');
            const content = document.getElementById('modal-content');
            
            let html = `
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">${data.icon}</div>
                    <h2 style="font-size: 2.8rem; margin-bottom: 1rem;">${data.title}</h2>
                    <p style="font-size: 1.6rem; opacity: 0.9;">${data.description}</p>
                </div>
            `;
            
            if (data.details) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">详细步骤</h3>
                        <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                            ${data.details.map(detail => `<li style="margin-bottom: 0.5rem;">${detail}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (data.content) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">详细过程</h3>
                        <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                            ${data.content.map(item => `<li style="margin-bottom: 0.5rem;">${item}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (data.example) {
                html += `
                    <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(0,0,0,0.3); border-radius: 15px;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">示例代码</h3>
                        <pre style="font-size: 1.2rem; line-height: 1.4; color: #2ecc71; font-family: 'Courier New', monospace; white-space: pre-wrap;">${data.example}</pre>
                    </div>
                `;
            }
            
            if (data.optimization) {
                html += `
                    <div style="padding: 1.5rem; background: rgba(46, 204, 113, 0.2); border-radius: 15px; border-left: 4px solid #2ecc71;">
                        <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #2ecc71;">💡 ${data.optimization}</h3>
                    </div>
                `;
            }
            
            content.innerHTML = html;
            modal.style.display = 'flex';
            
            // 添加动画效果
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
            
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
            
            // 数字键1-5查看步骤详情
            if (e.key >= '1' && e.key <= '5') {
                showStepDetails(parseInt(e.key));
            }
        });

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 自动播放演示
        let currentStep = 0;
        function autoPlayDemo() {
            const steps = document.querySelectorAll('.step');
            
            // 重置所有步骤
            steps.forEach(step => {
                step.style.transform = 'scale(1)';
                step.style.filter = 'brightness(1)';
            });
            
            // 高亮当前步骤
            if (steps[currentStep]) {
                steps[currentStep].style.transform = 'scale(1.1)';
                steps[currentStep].style.filter = 'brightness(1.2)';
            }
            
            currentStep = (currentStep + 1) % steps.length;
        }

        // 每3秒自动播放一次
        setInterval(autoPlayDemo, 3000);
        
        // 页面加载完成后开始自动播放
        setTimeout(autoPlayDemo, 4000);
    </script>
</body>
</html>
