<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器发展历史 - SVG时间线</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 3rem;
        }

        .timeline-svg {
            width: 100%;
            height: auto;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .browser-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .browser-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid var(--browser-color);
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .browser-card.active {
            opacity: 1;
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .browser-card h3 {
            color: var(--browser-color);
            margin-bottom: 1rem;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .browser-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--browser-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .browser-period {
            background: var(--browser-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .browser-features {
            list-style: none;
            padding: 0;
        }

        .browser-features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .browser-features li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--browser-color);
            font-weight: bold;
        }

        .market-share {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 3rem;
        }

        .share-chart {
            display: flex;
            height: 40px;
            border-radius: 20px;
            overflow: hidden;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .share-segment {
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .share-segment:hover {
            filter: brightness(1.1);
            transform: scaleY(1.1);
        }

        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        /* 浏览器颜色主题 */
        .worldwideweb { --browser-color: #8B4513; }
        .mosaic { --browser-color: #FF6B35; }
        .netscape { --browser-color: #0066CC; }
        .ie { --browser-color: #00BCF2; }
        .firefox { --browser-color: #FF7139; }
        .safari { --browser-color: #1B88CA; }
        .chrome { --browser-color: #4285F4; }
        .edge { --browser-color: #0078D4; }

        /* SVG动画 */
        .browser-node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .browser-node:hover {
            filter: drop-shadow(0 0 10px rgba(0,0,0,0.3));
        }

        .timeline-line {
            stroke-dasharray: 2000;
            stroke-dashoffset: 2000;
            animation: drawLine 3s ease-in-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .browser-appear {
            opacity: 0;
            animation: fadeInScale 0.8s ease-out forwards;
        }

        @keyframes fadeInScale {
            0% {
                opacity: 0;
                transform: scale(0.5);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .war-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .browser-details {
                grid-template-columns: 1fr;
            }
            
            .legend {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>浏览器发展历史</h1>
        <p class="subtitle">从第一个浏览器到现代浏览器大战的完整历程</p>

        <!-- SVG时间线 -->
        <svg class="timeline-svg" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
            <!-- 背景网格 -->
            <defs>
                <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                    <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#f0f0f0" stroke-width="1" opacity="0.5"/>
                </pattern>
                
                <!-- 浏览器图标定义 -->
                <g id="www-icon">
                    <circle r="20" fill="#8B4513"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">W</text>
                </g>
                
                <g id="mosaic-icon">
                    <circle r="20" fill="#FF6B35"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">M</text>
                </g>
                
                <g id="netscape-icon">
                    <circle r="20" fill="#0066CC"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">N</text>
                </g>
                
                <g id="ie-icon">
                    <circle r="20" fill="#00BCF2"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">IE</text>
                </g>
                
                <g id="firefox-icon">
                    <circle r="20" fill="#FF7139"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">🦊</text>
                </g>
                
                <g id="safari-icon">
                    <circle r="20" fill="#1B88CA"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">🧭</text>
                </g>
                
                <g id="chrome-icon">
                    <circle r="20" fill="#4285F4"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">🌐</text>
                </g>
                
                <g id="edge-icon">
                    <circle r="20" fill="#0078D4"/>
                    <text text-anchor="middle" dy="6" fill="white" font-size="12" font-weight="bold">E</text>
                </g>
            </defs>
            
            <!-- 背景 -->
            <rect width="1200" height="600" fill="url(#grid)"/>
            
            <!-- 标题 -->
            <text x="600" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">
                浏览器发展时间线 (1990-2024)
            </text>
            
            <!-- 主时间线 -->
            <line x1="100" y1="100" x2="1100" y2="100" stroke="#667eea" stroke-width="4" class="timeline-line"/>
            
            <!-- 年份标记 -->
            <g font-size="12" fill="#666" text-anchor="middle">
                <text x="150" y="130">1990</text>
                <text x="250" y="130">1993</text>
                <text x="350" y="130">1995</text>
                <text x="450" y="130">2000</text>
                <text x="550" y="130">2003</text>
                <text x="650" y="130">2008</text>
                <text x="750" y="130">2012</text>
                <text x="850" y="130">2015</text>
                <text x="950" y="130">2020</text>
                <text x="1050" y="130">2024</text>
            </g>
            
            <!-- 浏览器节点 -->
            <!-- WorldWideWeb (1990) -->
            <g class="browser-node browser-appear" data-browser="worldwideweb" style="animation-delay: 0.5s;">
                <line x1="150" y1="100" x2="150" y2="180" stroke="#8B4513" stroke-width="3"/>
                <use href="#www-icon" x="150" y="200"/>
                <text x="150" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#8B4513">WorldWideWeb</text>
                <text x="150" y="255" text-anchor="middle" font-size="10" fill="#666">1990</text>
            </g>
            
            <!-- Mosaic (1993) -->
            <g class="browser-node browser-appear" data-browser="mosaic" style="animation-delay: 1s;">
                <line x1="250" y1="100" x2="250" y2="180" stroke="#FF6B35" stroke-width="3"/>
                <use href="#mosaic-icon" x="250" y="200"/>
                <text x="250" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#FF6B35">Mosaic</text>
                <text x="250" y="255" text-anchor="middle" font-size="10" fill="#666">1993</text>
            </g>
            
            <!-- Netscape (1994) -->
            <g class="browser-node browser-appear" data-browser="netscape" style="animation-delay: 1.2s;">
                <line x1="320" y1="100" x2="320" y2="300" stroke="#0066CC" stroke-width="3"/>
                <use href="#netscape-icon" x="320" y="320"/>
                <text x="320" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="#0066CC">Netscape</text>
                <text x="320" y="375" text-anchor="middle" font-size="10" fill="#666">1994-2008</text>
            </g>
            
            <!-- Internet Explorer (1995) -->
            <g class="browser-node browser-appear" data-browser="ie" style="animation-delay: 1.5s;">
                <line x1="380" y1="100" x2="380" y2="180" stroke="#00BCF2" stroke-width="3"/>
                <use href="#ie-icon" x="380" y="200"/>
                <text x="380" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#00BCF2">Internet Explorer</text>
                <text x="380" y="255" text-anchor="middle" font-size="10" fill="#666">1995-2022</text>
            </g>
            
            <!-- 浏览器大战指示器 -->
            <rect x="300" y="400" width="200" height="40" rx="20" fill="#ff4757" opacity="0.8" class="war-indicator"/>
            <text x="400" y="425" text-anchor="middle" font-size="14" font-weight="bold" fill="white">第一次浏览器大战</text>
            
            <!-- Firefox (2004) -->
            <g class="browser-node browser-appear" data-browser="firefox" style="animation-delay: 2s;">
                <line x1="520" y1="100" x2="520" y2="300" stroke="#FF7139" stroke-width="3"/>
                <use href="#firefox-icon" x="520" y="320"/>
                <text x="520" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="#FF7139">Firefox</text>
                <text x="520" y="375" text-anchor="middle" font-size="10" fill="#666">2004-至今</text>
            </g>
            
            <!-- Safari (2003) -->
            <g class="browser-node browser-appear" data-browser="safari" style="animation-delay: 1.8s;">
                <line x1="480" y1="100" x2="480" y2="180" stroke="#1B88CA" stroke-width="3"/>
                <use href="#safari-icon" x="480" y="200"/>
                <text x="480" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#1B88CA">Safari</text>
                <text x="480" y="255" text-anchor="middle" font-size="10" fill="#666">2003-至今</text>
            </g>
            
            <!-- Chrome (2008) -->
            <g class="browser-node browser-appear" data-browser="chrome" style="animation-delay: 2.5s;">
                <line x1="650" y1="100" x2="650" y2="300" stroke="#4285F4" stroke-width="3"/>
                <use href="#chrome-icon" x="650" y="320"/>
                <text x="650" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="#4285F4">Chrome</text>
                <text x="650" y="375" text-anchor="middle" font-size="10" fill="#666">2008-至今</text>
            </g>
            
            <!-- Edge (2015) -->
            <g class="browser-node browser-appear" data-browser="edge" style="animation-delay: 3s;">
                <line x1="850" y1="100" x2="850" y2="180" stroke="#0078D4" stroke-width="3"/>
                <use href="#edge-icon" x="850" y="200"/>
                <text x="850" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#0078D4">Edge</text>
                <text x="850" y="255" text-anchor="middle" font-size="10" fill="#666">2015-至今</text>
            </g>
            
            <!-- 第二次浏览器大战指示器 -->
            <rect x="600" y="400" width="200" height="40" rx="20" fill="#2ed573" opacity="0.8" class="war-indicator"/>
            <text x="700" y="425" text-anchor="middle" font-size="14" font-weight="bold" fill="white">第二次浏览器大战</text>
            
            <!-- 重要事件标记 -->
            <g font-size="10" fill="#666">
                <circle cx="350" cy="480" r="3" fill="#e74c3c"/>
                <text x="360" y="485">JavaScript诞生 (1995)</text>
                
                <circle cx="450" cy="500" r="3" fill="#f39c12"/>
                <text x="460" y="505">AJAX兴起 (2005)</text>
                
                <circle cx="650" cy="480" r="3" fill="#27ae60"/>
                <text x="660" y="485">V8引擎 (2008)</text>
                
                <circle cx="750" cy="500" r="3" fill="#9b59b6"/>
                <text x="760" y="505">HTML5标准 (2014)</text>
            </g>
            
            <!-- 装饰性元素 -->
            <g opacity="0.3">
                <circle cx="1000" cy="500" r="30" fill="#667eea"/>
                <circle cx="1050" cy="450" r="20" fill="#764ba2"/>
                <circle cx="950" cy="480" r="25" fill="#667eea"/>
            </g>
        </svg>

        <!-- 浏览器详细信息卡片 -->
        <div class="browser-details">
            <div class="browser-card worldwideweb" data-browser="worldwideweb">
                <h3>
                    <div class="browser-icon">W</div>
                    WorldWideWeb
                </h3>
                <div class="browser-period">1990-1991</div>
                <p><strong>第一个浏览器</strong> - Tim Berners-Lee创造</p>
                <ul class="browser-features">
                    <li>第一个图形化浏览器</li>
                    <li>同时也是编辑器</li>
                    <li>只能在NeXT系统运行</li>
                    <li>后来改名为Nexus</li>
                </ul>
            </div>

            <div class="browser-card mosaic" data-browser="mosaic">
                <h3>
                    <div class="browser-icon">M</div>
                    Mosaic
                </h3>
                <div class="browser-period">1993-1997</div>
                <p><strong>第一个流行的浏览器</strong> - 让Web走向大众</p>
                <ul class="browser-features">
                    <li>支持图片内嵌显示</li>
                    <li>跨平台支持</li>
                    <li>用户友好的界面</li>
                    <li>推动了Web的普及</li>
                </ul>
            </div>

            <div class="browser-card netscape" data-browser="netscape">
                <h3>
                    <div class="browser-icon">N</div>
                    Netscape Navigator
                </h3>
                <div class="browser-period">1994-2008</div>
                <p><strong>第一次浏览器大战的主角</strong> - 引入JavaScript</p>
                <ul class="browser-features">
                    <li>JavaScript语言诞生</li>
                    <li>SSL安全协议</li>
                    <li>插件系统</li>
                    <li>最高市场份额达80%</li>
                </ul>
            </div>

            <div class="browser-card ie" data-browser="ie">
                <h3>
                    <div class="browser-icon">IE</div>
                    Internet Explorer
                </h3>
                <div class="browser-period">1995-2022</div>
                <p><strong>微软的反击</strong> - 与Windows捆绑销售</p>
                <ul class="browser-features">
                    <li>与Windows系统集成</li>
                    <li>ActiveX技术</li>
                    <li>最高市场份额超90%</li>
                    <li>Web标准兼容性问题</li>
                </ul>
            </div>

            <div class="browser-card firefox" data-browser="firefox">
                <h3>
                    <div class="browser-icon">🦊</div>
                    Mozilla Firefox
                </h3>
                <div class="browser-period">2004-至今</div>
                <p><strong>开源浏览器</strong> - 挑战IE垄断</p>
                <ul class="browser-features">
                    <li>开源免费</li>
                    <li>标签页浏览</li>
                    <li>扩展插件系统</li>
                    <li>注重隐私保护</li>
                </ul>
            </div>

            <div class="browser-card safari" data-browser="safari">
                <h3>
                    <div class="browser-icon">🧭</div>
                    Safari
                </h3>
                <div class="browser-period">2003-至今</div>
                <p><strong>苹果生态</strong> - Mac和iOS默认浏览器</p>
                <ul class="browser-features">
                    <li>WebKit渲染引擎</li>
                    <li>能耗优化</li>
                    <li>隐私保护功能</li>
                    <li>与苹果设备深度集成</li>
                </ul>
            </div>

            <div class="browser-card chrome" data-browser="chrome">
                <h3>
                    <div class="browser-icon">🌐</div>
                    Google Chrome
                </h3>
                <div class="browser-period">2008-至今</div>
                <p><strong>性能革命</strong> - V8引擎改变游戏规则</p>
                <ul class="browser-features">
                    <li>V8 JavaScript引擎</li>
                    <li>多进程架构</li>
                    <li>快速启动和运行</li>
                    <li>目前市场份额第一</li>
                </ul>
            </div>

            <div class="browser-card edge" data-browser="edge">
                <h3>
                    <div class="browser-icon">E</div>
                    Microsoft Edge
                </h3>
                <div class="browser-period">2015-至今</div>
                <p><strong>微软的新开始</strong> - 基于Chromium重构</p>
                <ul class="browser-features">
                    <li>替代Internet Explorer</li>
                    <li>2020年转向Chromium</li>
                    <li>集成Microsoft服务</li>
                    <li>注重企业用户</li>
                </ul>
            </div>
        </div>

        <!-- 市场份额图表 -->
        <div class="market-share">
            <h3 style="color: #2c3e50; margin-bottom: 1rem;">📊 当前浏览器市场份额 (2024)</h3>
            <div class="share-chart">
                <div class="share-segment" style="background-color: #4285F4; width: 65%;">Chrome 65%</div>
                <div class="share-segment" style="background-color: #1B88CA; width: 18%;">Safari 18%</div>
                <div class="share-segment" style="background-color: #0078D4; width: 11%;">Edge 11%</div>
                <div class="share-segment" style="background-color: #FF7139; width: 4%;">Firefox 4%</div>
                <div class="share-segment" style="background-color: #666; width: 2%;">其他 2%</div>
            </div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4285F4;"></div>
                    <span>Chrome - 谷歌浏览器</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #1B88CA;"></div>
                    <span>Safari - 苹果浏览器</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #0078D4;"></div>
                    <span>Edge - 微软浏览器</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #FF7139;"></div>
                    <span>Firefox - 火狐浏览器</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #666;"></div>
                    <span>其他浏览器</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // SVG交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const browserNodes = document.querySelectorAll('.browser-node');
            const browserCards = document.querySelectorAll('.browser-card');

            // 点击SVG节点高亮对应卡片
            browserNodes.forEach(node => {
                node.addEventListener('click', function() {
                    const browserType = this.getAttribute('data-browser');
                    
                    // 移除所有active状态
                    browserCards.forEach(card => card.classList.remove('active'));
                    
                    // 激活对应卡片
                    const targetCard = document.querySelector(`.browser-card[data-browser="${browserType}"]`);
                    if (targetCard) {
                        targetCard.classList.add('active');
                        targetCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });

                // 悬停效果
                node.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });

                node.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 卡片点击效果
            browserCards.forEach(card => {
                card.addEventListener('click', function() {
                    browserCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 自动播放演示
            let currentIndex = 0;
            const browsers = ['worldwideweb', 'mosaic', 'netscape', 'ie', 'firefox', 'safari', 'chrome', 'edge'];
            
            function autoHighlight() {
                // 移除所有active状态
                browserCards.forEach(card => card.classList.remove('active'));
                
                // 激活当前浏览器
                const currentBrowser = browsers[currentIndex];
                const targetCard = document.querySelector(`.browser-card[data-browser="${currentBrowser}"]`);
                if (targetCard) {
                    targetCard.classList.add('active');
                }
                
                currentIndex = (currentIndex + 1) % browsers.length;
            }

            // 每5秒自动切换一次
            setInterval(autoHighlight, 5000);
            
            // 初始化第一个
            autoHighlight();
        });
    </script>
</body>
</html>
