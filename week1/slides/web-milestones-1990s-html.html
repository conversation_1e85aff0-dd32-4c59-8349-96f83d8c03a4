<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web关键里程碑 (1990-1999)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        .main-title {
            font-size: 4.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 2.2rem;
            opacity: 0.9;
            font-weight: 300;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .timeline-container {
            width: 100%;
            max-width: 1400px;
            position: relative;
            margin-bottom: 2rem;
        }

        .timeline-line {
            position: absolute;
            top: 50%;
            left: 5%;
            right: 5%;
            height: 6px;
            background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8), #fff);
            border-radius: 3px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: drawLine 2s ease-out 1s both;
        }

        .milestones-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            width: 100%;
            max-width: 1400px;
            margin-top: 4rem;
            animation: fadeInUp 1s ease-out 1.5s both;
        }

        .milestone-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 2rem;
            text-align: center;
            border: 3px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .milestone-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .milestone-card:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .milestone-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .milestone-year {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: pulse 2s infinite;
        }

        .milestone-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
            animation: bounce 2s infinite;
        }

        .milestone-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .milestone-description {
            font-size: 1.3rem;
            opacity: 0.9;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        .milestone-impact {
            font-size: 1.1rem;
            font-style: italic;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem;
            border-radius: 15px;
            margin-top: 1rem;
        }

        /* 特定里程碑的颜色主题 */
        .html-card {
            border-color: #e74c3c;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
        }

        .html-card:hover {
            border-color: #e74c3c;
            box-shadow: 0 25px 50px rgba(231, 76, 60, 0.3);
        }

        .mosaic-card {
            border-color: #f39c12;
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.2), rgba(211, 84, 0, 0.2));
        }

        .mosaic-card:hover {
            border-color: #f39c12;
            box-shadow: 0 25px 50px rgba(243, 156, 18, 0.3);
        }

        .css-card {
            border-color: #3498db;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
        }

        .css-card:hover {
            border-color: #3498db;
            box-shadow: 0 25px 50px rgba(52, 152, 219, 0.3);
        }

        .js-card {
            border-color: #f1c40f;
            background: linear-gradient(135deg, rgba(241, 196, 15, 0.2), rgba(243, 156, 18, 0.2));
        }

        .js-card:hover {
            border-color: #f1c40f;
            box-shadow: 0 25px 50px rgba(241, 196, 15, 0.3);
        }

        .summary {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem 3rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-out 2.5s both;
            max-width: 1200px;
        }

        .summary-text {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            line-height: 1.4;
        }

        .highlight {
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        /* 动画定义 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes drawLine {
            from {
                width: 0;
            }
            to {
                width: 90%;
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-title { font-size: 3.5rem; }
            .subtitle { font-size: 1.8rem; }
            .milestones-grid { grid-template-columns: repeat(2, 1fr); }
            .milestone-year { font-size: 2.5rem; }
            .milestone-title { font-size: 1.8rem; }
            .summary-text { font-size: 1.8rem; }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2.8rem; }
            .subtitle { font-size: 1.5rem; }
            .milestones-grid { grid-template-columns: 1fr; gap: 1.5rem; }
            .milestone-card { padding: 1.5rem; }
            .milestone-year { font-size: 2rem; }
            .milestone-icon { font-size: 3rem; }
            .milestone-title { font-size: 1.5rem; }
            .summary-text { font-size: 1.5rem; }
        }

        /* 交互提示 */
        .interaction-hint {
            position: absolute;
            top: 2rem;
            right: 2rem;
            font-size: 1.3rem;
            opacity: 0.7;
            animation: fadeIn 2s ease-out 3s both;
        }

        /* 装饰性元素 */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 装饰性粒子 -->
        <div class="floating-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>

        <!-- 交互提示 -->
        <div class="interaction-hint">
            💡 点击里程碑卡片查看详细信息
        </div>

        <!-- 标题区域 -->
        <div class="header">
            <h1 class="main-title">Web关键里程碑</h1>
            <p class="subtitle">1990-1999：万维网的诞生与发展</p>
        </div>

        <!-- 时间线 -->
        <div class="timeline-container">
            <div class="timeline-line"></div>
        </div>

        <!-- 里程碑网格 -->
        <div class="milestones-grid">
            <div class="milestone-card html-card" onclick="showDetails('html')">
                <div class="milestone-year">1990</div>
                <div class="milestone-icon">📄</div>
                <div class="milestone-title">HTML诞生</div>
                <div class="milestone-description">
                    Tim Berners-Lee创建<br>
                    超文本标记语言
                </div>
                <div class="milestone-impact">
                    定义了网页的基本结构
                </div>
            </div>

            <div class="milestone-card mosaic-card" onclick="showDetails('mosaic')">
                <div class="milestone-year">1993</div>
                <div class="milestone-icon">🌐</div>
                <div class="milestone-title">Mosaic浏览器</div>
                <div class="milestone-description">
                    第一个图形化浏览器<br>
                    支持图片显示
                </div>
                <div class="milestone-impact">
                    让Web走向大众化
                </div>
            </div>

            <div class="milestone-card css-card" onclick="showDetails('css')">
                <div class="milestone-year">1996</div>
                <div class="milestone-icon">🎨</div>
                <div class="milestone-title">CSS引入</div>
                <div class="milestone-description">
                    层叠样式表<br>
                    内容与样式分离
                </div>
                <div class="milestone-impact">
                    网页设计革命性突破
                </div>
            </div>

            <div class="milestone-card js-card" onclick="showDetails('js')">
                <div class="milestone-year">1995</div>
                <div class="milestone-icon">⚡</div>
                <div class="milestone-title">JavaScript诞生</div>
                <div class="milestone-description">
                    Brendan Eich创造<br>
                    仅用10天完成
                </div>
                <div class="milestone-impact">
                    为网页带来交互性
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary">
            <div class="summary-text">
                🏗️ 这十年奠定了现代Web的<span class="highlight">三大基石</span>：<br>
                <span class="highlight">HTML</span>(结构) + <span class="highlight">CSS</span>(样式) + <span class="highlight">JavaScript</span>(行为)
            </div>
        </div>
    </div>

    <!-- 详细信息模态框 -->
    <div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
        <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 3rem; border-radius: 25px; max-width: 900px; width: 90%; color: white; position: relative; max-height: 80vh; overflow-y: auto;">
            <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 2.5rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
            <div id="modal-content"></div>
        </div>
    </div>

    <script>
        // 详细信息数据
        const detailsData = {
            html: {
                title: 'HTML的诞生 (1990)',
                icon: '📄',
                creator: 'Tim Berners-Lee',
                background: '在CERN工作期间，为了解决信息共享问题而创建',
                features: [
                    '超文本标记语言 (HyperText Markup Language)',
                    '使用标签定义文档结构',
                    '支持超链接连接不同文档',
                    '第一个版本非常简单，只有18个标签',
                    '奠定了现代网页的基础结构'
                ],
                impact: 'HTML的创建标志着万维网的正式诞生，为后续所有Web技术发展奠定了基础。',
                legacy: '至今仍是所有网页的基础，HTML5是当前的标准版本。'
            },
            mosaic: {
                title: 'Mosaic浏览器 (1993)',
                icon: '🌐',
                creator: 'Marc Andreessen & Eric Bina',
                background: '在伊利诺伊大学国家超级计算应用中心开发',
                features: [
                    '第一个真正的图形化Web浏览器',
                    '支持图片与文本混合显示',
                    '跨平台支持 (Unix, Windows, Mac)',
                    '用户友好的图形界面',
                    '免费提供给学术和非商业用户'
                ],
                impact: 'Mosaic让Web从学术工具变成大众媒体，引发了互联网的第一次爆炸式增长。',
                legacy: 'Marc Andreessen后来创立了Netscape，推动了第一次浏览器大战。'
            },
            css: {
                title: 'CSS的引入 (1996)',
                icon: '🎨',
                creator: 'Håkon Wium Lie & Bert Bos',
                background: 'W3C制定的样式表标准，解决HTML样式混乱问题',
                features: [
                    '层叠样式表 (Cascading Style Sheets)',
                    '实现内容与表现的分离',
                    '支持层叠和继承机制',
                    '提供丰富的样式控制选项',
                    '大大简化了网页设计工作'
                ],
                impact: 'CSS革命性地改变了网页设计，让设计师能够创造出美观、一致的网页外观。',
                legacy: 'CSS3是当前标准，持续演进中，支持动画、响应式设计等现代特性。'
            },
            js: {
                title: 'JavaScript的诞生 (1995)',
                icon: '⚡',
                creator: 'Brendan Eich (Netscape)',
                background: '为了给网页添加交互性，在极短时间内创造的脚本语言',
                features: [
                    '仅用10天时间设计和实现',
                    '最初名为LiveScript，后改名JavaScript',
                    '客户端脚本语言',
                    '支持动态类型和函数式编程',
                    '能够操作DOM和响应用户事件'
                ],
                impact: 'JavaScript让静态网页变成了动态交互应用，开启了现代Web应用的时代。',
                legacy: '现在是世界上最流行的编程语言之一，不仅用于前端，也广泛用于后端开发。'
            }
        };

        function showDetails(milestone) {
            const data = detailsData[milestone];
            const modal = document.getElementById('modal');
            const content = document.getElementById('modal-content');
            
            let html = `
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">${data.icon}</div>
                    <h2 style="font-size: 2.8rem; margin-bottom: 1rem;">${data.title}</h2>
                    <p style="font-size: 1.6rem; opacity: 0.9;">创造者：${data.creator}</p>
                </div>
                
                <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">背景</h3>
                    <p style="font-size: 1.4rem; line-height: 1.5;">${data.background}</p>
                </div>
                
                <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">主要特点</h3>
                    <ul style="font-size: 1.3rem; line-height: 1.6; padding-left: 1.5rem;">
                        ${data.features.map(feature => `<li style="margin-bottom: 0.5rem;">${feature}</li>`).join('')}
                    </ul>
                </div>
                
                <div style="margin-bottom: 2rem; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">历史影响</h3>
                    <p style="font-size: 1.4rem; line-height: 1.5;">${data.impact}</p>
                </div>
                
                <div style="padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3 style="font-size: 1.8rem; margin-bottom: 1rem; color: #f1c40f;">现代传承</h3>
                    <p style="font-size: 1.4rem; line-height: 1.5;">${data.legacy}</p>
                </div>
            `;
            
            content.innerHTML = html;
            modal.style.display = 'flex';
            
            // 添加动画效果
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
            
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
            
            // 数字键快速查看
            const milestoneMap = {
                '1': 'html',
                '2': 'mosaic',
                '3': 'css',
                '4': 'js'
            };
            
            if (milestoneMap[e.key]) {
                showDetails(milestoneMap[e.key]);
            }
        });

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为里程碑卡片添加随机延迟动画
            const cards = document.querySelectorAll('.milestone-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${1.5 + index * 0.2}s`;
                card.style.animation = 'fadeInUp 1s ease-out both';
            });
            
            // 添加鼠标移动视差效果
            document.addEventListener('mousemove', function(e) {
                const cards = document.querySelectorAll('.milestone-card');
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;
                
                cards.forEach((card, index) => {
                    const intensity = (index + 1) * 2;
                    const x = (mouseX - 0.5) * intensity;
                    const y = (mouseY - 0.5) * intensity;
                    
                    card.style.transform += ` translate(${x}px, ${y}px)`;
                });
            });
        });
    </script>
</body>
</html>
