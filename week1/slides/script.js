// 幻灯片控制脚本
class SlideShow {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = document.querySelectorAll('.slide').length;
        this.slides = document.querySelectorAll('.slide');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.slideCounter = document.getElementById('slideCounter');
        this.progress = document.getElementById('progress');
        
        this.init();
    }
    
    init() {
        this.updateSlideCounter();
        this.updateProgress();
        this.updateButtons();
        this.addKeyboardListeners();
        this.addTouchListeners();
    }
    
    // 切换幻灯片
    changeSlide(direction) {
        const newSlide = this.currentSlide + direction;
        
        if (newSlide < 1 || newSlide > this.totalSlides) {
            return;
        }
        
        // 移除当前活动状态
        this.slides[this.currentSlide - 1].classList.remove('active');
        
        // 添加过渡效果
        if (direction > 0) {
            this.slides[this.currentSlide - 1].classList.add('prev');
        } else {
            this.slides[this.currentSlide - 1].classList.remove('prev');
        }
        
        // 更新当前幻灯片
        this.currentSlide = newSlide;
        
        // 激活新幻灯片
        setTimeout(() => {
            this.slides[this.currentSlide - 1].classList.add('active');
            if (direction < 0) {
                this.slides[this.currentSlide].classList.remove('prev');
            }
        }, 50);
        
        this.updateSlideCounter();
        this.updateProgress();
        this.updateButtons();
        this.addSlideAnimation();
    }
    
    // 跳转到指定幻灯片
    goToSlide(slideNumber) {
        if (slideNumber < 1 || slideNumber > this.totalSlides || slideNumber === this.currentSlide) {
            return;
        }
        
        const direction = slideNumber > this.currentSlide ? 1 : -1;
        const steps = Math.abs(slideNumber - this.currentSlide);
        
        for (let i = 0; i < steps; i++) {
            setTimeout(() => {
                this.changeSlide(direction);
            }, i * 100);
        }
    }
    
    // 更新幻灯片计数器
    updateSlideCounter() {
        this.slideCounter.textContent = `${this.currentSlide} / ${this.totalSlides}`;
    }
    
    // 更新进度条
    updateProgress() {
        const progressPercent = (this.currentSlide / this.totalSlides) * 100;
        this.progress.style.width = `${progressPercent}%`;
    }
    
    // 更新按钮状态
    updateButtons() {
        this.prevBtn.disabled = this.currentSlide === 1;
        this.nextBtn.disabled = this.currentSlide === this.totalSlides;
    }
    
    // 添加键盘监听
    addKeyboardListeners() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    this.changeSlide(-1);
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    this.changeSlide(1);
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToSlide(this.totalSlides);
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
    }
    
    // 添加触摸监听（移动端支持）
    addTouchListeners() {
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // 判断是否为有效滑动
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // 向左滑动，下一张
                    this.changeSlide(1);
                } else {
                    // 向右滑动，上一张
                    this.changeSlide(-1);
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }
    
    // 添加幻灯片动画效果
    addSlideAnimation() {
        const currentSlideElement = this.slides[this.currentSlide - 1];
        const animatableElements = currentSlideElement.querySelectorAll('h1, h2, h3, p, li, .timeline-item, .component');
        
        animatableElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.6s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    // 全屏切换
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // 自动播放功能
    startAutoPlay(interval = 10000) {
        this.autoPlayInterval = setInterval(() => {
            if (this.currentSlide < this.totalSlides) {
                this.changeSlide(1);
            } else {
                this.stopAutoPlay();
            }
        }, interval);
    }
    
    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

// 全局函数（保持向后兼容）
function changeSlide(direction) {
    slideShow.changeSlide(direction);
}

// 初始化幻灯片
let slideShow;

document.addEventListener('DOMContentLoaded', () => {
    slideShow = new SlideShow();
    
    // 添加额外的交互功能
    addInteractiveFeatures();
});

// 添加交互功能
function addInteractiveFeatures() {
    // 添加幻灯片点击切换
    document.addEventListener('click', (e) => {
        if (e.target.closest('.navigation') || e.target.closest('button')) {
            return;
        }
        
        const clickX = e.clientX;
        const windowWidth = window.innerWidth;
        
        if (clickX < windowWidth / 2) {
            slideShow.changeSlide(-1);
        } else {
            slideShow.changeSlide(1);
        }
    });
    
    // 添加代码高亮效果
    highlightCode();
    
    // 添加时间线动画
    animateTimeline();
}

// 代码高亮
function highlightCode() {
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
        // 简单的语法高亮
        let html = block.innerHTML;
        
        // HTML标签高亮
        html = html.replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g, 
            '$1<span style="color: #e74c3c;">$2</span>$3$4');
        
        // 属性高亮
        html = html.replace(/([a-zA-Z-]+)(=)(".*?")/g, 
            '<span style="color: #f39c12;">$1</span>$2<span style="color: #27ae60;">$3</span>');
        
        // HTTP方法高亮
        html = html.replace(/(GET|POST|PUT|DELETE|HTTP\/1\.1)/g, 
            '<span style="color: #3498db; font-weight: bold;">$1</span>');
        
        block.innerHTML = html;
    });
}

// 时间线动画
function animateTimeline() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const timelineItems = entry.target.querySelectorAll('.timeline-item');
                timelineItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, index * 200);
                });
            }
        });
    });
    
    const timelines = document.querySelectorAll('.timeline');
    timelines.forEach(timeline => {
        const items = timeline.querySelectorAll('.timeline-item');
        items.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            item.style.transition = 'all 0.6s ease';
        });
        observer.observe(timeline);
    });
}

// 工具函数：显示提示信息
function showToast(message, duration = 3000) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 1rem 2rem;
        border-radius: 5px;
        z-index: 10000;
        font-size: 1rem;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, duration);
}

// 添加快捷键提示
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        showToast('提示：使用方向键或空格键切换幻灯片，ESC键切换全屏', 5000);
    }, 2000);
});
