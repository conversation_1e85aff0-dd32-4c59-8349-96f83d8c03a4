<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web关键里程碑 (1990-1999)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fullscreen-svg {
            width: 100vw;
            height: 100vh;
            display: block;
        }

        /* SVG动画样式 */
        .milestone-item {
            opacity: 0;
            transform: translateY(50px);
        }

        .milestone-item.animate {
            opacity: 1;
            transform: translateY(0);
            transition: all 1s ease-out;
        }

        .timeline-dot {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .timeline-dot:hover {
            transform: scale(1.2);
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.8));
        }

        .tech-icon {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tech-icon:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 0 15px rgba(0,0,0,0.3));
        }

        .floating-particle {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .draw-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-in-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease-in forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-in-left {
            transform: translateX(-100px);
            opacity: 0;
            animation: slideInLeft 1s ease-out forwards;
        }

        @keyframes slideInLeft {
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .slide-in-right {
            transform: translateX(100px);
            opacity: 0;
            animation: slideInRight 1s ease-out forwards;
        }

        @keyframes slideInRight {
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .zoom-in {
            transform: scale(0);
            opacity: 0;
            animation: zoomIn 0.8s ease-out forwards;
        }

        @keyframes zoomIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <svg class="fullscreen-svg" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
        <!-- 背景渐变 -->
        <defs>
            <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#667eea"/>
                <stop offset="50%" style="stop-color:#764ba2"/>
                <stop offset="100%" style="stop-color:#667eea"/>
            </linearGradient>
            
            <radialGradient id="glowEffect" cx="50%" cy="50%" r="50%">
                <stop offset="0%" style="stop-color:rgba(255,255,255,0.3)"/>
                <stop offset="100%" style="stop-color:rgba(255,255,255,0)"/>
            </radialGradient>
            
            <!-- 技术图标定义 -->
            <g id="html-icon">
                <rect width="80" height="100" rx="8" fill="#e34f26"/>
                <polygon points="20,20 60,20 55,70 40,75 25,70" fill="white"/>
                <text x="40" y="90" text-anchor="middle" font-size="12" font-weight="bold" fill="white">HTML</text>
            </g>
            
            <g id="mosaic-icon">
                <circle r="50" fill="#ff6b35"/>
                <rect x="-30" y="-20" width="60" height="40" rx="5" fill="white"/>
                <circle cx="-15" cy="-5" r="3" fill="#ff6b35"/>
                <circle cx="0" cy="-5" r="3" fill="#ff6b35"/>
                <circle cx="15" cy="-5" r="3" fill="#ff6b35"/>
                <rect x="-25" y="5" width="50" height="3" fill="#ff6b35"/>
                <rect x="-25" y="12" width="35" height="3" fill="#ff6b35"/>
                <text x="0" y="35" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Mosaic</text>
            </g>
            
            <g id="ie-icon">
                <circle r="50" fill="#00bcf2"/>
                <path d="M-30,-10 Q0,-30 30,-10 Q30,10 0,30 Q-30,10 -30,-10" fill="white"/>
                <text x="0" y="5" text-anchor="middle" font-size="16" font-weight="bold" fill="#00bcf2">e</text>
                <text x="0" y="35" text-anchor="middle" font-size="10" font-weight="bold" fill="white">IE</text>
            </g>
            
            <g id="css-icon">
                <rect width="80" height="100" rx="8" fill="#1572b6"/>
                <polygon points="20,20 60,20 55,70 40,75 25,70" fill="white"/>
                <rect x="30" y="30" width="20" height="3" fill="#1572b6"/>
                <rect x="30" y="40" width="15" height="3" fill="#1572b6"/>
                <rect x="30" y="50" width="18" height="3" fill="#1572b6"/>
                <text x="40" y="90" text-anchor="middle" font-size="12" font-weight="bold" fill="white">CSS</text>
            </g>
            
            <g id="js-icon">
                <rect width="80" height="100" rx="8" fill="#f7df1e"/>
                <text x="40" y="55" text-anchor="middle" font-size="36" font-weight="bold" fill="#323330">JS</text>
                <text x="40" y="90" text-anchor="middle" font-size="12" font-weight="bold" fill="#323330">JavaScript</text>
            </g>
            
            <!-- 装饰性粒子 -->
            <circle id="particle" r="2" fill="rgba(255,255,255,0.6)"/>
        </defs>
        
        <!-- 背景 -->
        <rect width="1920" height="1080" fill="url(#bgGradient)"/>
        
        <!-- 装饰性背景元素 -->
        <circle cx="200" cy="200" r="100" fill="url(#glowEffect)" class="floating-particle"/>
        <circle cx="1720" cy="300" r="80" fill="url(#glowEffect)" class="floating-particle" style="animation-delay: -2s"/>
        <circle cx="300" cy="800" r="60" fill="url(#glowEffect)" class="floating-particle" style="animation-delay: -4s"/>
        <circle cx="1600" cy="900" r="90" fill="url(#glowEffect)" class="floating-particle" style="animation-delay: -1s"/>
        
        <!-- 标题 -->
        <g class="fade-in" style="animation-delay: 0.5s">
            <text x="960" y="120" text-anchor="middle" font-size="64" font-weight="bold" fill="white">
                Web关键里程碑
            </text>
            <text x="960" y="180" text-anchor="middle" font-size="36" fill="rgba(255,255,255,0.9)">
                1990-1999：万维网的诞生与发展
            </text>
        </g>
        
        <!-- 主时间线 -->
        <line x1="200" y1="540" x2="1720" y2="540" stroke="rgba(255,255,255,0.8)" stroke-width="6" class="draw-line" style="animation-delay: 1s"/>
        
        <!-- 年份标记 -->
        <g class="fade-in" style="animation-delay: 2s">
            <text x="200" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="white">1990</text>
            <text x="580" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="white">1993</text>
            <text x="960" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="white">1995</text>
            <text x="1340" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="white">1996</text>
            <text x="1720" y="580" text-anchor="middle" font-size="24" font-weight="bold" fill="white">1999</text>
        </g>
        
        <!-- 里程碑1: HTML诞生 (1990) -->
        <g class="milestone-item slide-in-left" style="animation-delay: 2.5s">
            <line x1="200" y1="540" x2="200" y2="350" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
            <circle cx="200" cy="540" r="12" fill="#e34f26" class="timeline-dot pulse"/>
            
            <g transform="translate(200, 280)">
                <use href="#html-icon" transform="translate(-40, -50)"/>
                <rect x="-120" y="60" width="240" height="120" rx="15" fill="rgba(255,255,255,0.95)" stroke="#e34f26" stroke-width="3"/>
                <text x="0" y="85" text-anchor="middle" font-size="20" font-weight="bold" fill="#e34f26">HTML诞生</text>
                <text x="0" y="110" text-anchor="middle" font-size="14" fill="#333">Tim Berners-Lee创建</text>
                <text x="0" y="130" text-anchor="middle" font-size="14" fill="#333">超文本标记语言</text>
                <text x="0" y="150" text-anchor="middle" font-size="14" fill="#333">定义网页结构</text>
                <text x="0" y="170" text-anchor="middle" font-size="12" fill="#666">1990年</text>
            </g>
        </g>
        
        <!-- 里程碑2: Mosaic浏览器 (1993) -->
        <g class="milestone-item zoom-in" style="animation-delay: 3s">
            <line x1="580" y1="540" x2="580" y2="700" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
            <circle cx="580" cy="540" r="12" fill="#ff6b35" class="timeline-dot pulse" style="animation-delay: 0.5s"/>
            
            <g transform="translate(580, 780)">
                <use href="#mosaic-icon" transform="translate(0, -50)"/>
                <rect x="-120" y="20" width="240" height="120" rx="15" fill="rgba(255,255,255,0.95)" stroke="#ff6b35" stroke-width="3"/>
                <text x="0" y="45" text-anchor="middle" font-size="20" font-weight="bold" fill="#ff6b35">Mosaic浏览器</text>
                <text x="0" y="70" text-anchor="middle" font-size="14" fill="#333">第一个图形化浏览器</text>
                <text x="0" y="90" text-anchor="middle" font-size="14" fill="#333">支持图片显示</text>
                <text x="0" y="110" text-anchor="middle" font-size="14" fill="#333">推动Web普及</text>
                <text x="0" y="130" text-anchor="middle" font-size="12" fill="#666">1993年</text>
            </g>
        </g>
        
        <!-- 里程碑3: Internet Explorer (1995) -->
        <g class="milestone-item slide-in-right" style="animation-delay: 3.5s">
            <line x1="960" y1="540" x2="960" y2="350" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
            <circle cx="960" cy="540" r="12" fill="#00bcf2" class="timeline-dot pulse" style="animation-delay: 1s"/>
            
            <g transform="translate(960, 280)">
                <use href="#ie-icon" transform="translate(0, -50)"/>
                <rect x="-120" y="20" width="240" height="120" rx="15" fill="rgba(255,255,255,0.95)" stroke="#00bcf2" stroke-width="3"/>
                <text x="0" y="45" text-anchor="middle" font-size="20" font-weight="bold" fill="#00bcf2">Internet Explorer</text>
                <text x="0" y="70" text-anchor="middle" font-size="14" fill="#333">微软浏览器</text>
                <text x="0" y="90" text-anchor="middle" font-size="14" fill="#333">与Windows捆绑</text>
                <text x="0" y="110" text-anchor="middle" font-size="14" fill="#333">浏览器大战开始</text>
                <text x="0" y="130" text-anchor="middle" font-size="12" fill="#666">1995年</text>
            </g>
        </g>
        
        <!-- 里程碑4: CSS引入 (1996) -->
        <g class="milestone-item zoom-in" style="animation-delay: 4s">
            <line x1="1340" y1="540" x2="1340" y2="700" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
            <circle cx="1340" cy="540" r="12" fill="#1572b6" class="timeline-dot pulse" style="animation-delay: 1.5s"/>
            
            <g transform="translate(1340, 780)">
                <use href="#css-icon" transform="translate(-40, -50)"/>
                <rect x="-120" y="20" width="240" height="120" rx="15" fill="rgba(255,255,255,0.95)" stroke="#1572b6" stroke-width="3"/>
                <text x="0" y="45" text-anchor="middle" font-size="20" font-weight="bold" fill="#1572b6">CSS引入</text>
                <text x="0" y="70" text-anchor="middle" font-size="14" fill="#333">层叠样式表</text>
                <text x="0" y="90" text-anchor="middle" font-size="14" fill="#333">内容与样式分离</text>
                <text x="0" y="110" text-anchor="middle" font-size="14" fill="#333">网页设计革命</text>
                <text x="0" y="130" text-anchor="middle" font-size="12" fill="#666">1996年</text>
            </g>
        </g>
        
        <!-- 里程碑5: JavaScript诞生 (1995) -->
        <g class="milestone-item slide-in-left" style="animation-delay: 4.5s">
            <line x1="1720" y1="540" x2="1720" y2="350" stroke="rgba(255,255,255,0.6)" stroke-width="3"/>
            <circle cx="1720" cy="540" r="12" fill="#f7df1e" class="timeline-dot pulse" style="animation-delay: 2s"/>
            
            <g transform="translate(1720, 280)">
                <use href="#js-icon" transform="translate(-40, -50)"/>
                <rect x="-120" y="20" width="240" height="120" rx="15" fill="rgba(255,255,255,0.95)" stroke="#f7df1e" stroke-width="3"/>
                <text x="0" y="45" text-anchor="middle" font-size="20" font-weight="bold" fill="#f7df1e" stroke="#323330" stroke-width="1">JavaScript</text>
                <text x="0" y="70" text-anchor="middle" font-size="14" fill="#333">Brendan Eich创造</text>
                <text x="0" y="90" text-anchor="middle" font-size="14" fill="#333">仅用10天完成</text>
                <text x="0" y="110" text-anchor="middle" font-size="14" fill="#333">网页交互革命</text>
                <text x="0" y="130" text-anchor="middle" font-size="12" fill="#666">1995年</text>
            </g>
        </g>
        
        <!-- 连接线动画 -->
        <g class="fade-in" style="animation-delay: 5s">
            <path d="M 200 350 Q 390 250 580 350" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none" stroke-dasharray="5,5">
                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
            </path>
            <path d="M 580 700 Q 770 800 960 700" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none" stroke-dasharray="5,5">
                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
            </path>
            <path d="M 960 350 Q 1150 250 1340 350" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none" stroke-dasharray="5,5">
                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
            </path>
            <path d="M 1340 700 Q 1530 600 1720 700" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none" stroke-dasharray="5,5">
                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
            </path>
        </g>
        
        <!-- 底部总结 -->
        <g class="fade-in" style="animation-delay: 5.5s">
            <rect x="460" y="950" width="1000" height="100" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
            <text x="960" y="985" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                这十年奠定了现代Web的三大基石
            </text>
            <text x="960" y="1015" text-anchor="middle" font-size="18" fill="rgba(255,255,255,0.9)">
                HTML(结构) + CSS(样式) + JavaScript(行为) = 现代Web开发基础
            </text>
        </g>
        
        <!-- 装饰性粒子动画 -->
        <g class="fade-in" style="animation-delay: 6s">
            <use href="#particle" x="300" y="400" class="floating-particle"/>
            <use href="#particle" x="700" y="300" class="floating-particle" style="animation-delay: -1s"/>
            <use href="#particle" x="1100" y="450" class="floating-particle" style="animation-delay: -2s"/>
            <use href="#particle" x="1500" y="350" class="floating-particle" style="animation-delay: -3s"/>
            <use href="#particle" x="500" y="800" class="floating-particle" style="animation-delay: -4s"/>
            <use href="#particle" x="900" y="750" class="floating-particle" style="animation-delay: -5s"/>
            <use href="#particle" x="1300" y="850" class="floating-particle" style="animation-delay: -6s"/>
        </g>
        
        <!-- 交互提示 -->
        <g class="fade-in" style="animation-delay: 7s">
            <text x="960" y="50" text-anchor="middle" font-size="16" fill="rgba(255,255,255,0.7)">
                💡 点击时间线上的圆点可查看详细信息
            </text>
        </g>
    </svg>

    <script>
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const timelineDots = document.querySelectorAll('.timeline-dot');
            const milestoneItems = document.querySelectorAll('.milestone-item');
            
            // 为时间线圆点添加点击事件
            timelineDots.forEach((dot, index) => {
                dot.addEventListener('click', function() {
                    // 重置所有里程碑的高亮状态
                    milestoneItems.forEach(item => {
                        item.style.filter = 'brightness(0.7)';
                    });
                    
                    // 高亮当前里程碑
                    if (milestoneItems[index]) {
                        milestoneItems[index].style.filter = 'brightness(1.2) drop-shadow(0 0 20px rgba(255,255,255,0.5))';
                        
                        // 3秒后恢复正常
                        setTimeout(() => {
                            milestoneItems.forEach(item => {
                                item.style.filter = '';
                            });
                        }, 3000);
                    }
                });
                
                // 悬停效果
                dot.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.3)';
                    this.style.filter = 'drop-shadow(0 0 15px rgba(255,255,255,0.8))';
                });
                
                dot.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.filter = '';
                });
            });
            
            // 技术图标悬停效果
            const techIcons = document.querySelectorAll('.tech-icon');
            techIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.15)';
                    this.style.filter = 'drop-shadow(0 0 20px rgba(0,0,0,0.4))';
                });
                
                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.filter = '';
                });
            });
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (e.key === 'r' || e.key === 'R') {
                    // R键重新播放动画
                    location.reload();
                }
                
                if (e.key >= '1' && e.key <= '5') {
                    // 数字键1-5快速跳转到对应里程碑
                    const index = parseInt(e.key) - 1;
                    if (timelineDots[index]) {
                        timelineDots[index].click();
                    }
                }
            });
            
            // 自动播放演示
            let autoPlayIndex = 0;
            function autoPlay() {
                if (autoPlayIndex < timelineDots.length) {
                    timelineDots[autoPlayIndex].click();
                    autoPlayIndex++;
                    setTimeout(autoPlay, 4000); // 每4秒切换一个
                } else {
                    // 重置
                    setTimeout(() => {
                        autoPlayIndex = 0;
                        milestoneItems.forEach(item => {
                            item.style.filter = '';
                        });
                    }, 2000);
                }
            }
            
            // 10秒后开始自动播放
            setTimeout(autoPlay, 10000);
        });
    </script>
</body>
</html>
