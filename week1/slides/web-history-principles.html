<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web发展历史与基本原理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
            height: 100vh;
        }

        .presentation {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s ease-in-out;
            padding: 2rem;
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .slide-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            max-width: 1200px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-height: 90vh;
            overflow-y: auto;
        }

        h1 {
            font-size: 3rem;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: 2.5rem;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }

        h3 {
            font-size: 1.8rem;
            color: #34495e;
            margin-bottom: 1rem;
        }

        h4 {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 0.8rem;
        }

        .timeline {
            position: relative;
            padding: 2rem 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 2rem 0;
            width: 45%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            text-align: right;
            padding-right: 3rem;
        }

        .timeline-item:nth-child(even) {
            left: 55%;
            text-align: left;
            padding-left: 3rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: #667eea;
            border: 4px solid white;
            border-radius: 50%;
            top: 0.5rem;
        }

        .timeline-item:nth-child(odd)::before {
            right: -13px;
        }

        .timeline-item:nth-child(even)::before {
            left: -13px;
        }

        .timeline-content {
            background: rgba(102, 126, 234, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .year {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .event-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .event-description {
            color: #555;
            line-height: 1.5;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: start;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 2rem;
            align-items: start;
        }

        .concept-box {
            background: rgba(102, 126, 234, 0.1);
            padding: 2rem;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            margin: 1rem 0;
        }

        .vs-comparison {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 2rem 0;
        }

        .vs-item {
            flex: 1;
            text-align: center;
            padding: 2rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            margin: 0 1rem;
        }

        .vs-divider {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0 1rem;
        }

        .architecture-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 2rem 0;
            padding: 2rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
        }

        .arch-component {
            flex: 1;
            text-align: center;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 0 1rem;
        }

        .arch-arrow {
            font-size: 2rem;
            color: #667eea;
            margin: 0 1rem;
        }

        .url-structure {
            font-family: 'Courier New', monospace;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-size: 1.2rem;
            text-align: center;
        }

        .url-part {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            margin: 0.2rem;
            border-radius: 5px;
        }

        .protocol { background: #e74c3c; }
        .host { background: #f39c12; }
        .port { background: #27ae60; }
        .path { background: #3498db; }
        .query { background: #9b59b6; }
        .hash { background: #1abc9c; }

        .lifecycle-steps {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 2rem 0;
        }

        .lifecycle-step {
            flex: 1;
            min-width: 200px;
            text-align: center;
            padding: 1.5rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            margin: 0.5rem;
            position: relative;
        }

        .lifecycle-step::after {
            content: '→';
            position: absolute;
            right: -1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: #667eea;
        }

        .lifecycle-step:last-child::after {
            display: none;
        }

        .step-number {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            background: #667eea;
            color: white;
            border-radius: 50%;
            line-height: 2rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-align: left;
        }

        .comparison-table td {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:nth-child(even) {
            background: rgba(102, 126, 234, 0.05);
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #721c24;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #155724;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 2rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem 2rem;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .navigation button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .navigation button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .navigation button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 1000;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
            width: 6.67%;
        }

        @media (max-width: 768px) {
            .slide-content {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 { font-size: 2rem; }
            h2 { font-size: 1.8rem; }
            
            .two-column,
            .three-column {
                grid-template-columns: 1fr;
            }
            
            .timeline::before {
                left: 2rem;
            }
            
            .timeline-item {
                width: calc(100% - 4rem);
                left: 4rem !important;
                text-align: left !important;
                padding-left: 2rem !important;
                padding-right: 0 !important;
            }
            
            .timeline-item::before {
                left: -2.5rem !important;
                right: auto !important;
            }
            
            .vs-comparison {
                flex-direction: column;
            }
            
            .vs-divider {
                transform: rotate(90deg);
                margin: 1rem 0;
            }
            
            .architecture-diagram {
                flex-direction: column;
            }
            
            .arch-arrow {
                transform: rotate(90deg);
                margin: 1rem 0;
            }
            
            .lifecycle-steps {
                flex-direction: column;
            }
            
            .lifecycle-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -1rem;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <section class="slide active" id="slide-1">
            <div class="slide-content">
                <h1>Web发展历史与基本原理</h1>
                <div style="text-align: center; margin: 2rem 0;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">🌐</div>
                    <h3>从万维网诞生到现代Web技术</h3>
                    <p style="font-size: 1.2rem; color: #666; margin-top: 2rem;">
                        探索Web技术的发展历程，理解现代Web应用的基本原理
                    </p>
                </div>
                <div class="highlight">
                    <strong>本节内容：</strong>
                    <ul style="margin-top: 1rem; columns: 2;">
                        <li>Web的基本概念</li>
                        <li>互联网vs万维网</li>
                        <li>关键历史里程碑</li>
                        <li>客户端-服务器模型</li>
                        <li>URL结构与DNS</li>
                        <li>请求-响应生命周期</li>
                        <li>静态页vs动态页</li>
                        <li>职责分层</li>
                        <li>渲染模式</li>
                        <li>浏览器内核</li>
                        <li>标准组织</li>
                        <li>开发到上线流程</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Web是什么 -->
        <section class="slide" id="slide-2">
            <div class="slide-content">
                <h2>Web是什么？</h2>
                <div class="concept-box">
                    <h3>🌐 万维网 (World Wide Web)</h3>
                    <p style="font-size: 1.2rem; margin-bottom: 1rem;">
                        <strong>文档与资源的超链接网络</strong>
                    </p>
                    <p>通过超链接连接的信息系统，用户可以通过浏览器访问和浏览各种资源</p>
                </div>
                
                <div class="two-column">
                    <div>
                        <h3>核心组成部分</h3>
                        <div class="concept-box">
                            <h4>📄 文档 (Documents)</h4>
                            <ul>
                                <li>HTML页面</li>
                                <li>文本文件</li>
                                <li>PDF文档</li>
                                <li>图片、视频等媒体</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>🔗 超链接 (Hyperlinks)</h4>
                            <ul>
                                <li>连接不同资源</li>
                                <li>实现非线性浏览</li>
                                <li>构建信息网络</li>
                                <li>支持跨站点链接</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>浏览器作为客户端</h3>
                        <div class="concept-box">
                            <h4>🖥️ 浏览器的职责</h4>
                            <ul>
                                <li><strong>请求资源</strong> - 向服务器发送HTTP请求</li>
                                <li><strong>解析内容</strong> - 解析HTML、CSS、JavaScript</li>
                                <li><strong>渲染页面</strong> - 将代码转换为可视化界面</li>
                                <li><strong>用户交互</strong> - 处理用户操作和事件</li>
                            </ul>
                        </div>
                        <div class="success">
                            <strong>关键理念：</strong>Web是一个分布式的信息系统，通过标准化的协议和格式，让全世界的信息能够互联互通。
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 互联网 vs 万维网 -->
        <section class="slide" id="slide-3">
            <div class="slide-content">
                <h2>互联网 vs 万维网</h2>
                <div class="vs-comparison">
                    <div class="vs-item">
                        <h3>🛣️ 互联网 (Internet)</h3>
                        <h4>"道路基础设施"</h4>
                        <ul style="text-align: left;">
                            <li><strong>物理网络</strong> - 全球计算机网络</li>
                            <li><strong>协议栈</strong> - TCP/IP协议族</li>
                            <li><strong>基础设施</strong> - 路由器、光缆、服务器</li>
                            <li><strong>传输层</strong> - 数据包传输</li>
                        </ul>
                        <div class="highlight">
                            <strong>诞生：</strong>1969年 ARPANET<br>
                            <strong>目的：</strong>连接全球计算机
                        </div>
                    </div>
                    <div class="vs-divider">VS</div>
                    <div class="vs-item">
                        <h3>🌐 万维网 (Web)</h3>
                        <h4>"跑在道路上的应用"</h4>
                        <ul style="text-align: left;">
                            <li><strong>应用层</strong> - 运行在互联网之上</li>
                            <li><strong>信息系统</strong> - 文档和资源网络</li>
                            <li><strong>用户界面</strong> - 浏览器访问</li>
                            <li><strong>内容层</strong> - 网页、应用、服务</li>
                        </ul>
                        <div class="highlight">
                            <strong>诞生：</strong>1989年 Tim Berners-Lee<br>
                            <strong>目的：</strong>信息共享和访问
                        </div>
                    </div>
                </div>
                
                <div class="concept-box">
                    <h3>🔗 关系类比</h3>
                    <div class="two-column">
                        <div>
                            <h4>互联网 = 高速公路系统</h4>
                            <ul>
                                <li>提供连接和传输能力</li>
                                <li>定义通信规则和协议</li>
                                <li>支持多种应用和服务</li>
                            </ul>
                        </div>
                        <div>
                            <h4>Web = 在公路上行驶的车辆</h4>
                            <ul>
                                <li>利用互联网基础设施</li>
                                <li>提供具体的用户服务</li>
                                <li>实现信息的展示和交互</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关键里程碑 1990-1999 -->
        <section class="slide" id="slide-4">
            <div class="slide-content">
                <h2>关键里程碑：Web诞生期 (1990-1999)</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">1990</div>
                            <div class="event-title">HTML诞生</div>
                            <div class="event-description">
                                Tim Berners-Lee创建HTML，定义了网页的基本结构和超链接概念
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">1993</div>
                            <div class="event-title">Mosaic浏览器</div>
                            <div class="event-description">
                                第一个图形化浏览器，支持图片显示，让Web变得更加直观和易用
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">1995</div>
                            <div class="event-title">Internet Explorer</div>
                            <div class="event-description">
                                微软发布IE浏览器，开始第一次浏览器大战，推动Web技术快速发展
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">1996</div>
                            <div class="event-title">CSS引入</div>
                            <div class="event-description">
                                层叠样式表诞生，实现了内容与样式的分离，让网页设计更加灵活
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">1995</div>
                            <div class="event-title">JavaScript诞生</div>
                            <div class="event-description">
                                Brendan Eich在10天内创造JavaScript，为网页带来了交互性和动态功能
                            </div>
                        </div>
                    </div>
                </div>
                <div class="success">
                    <strong>这个时期的意义：</strong>奠定了现代Web的三大基石 - HTML(结构)、CSS(样式)、JavaScript(行为)
                </div>
            </div>
        </section>

        <!-- 关键里程碑 2000-2009 -->
        <section class="slide" id="slide-5">
            <div class="slide-content">
                <h2>关键里程碑：Web 2.0时代 (2000-2009)</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2005</div>
                            <div class="event-title">AJAX技术兴起</div>
                            <div class="event-description">
                                异步JavaScript和XML，实现了无刷新页面更新，开启了富客户端应用时代
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2004-2006</div>
                            <div class="event-title">Web 2.0概念</div>
                            <div class="event-description">
                                用户生成内容、社交网络、协作平台兴起，Web从信息展示转向互动参与
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2006</div>
                            <div class="event-title">jQuery发布</div>
                            <div class="event-description">
                                简化了JavaScript操作，"Write less, do more"，降低了前端开发门槛
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2008</div>
                            <div class="event-title">Chrome浏览器</div>
                            <div class="event-description">
                                Google发布Chrome，引入V8引擎，大幅提升JavaScript执行性能
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2009</div>
                            <div class="event-title">Node.js诞生</div>
                            <div class="event-description">
                                JavaScript进入服务端，全栈JavaScript开发成为可能
                            </div>
                        </div>
                    </div>
                </div>
                <div class="highlight">
                    <strong>这个时期的特点：</strong>从静态网页向动态Web应用转变，用户体验大幅提升，前端技术快速发展
                </div>
            </div>
        </section>

        <!-- 关键里程碑 2010-至今 -->
        <section class="slide" id="slide-6">
            <div class="slide-content">
                <h2>关键里程碑：现代Web时代 (2010-至今)</h2>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2014</div>
                            <div class="event-title">HTML5标准化</div>
                            <div class="event-description">
                                HTML5正式成为标准，带来语义化标签、多媒体支持、离线存储等新特性
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2010-2015</div>
                            <div class="event-title">移动优先设计</div>
                            <div class="event-description">
                                响应式设计兴起，移动设备成为主要访问方式，Mobile First成为设计理念
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2013-2016</div>
                            <div class="event-title">现代前端框架</div>
                            <div class="event-description">
                                React(2013)、Vue(2014)、Angular 2+(2016)发布，组件化开发成为主流
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2015-2022</div>
                            <div class="event-title">HTTP/2 & HTTP/3</div>
                            <div class="event-description">
                                HTTP/2(2015)多路复用，HTTP/3(2022)基于QUIC，大幅提升传输性能
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="year">2010-至今</div>
                            <div class="event-title">云计算与DevOps</div>
                            <div class="event-description">
                                云服务普及，CI/CD流水线，容器化部署，现代化开发运维体系建立
                            </div>
                        </div>
                    </div>
                </div>
                <div class="success">
                    <strong>当前趋势：</strong>组件化、工程化、云原生、AI辅助开发，Web技术向更高效、更智能的方向发展
                </div>
            </div>
        </section>

        <!-- 客户端-服务器模型 -->
        <section class="slide" id="slide-7">
            <div class="slide-content">
                <h2>客户端-服务器模型</h2>
                <div class="architecture-diagram">
                    <div class="arch-component">
                        <h3>🖥️ 客户端 (Client)</h3>
                        <h4>浏览器</h4>
                        <ul>
                            <li>发送HTTP请求</li>
                            <li>接收和解析响应</li>
                            <li>渲染用户界面</li>
                            <li>处理用户交互</li>
                        </ul>
                    </div>
                    <div class="arch-arrow">⇄</div>
                    <div class="arch-component">
                        <h3>🖥️ 服务器 (Server)</h3>
                        <h4>Web服务器</h4>
                        <ul>
                            <li>接收HTTP请求</li>
                            <li>处理业务逻辑</li>
                            <li>访问数据库</li>
                            <li>返回HTTP响应</li>
                        </ul>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>🔄 交互特点</h3>
                        <div class="concept-box">
                            <h4>无状态 (Stateless)</h4>
                            <ul>
                                <li>每个请求都是独立的</li>
                                <li>服务器不保存客户端状态</li>
                                <li>需要通过Cookie/Session管理状态</li>
                                <li>提高了系统的可扩展性</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>请求-响应模式</h4>
                            <ul>
                                <li>客户端主动发起请求</li>
                                <li>服务器被动响应请求</li>
                                <li>一问一答的通信方式</li>
                                <li>支持并发处理多个请求</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>💡 模型优势</h3>
                        <div class="concept-box">
                            <h4>分工明确</h4>
                            <ul>
                                <li><strong>客户端</strong>：用户界面和体验</li>
                                <li><strong>服务器</strong>：业务逻辑和数据</li>
                                <li>职责分离，便于维护</li>
                                <li>支持多种客户端类型</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>可扩展性</h4>
                            <ul>
                                <li>服务器可以水平扩展</li>
                                <li>负载均衡分散压力</li>
                                <li>缓存提高响应速度</li>
                                <li>CDN加速内容分发</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- URL结构 -->
        <section class="slide" id="slide-8">
            <div class="slide-content">
                <h2>URL结构详解</h2>
                <div class="url-structure">
                    <span class="url-part protocol">https</span>://
                    <span class="url-part host">www.example.com</span>:
                    <span class="url-part port">443</span>
                    <span class="url-part path">/api/users</span>?
                    <span class="url-part query">page=1&size=10</span>#
                    <span class="url-part hash">section1</span>
                </div>

                <div class="three-column">
                    <div>
                        <h3>基本组成</h3>
                        <div class="concept-box">
                            <h4 style="color: #e74c3c;">协议 (Protocol)</h4>
                            <ul>
                                <li><strong>http://</strong> - 不安全</li>
                                <li><strong>https://</strong> - 安全加密</li>
                                <li><strong>ftp://</strong> - 文件传输</li>
                                <li><strong>file://</strong> - 本地文件</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4 style="color: #f39c12;">主机 (Host)</h4>
                            <ul>
                                <li>域名：www.example.com</li>
                                <li>IP地址：***********</li>
                                <li>localhost：本地主机</li>
                                <li>子域名：api.example.com</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>路径与参数</h3>
                        <div class="concept-box">
                            <h4 style="color: #27ae60;">端口 (Port)</h4>
                            <ul>
                                <li><strong>80</strong> - HTTP默认端口</li>
                                <li><strong>443</strong> - HTTPS默认端口</li>
                                <li><strong>3000</strong> - 开发服务器常用</li>
                                <li><strong>8080</strong> - 备用HTTP端口</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4 style="color: #3498db;">路径 (Path)</h4>
                            <ul>
                                <li>/index.html - 文件路径</li>
                                <li>/api/users - API端点</li>
                                <li>/products/123 - 资源ID</li>
                                <li>/ - 根路径</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>查询与锚点</h3>
                        <div class="concept-box">
                            <h4 style="color: #9b59b6;">查询参数 (Query)</h4>
                            <ul>
                                <li>?key=value - 单个参数</li>
                                <li>&key2=value2 - 多个参数</li>
                                <li>用于传递数据给服务器</li>
                                <li>GET请求的主要数据载体</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4 style="color: #1abc9c;">哈希 (Hash)</h4>
                            <ul>
                                <li>#section1 - 页面锚点</li>
                                <li>不会发送到服务器</li>
                                <li>用于页面内导航</li>
                                <li>SPA路由常用</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- DNS与域名解析 -->
        <section class="slide" id="slide-9">
            <div class="slide-content">
                <h2>DNS与域名解析</h2>
                <div class="concept-box">
                    <h3>🌐 DNS (Domain Name System)</h3>
                    <p><strong>域名系统</strong> - 将人类可读的域名转换为计算机可识别的IP地址</p>
                </div>

                <div class="lifecycle-steps">
                    <div class="lifecycle-step">
                        <div class="step-number">1</div>
                        <h4>用户输入域名</h4>
                        <p>在浏览器中输入<br>www.example.com</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">2</div>
                        <h4>检查本地缓存</h4>
                        <p>浏览器和操作系统<br>检查DNS缓存</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">3</div>
                        <h4>查询DNS服务器</h4>
                        <p>向ISP的DNS服务器<br>发送查询请求</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">4</div>
                        <h4>递归解析</h4>
                        <p>根服务器→顶级域服务器<br>→权威域名服务器</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">5</div>
                        <h4>返回IP地址</h4>
                        <p>获得目标服务器的<br>IP地址</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">6</div>
                        <h4>建立连接</h4>
                        <p>使用IP地址连接<br>目标服务器</p>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>🔄 递归解析过程</h3>
                        <div class="concept-box">
                            <h4>1. 根域名服务器 (.)</h4>
                            <p>告诉你.com域名服务器的地址</p>
                        </div>
                        <div class="concept-box">
                            <h4>2. 顶级域名服务器 (.com)</h4>
                            <p>告诉你example.com域名服务器的地址</p>
                        </div>
                        <div class="concept-box">
                            <h4>3. 权威域名服务器</h4>
                            <p>返回www.example.com的实际IP地址</p>
                        </div>
                    </div>
                    <div>
                        <h3>⚡ 缓存机制</h3>
                        <div class="concept-box">
                            <h4>多级缓存</h4>
                            <ul>
                                <li><strong>浏览器缓存</strong> - 几分钟到几小时</li>
                                <li><strong>操作系统缓存</strong> - 系统级DNS缓存</li>
                                <li><strong>路由器缓存</strong> - 网络设备缓存</li>
                                <li><strong>ISP缓存</strong> - 运营商DNS缓存</li>
                            </ul>
                        </div>
                        <div class="success">
                            <strong>缓存的好处：</strong>减少查询时间，降低网络负载，提高访问速度
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 请求-响应生命周期 -->
        <section class="slide" id="slide-10">
            <div class="slide-content">
                <h2>一次请求-响应的生命周期</h2>
                <div class="lifecycle-steps">
                    <div class="lifecycle-step">
                        <div class="step-number">1</div>
                        <h4>构造请求</h4>
                        <p>浏览器解析URL<br>构造HTTP请求</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">2</div>
                        <h4>DNS解析</h4>
                        <p>域名解析为<br>IP地址</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">3</div>
                        <h4>建立连接</h4>
                        <p>TCP三次握手<br>建立连接</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">4</div>
                        <h4>发送请求</h4>
                        <p>发送HTTP请求<br>到服务器</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">5</div>
                        <h4>服务器处理</h4>
                        <p>解析请求<br>执行业务逻辑</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">6</div>
                        <h4>返回响应</h4>
                        <p>生成HTTP响应<br>发送给客户端</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">7</div>
                        <h4>接收响应</h4>
                        <p>浏览器接收<br>响应数据</p>
                    </div>
                    <div class="lifecycle-step">
                        <div class="step-number">8</div>
                        <h4>渲染页面</h4>
                        <p>解析HTML/CSS<br>渲染用户界面</p>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>🔍 详细过程分析</h3>
                        <div class="concept-box">
                            <h4>网络层面</h4>
                            <ul>
                                <li><strong>DNS查询</strong> - 通常20-100ms</li>
                                <li><strong>TCP连接</strong> - 三次握手延迟</li>
                                <li><strong>SSL握手</strong> - HTTPS额外开销</li>
                                <li><strong>数据传输</strong> - 受带宽和距离影响</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>服务器处理</h4>
                            <ul>
                                <li><strong>请求解析</strong> - 解析HTTP头和参数</li>
                                <li><strong>路由匹配</strong> - 找到对应的处理器</li>
                                <li><strong>业务逻辑</strong> - 执行具体功能</li>
                                <li><strong>数据库查询</strong> - 获取或存储数据</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>⚡ 性能优化点</h3>
                        <div class="concept-box">
                            <h4>减少请求次数</h4>
                            <ul>
                                <li>合并CSS/JS文件</li>
                                <li>使用CSS Sprites</li>
                                <li>内联小图片(base64)</li>
                                <li>HTTP/2多路复用</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>加速响应</h4>
                            <ul>
                                <li><strong>CDN</strong> - 内容分发网络</li>
                                <li><strong>缓存</strong> - 浏览器和服务器缓存</li>
                                <li><strong>压缩</strong> - Gzip/Brotli压缩</li>
                                <li><strong>预加载</strong> - DNS预解析、资源预加载</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 静态页 vs 动态页 -->
        <section class="slide" id="slide-11">
            <div class="slide-content">
                <h2>静态页面 vs 动态页面</h2>
                <div class="vs-comparison">
                    <div class="vs-item">
                        <h3>📄 静态页面</h3>
                        <h4>纯文件托管</h4>
                        <div class="concept-box">
                            <h4>特点</h4>
                            <ul>
                                <li>预先生成的HTML文件</li>
                                <li>内容固定不变</li>
                                <li>直接从文件系统读取</li>
                                <li>无需服务器端处理</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>优势</h4>
                            <ul>
                                <li>✅ 加载速度快</li>
                                <li>✅ 服务器压力小</li>
                                <li>✅ 易于缓存</li>
                                <li>✅ 安全性高</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>适用场景</h4>
                            <ul>
                                <li>企业官网</li>
                                <li>产品介绍页</li>
                                <li>博客文章</li>
                                <li>文档网站</li>
                            </ul>
                        </div>
                    </div>
                    <div class="vs-divider">VS</div>
                    <div class="vs-item">
                        <h3>⚡ 动态页面</h3>
                        <h4>服务端渲染/接口驱动</h4>
                        <div class="concept-box">
                            <h4>特点</h4>
                            <ul>
                                <li>实时生成HTML内容</li>
                                <li>内容根据请求变化</li>
                                <li>需要服务器端处理</li>
                                <li>可以访问数据库</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>优势</h4>
                            <ul>
                                <li>✅ 内容个性化</li>
                                <li>✅ 实时数据更新</li>
                                <li>✅ 用户交互丰富</li>
                                <li>✅ 功能强大</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>适用场景</h4>
                            <ul>
                                <li>电商网站</li>
                                <li>社交平台</li>
                                <li>管理系统</li>
                                <li>在线应用</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="highlight">
                    <strong>现代趋势：</strong>静态站点生成器(SSG)结合动态功能，如Gatsby、Next.js、Nuxt.js等，兼顾性能和功能
                </div>
            </div>
        </section>

        <!-- 职责分层 -->
        <section class="slide" id="slide-12">
            <div class="slide-content">
                <h2>Web开发职责分层</h2>
                <div class="three-column">
                    <div>
                        <div class="concept-box">
                            <h3>🎨 前端 (Frontend)</h3>
                            <h4>用户界面与体验</h4>
                            <ul>
                                <li><strong>技术栈</strong></li>
                                <li>HTML/CSS/JavaScript</li>
                                <li>React/Vue/Angular</li>
                                <li>Webpack/Vite</li>
                                <li>Sass/Less/PostCSS</li>
                            </ul>
                            <ul>
                                <li><strong>职责</strong></li>
                                <li>页面布局和样式</li>
                                <li>用户交互逻辑</li>
                                <li>数据展示</li>
                                <li>性能优化</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>⚙️ 后端 (Backend)</h3>
                            <h4>业务逻辑与数据</h4>
                            <ul>
                                <li><strong>技术栈</strong></li>
                                <li>Node.js/Python/Java</li>
                                <li>Express/Django/Spring</li>
                                <li>MySQL/MongoDB</li>
                                <li>Redis/Elasticsearch</li>
                            </ul>
                            <ul>
                                <li><strong>职责</strong></li>
                                <li>API接口开发</li>
                                <li>业务逻辑处理</li>
                                <li>数据库操作</li>
                                <li>安全认证</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>🔄 全栈 (Fullstack)</h3>
                            <h4>前后端通吃</h4>
                            <ul>
                                <li><strong>技术栈</strong></li>
                                <li>JavaScript全栈</li>
                                <li>MEAN/MERN</li>
                                <li>Next.js/Nuxt.js</li>
                                <li>Serverless</li>
                            </ul>
                            <ul>
                                <li><strong>职责</strong></li>
                                <li>端到端开发</li>
                                <li>架构设计</li>
                                <li>技术选型</li>
                                <li>项目管理</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>🤝 协作边界</h3>
                        <div class="concept-box">
                            <h4>接口约定</h4>
                            <ul>
                                <li><strong>API设计</strong> - RESTful/GraphQL</li>
                                <li><strong>数据格式</strong> - JSON/XML</li>
                                <li><strong>错误处理</strong> - 统一错误码</li>
                                <li><strong>文档规范</strong> - Swagger/OpenAPI</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>开发流程</h4>
                            <ul>
                                <li>需求分析和接口设计</li>
                                <li>并行开发和联调测试</li>
                                <li>集成测试和部署</li>
                                <li>监控和维护</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>🎯 技能要求</h3>
                        <div class="concept-box">
                            <h4>前端工程师</h4>
                            <ul>
                                <li>UI/UX设计理解</li>
                                <li>浏览器兼容性</li>
                                <li>性能优化</li>
                                <li>工程化工具</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>后端工程师</h4>
                            <ul>
                                <li>系统架构设计</li>
                                <li>数据库设计</li>
                                <li>安全防护</li>
                                <li>高并发处理</li>
                            </ul>
                        </div>
                        <div class="success">
                            <strong>趋势：</strong>T型人才，既有专业深度，又有跨领域广度
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 渲染模式 -->
        <section class="slide" id="slide-13">
            <div class="slide-content">
                <h2>Web应用渲染模式</h2>
                <div class="comparison-table">
                    <thead>
                        <tr>
                            <th>渲染模式</th>
                            <th>全称</th>
                            <th>渲染位置</th>
                            <th>优势</th>
                            <th>劣势</th>
                            <th>适用场景</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>CSR</strong></td>
                            <td>Client-Side Rendering</td>
                            <td>浏览器端</td>
                            <td>交互流畅、服务器压力小</td>
                            <td>首屏慢、SEO不友好</td>
                            <td>管理后台、SPA应用</td>
                        </tr>
                        <tr>
                            <td><strong>SSR</strong></td>
                            <td>Server-Side Rendering</td>
                            <td>服务器端</td>
                            <td>首屏快、SEO友好</td>
                            <td>服务器压力大、交互延迟</td>
                            <td>电商网站、内容网站</td>
                        </tr>
                        <tr>
                            <td><strong>SSG</strong></td>
                            <td>Static Site Generation</td>
                            <td>构建时</td>
                            <td>性能最佳、CDN友好</td>
                            <td>内容更新需重新构建</td>
                            <td>博客、文档、官网</td>
                        </tr>
                    </tbody>
                </div>

                <div class="three-column">
                    <div>
                        <div class="concept-box">
                            <h3>🖥️ CSR - 客户端渲染</h3>
                            <h4>工作流程</h4>
                            <ol>
                                <li>下载空白HTML</li>
                                <li>下载JavaScript包</li>
                                <li>执行JS渲染页面</li>
                                <li>发起API请求获取数据</li>
                                <li>更新页面内容</li>
                            </ol>
                            <div class="highlight">
                                <strong>代表框架：</strong><br>
                                React SPA、Vue SPA、Angular
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>🖥️ SSR - 服务端渲染</h3>
                            <h4>工作流程</h4>
                            <ol>
                                <li>服务器获取数据</li>
                                <li>服务器渲染HTML</li>
                                <li>返回完整HTML</li>
                                <li>浏览器显示页面</li>
                                <li>JavaScript接管交互</li>
                            </ol>
                            <div class="highlight">
                                <strong>代表框架：</strong><br>
                                Next.js、Nuxt.js、SvelteKit
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>📦 SSG - 静态站点生成</h3>
                            <h4>工作流程</h4>
                            <ol>
                                <li>构建时获取数据</li>
                                <li>生成静态HTML文件</li>
                                <li>部署到CDN</li>
                                <li>用户直接访问静态文件</li>
                                <li>可选择性添加交互</li>
                            </ol>
                            <div class="highlight">
                                <strong>代表框架：</strong><br>
                                Gatsby、Hugo、Jekyll
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 浏览器内核 -->
        <section class="slide" id="slide-14">
            <div class="slide-content">
                <h2>浏览器内核与Web标准</h2>
                <div class="three-column">
                    <div>
                        <div class="concept-box">
                            <h3>🌐 Blink</h3>
                            <h4>Google Chrome系</h4>
                            <ul>
                                <li><strong>使用浏览器</strong></li>
                                <li>Google Chrome</li>
                                <li>Microsoft Edge (新版)</li>
                                <li>Opera</li>
                                <li>Brave</li>
                            </ul>
                            <ul>
                                <li><strong>特点</strong></li>
                                <li>性能优秀</li>
                                <li>标准支持最新</li>
                                <li>市场份额最大</li>
                                <li>开发者工具强大</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>🦊 Gecko</h3>
                            <h4>Mozilla Firefox</h4>
                            <ul>
                                <li><strong>使用浏览器</strong></li>
                                <li>Mozilla Firefox</li>
                                <li>Thunderbird</li>
                            </ul>
                            <ul>
                                <li><strong>特点</strong></li>
                                <li>注重隐私保护</li>
                                <li>开源透明</li>
                                <li>CSS Grid支持优秀</li>
                                <li>开发者工具专业</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div class="concept-box">
                            <h3>🍎 WebKit</h3>
                            <h4>Apple Safari</h4>
                            <ul>
                                <li><strong>使用浏览器</strong></li>
                                <li>Safari</li>
                                <li>iOS Safari</li>
                                <li>部分移动浏览器</li>
                            </ul>
                            <ul>
                                <li><strong>特点</strong></li>
                                <li>能耗优化好</li>
                                <li>iOS生态集成</li>
                                <li>隐私保护强</li>
                                <li>某些新特性较慢</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>📊 市场份额 (2024)</h3>
                        <div class="concept-box">
                            <h4>桌面浏览器</h4>
                            <ul>
                                <li><strong>Chrome</strong> - ~65%</li>
                                <li><strong>Edge</strong> - ~13%</li>
                                <li><strong>Safari</strong> - ~9%</li>
                                <li><strong>Firefox</strong> - ~8%</li>
                                <li><strong>其他</strong> - ~5%</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>移动浏览器</h4>
                            <ul>
                                <li><strong>Chrome Mobile</strong> - ~63%</li>
                                <li><strong>Safari Mobile</strong> - ~25%</li>
                                <li><strong>Samsung Internet</strong> - ~4%</li>
                                <li><strong>其他</strong> - ~8%</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>🎯 兼容性策略</h3>
                        <div class="concept-box">
                            <h4>开发建议</h4>
                            <ul>
                                <li><strong>渐进增强</strong> - 基础功能优先</li>
                                <li><strong>特性检测</strong> - 而非浏览器检测</li>
                                <li><strong>Polyfill</strong> - 补充缺失功能</li>
                                <li><strong>测试覆盖</strong> - 主流浏览器测试</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>工具支持</h4>
                            <ul>
                                <li><strong>Can I Use</strong> - 特性支持查询</li>
                                <li><strong>Autoprefixer</strong> - 自动添加前缀</li>
                                <li><strong>Babel</strong> - JavaScript转译</li>
                                <li><strong>BrowserStack</strong> - 跨浏览器测试</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 标准组织与开发流程 -->
        <section class="slide" id="slide-15">
            <div class="slide-content">
                <h2>Web标准组织与开发流程</h2>
                <div class="two-column">
                    <div>
                        <h3>🏛️ 标准组织</h3>
                        <div class="concept-box">
                            <h4>WHATWG</h4>
                            <p><strong>Web Hypertext Application Technology Working Group</strong></p>
                            <ul>
                                <li>HTML Living Standard维护者</li>
                                <li>由浏览器厂商主导</li>
                                <li>快速迭代，持续更新</li>
                                <li>实用主义导向</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>W3C</h4>
                            <p><strong>World Wide Web Consortium</strong></p>
                            <ul>
                                <li>CSS、SVG、WebRTC等标准</li>
                                <li>多方利益相关者参与</li>
                                <li>正式标准化流程</li>
                                <li>长期稳定性考虑</li>
                            </ul>
                        </div>
                        <div class="concept-box">
                            <h4>ECMA International</h4>
                            <ul>
                                <li>ECMAScript (JavaScript)标准</li>
                                <li>年度发布周期</li>
                                <li>TC39委员会管理</li>
                                <li>提案分阶段推进</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3>🚀 从开发到上线</h3>
                        <div class="lifecycle-steps" style="flex-direction: column;">
                            <div class="lifecycle-step">
                                <div class="step-number">1</div>
                                <h4>开发 (Development)</h4>
                                <p>编写代码、本地调试、功能实现</p>
                            </div>
                            <div class="lifecycle-step">
                                <div class="step-number">2</div>
                                <h4>版本控制 (Version Control)</h4>
                                <p>Git管理、分支策略、代码审查</p>
                            </div>
                            <div class="lifecycle-step">
                                <div class="step-number">3</div>
                                <h4>构建 (Build)</h4>
                                <p>打包压缩、代码转译、资源优化</p>
                            </div>
                            <div class="lifecycle-step">
                                <div class="step-number">4</div>
                                <h4>测试 (Testing)</h4>
                                <p>单元测试、集成测试、E2E测试</p>
                            </div>
                            <div class="lifecycle-step">
                                <div class="step-number">5</div>
                                <h4>部署 (Deployment)</h4>
                                <p>CI/CD流水线、自动化部署</p>
                            </div>
                            <div class="lifecycle-step">
                                <div class="step-number">6</div>
                                <h4>监控 (Monitoring)</h4>
                                <p>性能监控、错误追踪、用户分析</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="success">
                    <strong>现代Web开发：</strong>标准化、工程化、自动化，从个人作坊到工业化生产的转变
                </div>
            </div>
        </section>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button id="prevBtn" onclick="changeSlide(-1)">← 上一页</button>
        <span id="slideCounter">1 / 15</span>
        <button id="nextBtn" onclick="changeSlide(1)">下一页 →</button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress" id="progress"></div>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 15;
        const slides = document.querySelectorAll('.slide');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const slideCounter = document.getElementById('slideCounter');
        const progress = document.getElementById('progress');

        function updateSlide() {
            slides.forEach((slide, index) => {
                slide.classList.remove('active', 'prev');
                if (index === currentSlide - 1) {
                    slide.classList.add('active');
                } else if (index < currentSlide - 1) {
                    slide.classList.add('prev');
                }
            });

            slideCounter.textContent = `${currentSlide} / ${totalSlides}`;
            progress.style.width = `${(currentSlide / totalSlides) * 100}%`;
            
            prevBtn.disabled = currentSlide === 1;
            nextBtn.disabled = currentSlide === totalSlides;
        }

        function changeSlide(direction) {
            const newSlide = currentSlide + direction;
            if (newSlide >= 1 && newSlide <= totalSlides) {
                currentSlide = newSlide;
                updateSlide();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    changeSlide(-1);
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    changeSlide(1);
                    break;
                case 'Home':
                    e.preventDefault();
                    currentSlide = 1;
                    updateSlide();
                    break;
                case 'End':
                    e.preventDefault();
                    currentSlide = totalSlides;
                    updateSlide();
                    break;
            }
        });

        // 初始化
        updateSlide();
    </script>
</body>
</html>
