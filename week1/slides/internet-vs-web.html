<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>互联网 vs 万维网</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fullscreen-svg {
            width: 100vw;
            height: 100vh;
            display: block;
        }

        /* 动画样式 */
        .road-segment {
            stroke-dasharray: 100;
            stroke-dashoffset: 100;
            animation: drawRoad 2s ease-in-out forwards;
        }

        @keyframes drawRoad {
            to { stroke-dashoffset: 0; }
        }

        .vehicle {
            opacity: 0;
            animation: vehicleAppear 1s ease-out forwards;
        }

        @keyframes vehicleAppear {
            to { opacity: 1; }
        }

        .moving-vehicle {
            animation: moveVehicle 8s linear infinite;
        }

        @keyframes moveVehicle {
            0% { transform: translateX(0); }
            25% { transform: translateX(300px); }
            50% { transform: translateX(600px); }
            75% { transform: translateX(300px); }
            100% { transform: translateX(0); }
        }

        .data-packet {
            animation: dataFlow 3s linear infinite;
        }

        @keyframes dataFlow {
            0% { transform: translateX(-50px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(800px); opacity: 0; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease-in forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 1s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .zoom-in {
            transform: scale(0);
            opacity: 0;
            animation: zoomIn 0.8s ease-out forwards;
        }

        @keyframes zoomIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .floating {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        .rotate {
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .glow {
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.6));
        }

        .interactive:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 15px rgba(255,255,255,0.8));
            cursor: pointer;
        }
    </style>
</head>
<body>
    <svg class="fullscreen-svg" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
        <!-- 背景渐变定义 -->
        <defs>
            <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#87CEEB"/>
                <stop offset="50%" style="stop-color:#98D8E8"/>
                <stop offset="100%" style="stop-color:#B0E0E6"/>
            </linearGradient>
            
            <linearGradient id="roadGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#555"/>
                <stop offset="50%" style="stop-color:#333"/>
                <stop offset="100%" style="stop-color:#222"/>
            </linearGradient>
            
            <linearGradient id="webGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#667eea"/>
                <stop offset="100%" style="stop-color:#764ba2"/>
            </linearGradient>
            
            <!-- 云朵图案 -->
            <g id="cloud">
                <ellipse cx="0" cy="0" rx="25" ry="15" fill="white" opacity="0.8"/>
                <ellipse cx="-15" cy="-5" rx="20" ry="12" fill="white" opacity="0.8"/>
                <ellipse cx="15" cy="-5" rx="20" ry="12" fill="white" opacity="0.8"/>
                <ellipse cx="-8" cy="8" rx="15" ry="10" fill="white" opacity="0.8"/>
                <ellipse cx="8" cy="8" rx="15" ry="10" fill="white" opacity="0.8"/>
            </g>
            
            <!-- 汽车图标 -->
            <g id="car">
                <rect x="0" y="10" width="60" height="25" rx="5" fill="#e74c3c"/>
                <rect x="5" y="5" width="50" height="15" rx="3" fill="#3498db"/>
                <circle cx="15" cy="40" r="8" fill="#2c3e50"/>
                <circle cx="45" cy="40" r="8" fill="#2c3e50"/>
                <circle cx="15" cy="40" r="5" fill="#95a5a6"/>
                <circle cx="45" cy="40" r="5" fill="#95a5a6"/>
            </g>
            
            <!-- 卡车图标 -->
            <g id="truck">
                <rect x="0" y="10" width="80" height="30" rx="5" fill="#f39c12"/>
                <rect x="60" y="5" width="20" height="20" rx="3" fill="#3498db"/>
                <circle cx="20" cy="45" r="10" fill="#2c3e50"/>
                <circle cx="60" cy="45" r="10" fill="#2c3e50"/>
                <circle cx="20" cy="45" r="6" fill="#95a5a6"/>
                <circle cx="60" cy="45" r="6" fill="#95a5a6"/>
            </g>
            
            <!-- 网页图标 -->
            <g id="webpage">
                <rect x="0" y="0" width="50" height="40" rx="3" fill="white" stroke="#3498db" stroke-width="2"/>
                <rect x="3" y="3" width="44" height="8" rx="1" fill="#3498db"/>
                <circle cx="8" cy="7" r="1.5" fill="white"/>
                <circle cx="13" cy="7" r="1.5" fill="white"/>
                <circle cx="18" cy="7" r="1.5" fill="white"/>
                <rect x="5" y="15" width="30" height="2" fill="#bdc3c7"/>
                <rect x="5" y="20" width="25" height="2" fill="#bdc3c7"/>
                <rect x="5" y="25" width="35" height="2" fill="#bdc3c7"/>
                <rect x="5" y="30" width="20" height="2" fill="#bdc3c7"/>
            </g>
            
            <!-- 邮件图标 -->
            <g id="email">
                <rect x="0" y="5" width="40" height="25" rx="2" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>
                <polygon points="0,5 20,20 40,5" fill="#c0392b"/>
                <text x="20" y="22" text-anchor="middle" font-size="8" fill="white" font-weight="bold">@</text>
            </g>
            
            <!-- 数据包图标 -->
            <g id="packet">
                <rect x="0" y="0" width="20" height="15" rx="2" fill="#2ecc71"/>
                <rect x="2" y="2" width="16" height="2" fill="white"/>
                <rect x="2" y="6" width="12" height="2" fill="white"/>
                <rect x="2" y="10" width="14" height="2" fill="white"/>
            </g>
            
            <!-- 服务器图标 -->
            <g id="server">
                <rect x="0" y="0" width="30" height="40" rx="3" fill="#34495e"/>
                <rect x="3" y="5" width="24" height="4" rx="1" fill="#2ecc71"/>
                <rect x="3" y="12" width="24" height="4" rx="1" fill="#e74c3c"/>
                <rect x="3" y="19" width="24" height="4" rx="1" fill="#f39c12"/>
                <circle cx="6" cy="30" r="2" fill="#3498db"/>
                <circle cx="12" cy="30" r="2" fill="#9b59b6"/>
                <circle cx="18" cy="30" r="2" fill="#1abc9c"/>
                <circle cx="24" cy="30" r="2" fill="#e67e22"/>
            </g>
        </defs>
        
        <!-- 天空背景 -->
        <rect width="1920" height="1080" fill="url(#skyGradient)"/>
        
        <!-- 云朵装饰 -->
        <g class="floating" style="animation-delay: 0s">
            <use href="#cloud" x="200" y="150"/>
        </g>
        <g class="floating" style="animation-delay: -2s">
            <use href="#cloud" x="500" y="100"/>
        </g>
        <g class="floating" style="animation-delay: -1s">
            <use href="#cloud" x="800" y="180"/>
        </g>
        <g class="floating" style="animation-delay: -3s">
            <use href="#cloud" x="1200" y="120"/>
        </g>
        <g class="floating" style="animation-delay: -0.5s">
            <use href="#cloud" x="1600" y="160"/>
        </g>
        
        <!-- 标题 -->
        <g class="fade-in" style="animation-delay: 0.5s">
            <text x="960" y="100" text-anchor="middle" font-size="56" font-weight="bold" fill="#2c3e50">
                互联网 vs 万维网
            </text>
            <text x="960" y="150" text-anchor="middle" font-size="28" fill="#34495e">
                基础设施与应用服务的关系
            </text>
        </g>
        
        <!-- 分割线 -->
        <line x1="960" y1="200" x2="960" y2="880" stroke="#bdc3c7" stroke-width="3" stroke-dasharray="10,5" class="fade-in" style="animation-delay: 1s"/>
        
        <!-- 左侧：互联网（道路系统） -->
        <g class="slide-up" style="animation-delay: 1.5s">
            <text x="480" y="250" text-anchor="middle" font-size="36" font-weight="bold" fill="#2c3e50">
                🛣️ 互联网 (Internet)
            </text>
            <text x="480" y="290" text-anchor="middle" font-size="24" fill="#7f8c8d">
                "道路基础设施"
            </text>
        </g>
        
        <!-- 道路网络 -->
        <g class="road-network">
            <!-- 主干道 -->
            <rect x="100" y="400" width="760" height="60" rx="30" fill="url(#roadGradient)" class="road-segment" style="animation-delay: 2s"/>
            <!-- 道路中线 -->
            <line x1="120" y1="430" x2="840" y2="430" stroke="white" stroke-width="3" stroke-dasharray="20,10" class="road-segment" style="animation-delay: 2.2s"/>
            
            <!-- 支路1 -->
            <rect x="200" y="320" width="60" height="140" rx="30" fill="url(#roadGradient)" class="road-segment" style="animation-delay: 2.4s"/>
            <line x1="230" y1="340" x2="230" y2="440" stroke="white" stroke-width="2" stroke-dasharray="15,8" class="road-segment" style="animation-delay: 2.6s"/>
            
            <!-- 支路2 -->
            <rect x="400" y="320" width="60" height="140" rx="30" fill="url(#roadGradient)" class="road-segment" style="animation-delay: 2.8s"/>
            <line x1="430" y1="340" x2="430" y2="440" stroke="white" stroke-width="2" stroke-dasharray="15,8" class="road-segment" style="animation-delay: 3s"/>
            
            <!-- 支路3 -->
            <rect x="600" y="320" width="60" height="140" rx="30" fill="url(#roadGradient)" class="road-segment" style="animation-delay: 3.2s"/>
            <line x1="630" y1="340" x2="630" y2="440" stroke="white" stroke-width="2" stroke-dasharray="15,8" class="road-segment" style="animation-delay: 3.4s"/>
            
            <!-- 环形交叉口 -->
            <circle cx="480" cy="580" r="80" fill="url(#roadGradient)" class="road-segment" style="animation-delay: 3.6s"/>
            <circle cx="480" cy="580" r="50" fill="url(#skyGradient)" class="fade-in" style="animation-delay: 3.8s"/>
            <circle cx="480" cy="580" r="50" fill="none" stroke="white" stroke-width="2" stroke-dasharray="10,5" class="road-segment" style="animation-delay: 4s"/>
        </g>
        
        <!-- 道路上的交通工具（数据传输） -->
        <g class="vehicle moving-vehicle" style="animation-delay: 4.5s">
            <use href="#car" x="150" y="380"/>
        </g>
        <g class="vehicle moving-vehicle" style="animation-delay: 5s; animation-duration: 10s">
            <use href="#truck" x="120" y="380"/>
        </g>
        
        <!-- 互联网特点说明 -->
        <g class="zoom-in" style="animation-delay: 4s">
            <rect x="50" y="700" width="860" height="150" rx="15" fill="rgba(52, 73, 94, 0.9)" stroke="#34495e" stroke-width="2"/>
            <text x="480" y="730" text-anchor="middle" font-size="20" font-weight="bold" fill="white">
                互联网特点
            </text>
            <text x="80" y="760" font-size="16" fill="#ecf0f1">• 物理网络基础设施</text>
            <text x="80" y="785" font-size="16" fill="#ecf0f1">• TCP/IP协议栈</text>
            <text x="80" y="810" font-size="16" fill="#ecf0f1">• 路由器、光缆、服务器</text>
            <text x="80" y="835" font-size="16" fill="#ecf0f1">• 数据包传输通道</text>
            
            <text x="500" y="760" font-size="16" fill="#ecf0f1">• 全球计算机网络</text>
            <text x="500" y="785" font-size="16" fill="#ecf0f1">• 1969年ARPANET起源</text>
            <text x="500" y="810" font-size="16" fill="#ecf0f1">• 连接世界各地设备</text>
            <text x="500" y="835" font-size="16" fill="#ecf0f1">• 支持多种应用协议</text>
        </g>
        
        <!-- 右侧：万维网（应用服务） -->
        <g class="slide-up" style="animation-delay: 1.5s">
            <text x="1440" y="250" text-anchor="middle" font-size="36" font-weight="bold" fill="#2c3e50">
                🌐 万维网 (Web)
            </text>
            <text x="1440" y="290" text-anchor="middle" font-size="24" fill="#7f8c8d">
                "跑在道路上的应用体系"
            </text>
        </g>
        
        <!-- Web应用层 -->
        <g class="web-layer">
            <!-- 浏览器窗口 -->
            <rect x="1100" y="350" width="680" height="400" rx="20" fill="url(#webGradient)" class="zoom-in" style="animation-delay: 3s"/>
            <rect x="1120" y="370" width="640" height="360" rx="10" fill="rgba(255,255,255,0.95)" class="fade-in" style="animation-delay: 3.5s"/>
            
            <!-- 浏览器标题栏 -->
            <rect x="1120" y="370" width="640" height="40" rx="10" fill="#3498db" class="fade-in" style="animation-delay: 3.7s"/>
            <circle cx="1140" cy="390" r="6" fill="#e74c3c" class="zoom-in" style="animation-delay: 4s"/>
            <circle cx="1160" cy="390" r="6" fill="#f39c12" class="zoom-in" style="animation-delay: 4.1s"/>
            <circle cx="1180" cy="390" r="6" fill="#2ecc71" class="zoom-in" style="animation-delay: 4.2s"/>
            <text x="1440" y="395" text-anchor="middle" font-size="14" fill="white" font-weight="bold">万维网应用</text>
            
            <!-- Web应用图标 -->
            <g class="zoom-in" style="animation-delay: 4.5s">
                <use href="#webpage" x="1150" y="450" class="interactive"/>
                <text x="1175" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">网页</text>
            </g>
            
            <g class="zoom-in" style="animation-delay: 4.7s">
                <use href="#email" x="1230" y="450" class="interactive"/>
                <text x="1250" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">邮件</text>
            </g>
            
            <g class="zoom-in" style="animation-delay: 4.9s">
                <use href="#server" x="1320" y="450" class="interactive"/>
                <text x="1335" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">服务</text>
            </g>
            
            <!-- 社交媒体图标 -->
            <g class="zoom-in" style="animation-delay: 5.1s">
                <circle cx="1420" cy="470" r="25" fill="#3b5998" class="interactive"/>
                <text x="1420" y="477" text-anchor="middle" font-size="16" fill="white" font-weight="bold">f</text>
                <text x="1420" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">社交</text>
            </g>
            
            <g class="zoom-in" style="animation-delay: 5.3s">
                <circle cx="1520" cy="470" r="25" fill="#1da1f2" class="interactive"/>
                <text x="1520" y="477" text-anchor="middle" font-size="16" fill="white" font-weight="bold">T</text>
                <text x="1520" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">微博</text>
            </g>
            
            <g class="zoom-in" style="animation-delay: 5.5s">
                <circle cx="1620" cy="470" r="25" fill="#ff4500" class="interactive"/>
                <text x="1620" y="477" text-anchor="middle" font-size="16" fill="white" font-weight="bold">🛒</text>
                <text x="1620" y="510" text-anchor="middle" font-size="12" fill="#2c3e50">购物</text>
            </g>
            
            <!-- 数据流动效果 -->
            <g class="data-packet" style="animation-delay: 6s">
                <use href="#packet" x="1150" y="550"/>
            </g>
            <g class="data-packet" style="animation-delay: 6.5s">
                <use href="#packet" x="1150" y="570"/>
            </g>
            <g class="data-packet" style="animation-delay: 7s">
                <use href="#packet" x="1150" y="590"/>
            </g>
            
            <!-- HTTP协议标识 -->
            <text x="1440" y="650" text-anchor="middle" font-size="18" fill="#7f8c8d" class="fade-in" style="animation-delay: 6s">
                HTTP/HTTPS 协议
            </text>
            <text x="1440" y="680" text-anchor="middle" font-size="14" fill="#95a5a6" class="fade-in" style="animation-delay: 6.2s">
                运行在互联网基础设施之上
            </text>
        </g>
        
        <!-- Web特点说明 -->
        <g class="zoom-in" style="animation-delay: 5s">
            <rect x="1010" y="780" width="860" height="150" rx="15" fill="rgba(102, 126, 234, 0.9)" stroke="#667eea" stroke-width="2"/>
            <text x="1440" y="810" text-anchor="middle" font-size="20" font-weight="bold" fill="white">
                万维网特点
            </text>
            <text x="1040" y="840" font-size="16" fill="white">• 信息系统和应用层</text>
            <text x="1040" y="865" font-size="16" fill="white">• 超文本文档网络</text>
            <text x="1040" y="890" font-size="16" fill="white">• 浏览器用户界面</text>
            <text x="1040" y="915" font-size="16" fill="white">• HTML/CSS/JavaScript</text>
            
            <text x="1460" y="840" font-size="16" fill="white">• 1989年Tim Berners-Lee发明</text>
            <text x="1460" y="865" font-size="16" fill="white">• 运行在互联网之上</text>
            <text x="1460" y="890" font-size="16" fill="white">• 网页、应用、服务</text>
            <text x="1460" y="915" font-size="16" fill="white">• 用户友好的信息访问</text>
        </g>
        
        <!-- 连接箭头和说明 -->
        <g class="fade-in" style="animation-delay: 6.5s">
            <path d="M 900 430 Q 960 380 1020 430" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
            <text x="960" y="400" text-anchor="middle" font-size="16" fill="#e74c3c" font-weight="bold">
                Web运行在Internet上
            </text>
        </g>
        
        <!-- 箭头标记定义 -->
        <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
            </marker>
        </defs>
        
        <!-- 底部类比说明 -->
        <g class="slide-up" style="animation-delay: 7s">
            <rect x="460" y="950" width="1000" height="100" rx="20" fill="rgba(44, 62, 80, 0.9)" stroke="#2c3e50" stroke-width="2"/>
            <text x="960" y="980" text-anchor="middle" font-size="24" font-weight="bold" fill="white">
                🚗 类比理解
            </text>
            <text x="960" y="1010" text-anchor="middle" font-size="18" fill="#ecf0f1">
                互联网 = 高速公路系统　　万维网 = 在公路上行驶的各种车辆和服务
            </text>
            <text x="960" y="1035" text-anchor="middle" font-size="16" fill="#bdc3c7">
                基础设施提供连接能力，应用服务提供具体功能
            </text>
        </g>
        
        <!-- 交互提示 -->
        <g class="fade-in" style="animation-delay: 8s">
            <text x="960" y="30" text-anchor="middle" font-size="16" fill="#7f8c8d">
                💡 点击右侧Web应用图标可查看详细信息 | 按R键重新播放动画
            </text>
        </g>
    </svg>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 交互式Web应用图标
            const interactiveElements = document.querySelectorAll('.interactive');
            
            interactiveElements.forEach((element, index) => {
                element.addEventListener('click', function() {
                    const messages = [
                        '🌐 网页：超文本文档，通过HTTP协议传输',
                        '📧 邮件：电子邮件服务，SMTP/POP3/IMAP协议',
                        '🖥️ 服务：Web服务和API，RESTful架构',
                        '👥 社交：社交网络平台，用户生成内容',
                        '🐦 微博：微博客服务，实时信息分享',
                        '🛒 购物：电子商务平台，在线交易服务'
                    ];
                    
                    if (messages[index]) {
                        // 创建临时提示框
                        const tooltip = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                        tooltip.innerHTML = `
                            <rect x="1200" y="600" width="400" height="60" rx="10" fill="rgba(44, 62, 80, 0.95)" stroke="#3498db" stroke-width="2"/>
                            <text x="1400" y="635" text-anchor="middle" font-size="16" fill="white">${messages[index]}</text>
                        `;
                        
                        document.querySelector('svg').appendChild(tooltip);
                        
                        // 3秒后移除提示框
                        setTimeout(() => {
                            tooltip.remove();
                        }, 3000);
                    }
                    
                    // 高亮效果
                    this.style.filter = 'drop-shadow(0 0 20px rgba(52, 152, 219, 0.8))';
                    setTimeout(() => {
                        this.style.filter = '';
                    }, 2000);
                });
            });
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (e.key === 'r' || e.key === 'R') {
                    // R键重新播放动画
                    location.reload();
                }
                
                if (e.key === ' ') {
                    // 空格键暂停/恢复动画
                    e.preventDefault();
                    const animatedElements = document.querySelectorAll('[style*="animation"]');
                    animatedElements.forEach(el => {
                        const style = window.getComputedStyle(el);
                        if (style.animationPlayState === 'paused') {
                            el.style.animationPlayState = 'running';
                        } else {
                            el.style.animationPlayState = 'paused';
                        }
                    });
                }
            });
            
            // 鼠标悬停效果增强
            const vehicles = document.querySelectorAll('.vehicle');
            vehicles.forEach(vehicle => {
                vehicle.addEventListener('mouseenter', function() {
                    this.style.animationPlayState = 'paused';
                    this.style.filter = 'drop-shadow(0 0 15px rgba(231, 76, 60, 0.8))';
                });
                
                vehicle.addEventListener('mouseleave', function() {
                    this.style.animationPlayState = 'running';
                    this.style.filter = '';
                });
            });
            
            // 数据包点击效果
            const dataPackets = document.querySelectorAll('.data-packet');
            dataPackets.forEach(packet => {
                packet.addEventListener('click', function() {
                    // 创建数据传输说明
                    const explanation = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                    explanation.innerHTML = `
                        <rect x="1000" y="520" width="300" height="80" rx="10" fill="rgba(46, 204, 113, 0.95)" stroke="#27ae60" stroke-width="2"/>
                        <text x="1150" y="545" text-anchor="middle" font-size="14" fill="white" font-weight="bold">数据包传输</text>
                        <text x="1150" y="565" text-anchor="middle" font-size="12" fill="white">HTTP请求/响应</text>
                        <text x="1150" y="580" text-anchor="middle" font-size="12" fill="white">通过互联网基础设施传输</text>
                    `;
                    
                    document.querySelector('svg').appendChild(explanation);
                    
                    setTimeout(() => {
                        explanation.remove();
                    }, 3000);
                });
            });
        });
    </script>
</body>
</html>
