# Web开发环境配置指南

## 第1周：开发环境搭建

本指南将帮助你配置完整的Web开发环境，为后续的学习做好准备。

## 📋 环境要求

### 操作系统
- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+)

### 硬件要求
- 内存：至少4GB RAM（推荐8GB+）
- 存储：至少2GB可用空间
- 网络：稳定的互联网连接

## 🛠️ 必需软件安装

### 1. 代码编辑器 - Visual Studio Code

#### 下载安装
1. 访问 [VS Code官网](https://code.visualstudio.com/)
2. 下载适合你操作系统的版本
3. 运行安装程序，按默认设置安装

#### 推荐插件安装
打开VS Code后，按 `Ctrl+Shift+X` (Windows/Linux) 或 `Cmd+Shift+X` (macOS) 打开扩展面板，搜索并安装以下插件：

**必装插件：**
- `Live Server` - 实时预览网页
- `HTML CSS Support` - HTML/CSS智能提示
- `JavaScript (ES6) code snippets` - JavaScript代码片段
- `Prettier - Code formatter` - 代码格式化
- `Auto Rename Tag` - 自动重命名HTML标签

**推荐插件：**
- `Bracket Pair Colorizer` - 括号配对着色
- `indent-rainbow` - 缩进彩虹线
- `Material Icon Theme` - 文件图标主题
- `One Dark Pro` - 暗色主题

### 2. 浏览器

#### 主要浏览器
**Google Chrome** (推荐)
- 下载：[chrome.google.com](https://www.google.com/chrome/)
- 优势：强大的开发者工具，最新的Web标准支持

**Mozilla Firefox**
- 下载：[firefox.com](https://www.mozilla.org/firefox/)
- 优势：优秀的开发者工具，注重隐私

#### 开发者工具使用
1. 打开浏览器
2. 按 `F12` 或右键选择"检查元素"
3. 熟悉各个面板：
   - **Elements**: 查看和编辑HTML/CSS
   - **Console**: JavaScript控制台
   - **Sources**: 调试JavaScript代码
   - **Network**: 查看网络请求
   - **Application**: 查看存储数据

### 3. Node.js (后续课程需要)

#### 安装步骤
1. 访问 [nodejs.org](https://nodejs.org/)
2. 下载LTS版本（长期支持版）
3. 运行安装程序
4. 验证安装：打开终端/命令提示符，输入：
   ```bash
   node --version
   npm --version
   ```

## 📁 项目文件夹结构

创建以下文件夹结构来组织你的学习项目：

```
web-course/
├── week1/
│   ├── slides/
│   ├── examples/
│   └── exercises/
├── week2/
├── week3/
└── ...
```

### 创建步骤
1. 在你的文档文件夹中创建 `web-course` 文件夹
2. 在VS Code中打开这个文件夹：`File > Open Folder`
3. 创建子文件夹结构

## 🚀 第一个项目设置

### 1. 创建HTML文件
1. 在 `week1/exercises/` 文件夹中创建 `my-first-page.html`
2. 输入基本HTML结构：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的第一个网页</title>
</head>
<body>
    <h1>Hello, Web World!</h1>
    <p>这是我的第一个网页。</p>
</body>
</html>
```

### 2. 使用Live Server预览
1. 在VS Code中右键点击HTML文件
2. 选择 "Open with Live Server"
3. 浏览器会自动打开并显示你的网页
4. 修改代码时，页面会自动刷新

## 🔧 VS Code配置优化

### 用户设置
按 `Ctrl+,` (Windows/Linux) 或 `Cmd+,` (macOS) 打开设置，添加以下配置：

```json
{
    "editor.fontSize": 14,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,
    "editor.formatOnSave": true,
    "emmet.includeLanguages": {
        "javascript": "javascriptreact"
    },
    "liveServer.settings.donotShowInfoMsg": true,
    "liveServer.settings.port": 5500
}
```

### 快捷键
熟悉以下常用快捷键：

**文件操作：**
- `Ctrl+N` - 新建文件
- `Ctrl+S` - 保存文件
- `Ctrl+O` - 打开文件

**编辑操作：**
- `Ctrl+Z` - 撤销
- `Ctrl+Y` - 重做
- `Ctrl+F` - 查找
- `Ctrl+H` - 替换

**代码操作：**
- `Alt+Shift+F` - 格式化代码
- `Ctrl+/` - 注释/取消注释
- `Ctrl+D` - 选择相同内容

## 🌐 浏览器开发者工具详解

### Elements面板
- 查看HTML结构
- 实时编辑HTML和CSS
- 检查元素样式

### Console面板
- 执行JavaScript代码
- 查看错误信息
- 调试输出

### Network面板
- 监控网络请求
- 查看加载时间
- 分析性能

### Sources面板
- 调试JavaScript代码
- 设置断点
- 查看变量值

## ✅ 环境验证清单

完成以下检查确保环境配置正确：

- [ ] VS Code已安装并配置插件
- [ ] 浏览器已安装（Chrome/Firefox）
- [ ] 能够打开开发者工具
- [ ] Live Server插件工作正常
- [ ] 能够创建和预览HTML文件
- [ ] Node.js已安装（可选，后续需要）

## 🆘 常见问题解决

### Live Server无法启动
**问题**: 右键菜单没有"Open with Live Server"选项
**解决**: 
1. 确保已安装Live Server插件
2. 重启VS Code
3. 确保文件扩展名为.html

### 中文显示乱码
**问题**: 网页中文显示为乱码
**解决**: 
1. 确保HTML文件编码为UTF-8
2. 添加 `<meta charset="UTF-8">` 标签

### 开发者工具打不开
**问题**: 按F12无反应
**解决**: 
1. 尝试右键选择"检查元素"
2. 使用菜单：更多工具 > 开发者工具
3. 检查是否被其他软件占用F12键

## 📚 学习资源

### 官方文档
- [MDN Web Docs](https://developer.mozilla.org/) - 最权威的Web技术文档
- [VS Code文档](https://code.visualstudio.com/docs) - VS Code使用指南

### 在线工具
- [CodePen](https://codepen.io/) - 在线代码编辑器
- [JSFiddle](https://jsfiddle.net/) - 前端代码测试
- [Can I Use](https://caniuse.com/) - 浏览器兼容性查询

### 学习网站
- [W3Schools](https://www.w3schools.com/) - Web技术教程
- [FreeCodeCamp](https://www.freecodecamp.org/) - 免费编程课程

## 🎯 下一步

环境配置完成后，你可以：

1. 完成第1周的练习作业
2. 熟悉VS Code和浏览器开发者工具
3. 尝试创建更多的HTML页面
4. 预习第2周的HTML5基础内容

---

**提示**: 如果在配置过程中遇到问题，请及时向老师或同学求助。良好的开发环境是学习Web开发的基础！
