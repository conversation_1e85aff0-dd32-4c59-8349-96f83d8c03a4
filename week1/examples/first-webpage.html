<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的第一个网页</title>
    <style>
        /* 内联CSS样式 - 后续课程会学习外部CSS */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        
        p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
        }
        
        ul {
            padding-left: 30px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎来到Web开发世界！</h1>
        
        <p>这是我的第一个HTML页面。通过这个页面，我将学习Web开发的基础知识。</p>
        
        <h2>什么是HTML？</h2>
        <p>HTML（HyperText Markup Language）是超文本标记语言，是构建网页的基础语言。它使用标签来描述网页的结构和内容。</p>
        
        <div class="highlight">
            <strong>重要提示：</strong> HTML是所有网页的基础，掌握HTML是学习Web开发的第一步！
        </div>
        
        <h2>HTML基本结构</h2>
        <p>每个HTML文档都包含以下基本结构：</p>
        
        <div class="code-example">
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;title&gt;页面标题&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;!-- 页面内容 --&gt;
&lt;/body&gt;
&lt;/html&gt;
        </div>
        
        <h2>常用HTML标签</h2>
        <ul>
            <li><strong>&lt;h1&gt; - &lt;h6&gt;</strong>：标题标签，h1是最大的标题</li>
            <li><strong>&lt;p&gt;</strong>：段落标签</li>
            <li><strong>&lt;div&gt;</strong>：块级容器标签</li>
            <li><strong>&lt;span&gt;</strong>：行内容器标签</li>
            <li><strong>&lt;a&gt;</strong>：链接标签</li>
            <li><strong>&lt;img&gt;</strong>：图片标签</li>
            <li><strong>&lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;</strong>：列表标签</li>
        </ul>
        
        <h2>我学到了什么？</h2>
        <p>通过这个简单的网页，我学习了：</p>
        <ol>
            <li>HTML文档的基本结构</li>
            <li>如何使用各种HTML标签</li>
            <li>如何添加简单的CSS样式</li>
            <li>如何在浏览器中查看网页</li>
        </ol>
        
        <h2>下一步学习计划</h2>
        <p>接下来我将学习：</p>
        <ul>
            <li>更多的HTML5标签和属性</li>
            <li>CSS样式设计</li>
            <li>JavaScript交互功能</li>
            <li>响应式网页设计</li>
        </ul>
        
        <div class="highlight">
            <strong>练习建议：</strong> 尝试修改这个页面的内容，添加你自己的信息，比如姓名、专业、兴趣爱好等。
        </div>
        
        <div class="footer">
            <p>© 2024 我的第一个网页 | Web程序设计课程</p>
            <p>创建时间：<span id="current-time"></span></p>
        </div>
    </div>
    
    <script>
        // 简单的JavaScript代码 - 显示当前时间
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 添加一个简单的交互功能
        document.querySelector('h1').addEventListener('click', function() {
            alert('欢迎学习Web开发！');
        });
        
        // 在控制台输出欢迎信息
        console.log('欢迎来到Web开发世界！');
        console.log('打开浏览器开发者工具（F12）可以看到这条消息');
    </script>
</body>
</html>
