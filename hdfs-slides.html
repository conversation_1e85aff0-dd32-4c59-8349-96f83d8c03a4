<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDFS 文件系统 - 教学幻灯片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }
        
        .slide-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .slide {
            width: 95vw;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            padding: 40px;
            display: none;
            position: relative;
            overflow: hidden;
        }
        
        .slide.active {
            display: block;
            animation: slideIn 0.8s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .slide h1 {
            font-size: 3.5em;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .slide h2 {
            font-size: 2.8em;
            color: #34495e;
            margin-bottom: 25px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .slide h3 {
            font-size: 2.2em;
            color: #2980b9;
            margin-bottom: 20px;
        }
        
        .slide p, .slide li {
            font-size: 1.8em;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .slide ul {
            margin-left: 40px;
        }
        
        .slide-number {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 1.2em;
            color: #7f8c8d;
        }
        
        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        
        .nav-btn {
            background: rgba(52, 73, 94, 0.8);
            color: white;
            border: none;
            padding: 15px 25px;
            margin: 0 10px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(52, 73, 94, 1);
            transform: translateY(-2px);
        }
        
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #3498db;
            transition: width 0.3s ease;
            z-index: 1000;
        }
        
        .svg-animation {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.4em;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: calc(100% - 150px);
        }
        
        .center-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: calc(100% - 100px);
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Slide 1: Title -->
    <div class="slide active">
        <div class="center-content">
            <h1>HDFS 分布式文件系统</h1>
            <svg class="svg-animation" viewBox="0 0 800 300">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect x="50" y="50" width="700" height="200" fill="url(#grad1)" rx="20" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="2s" fill="freeze"/>
                </rect>
                <text x="400" y="120" text-anchor="middle" fill="white" font-size="24" font-weight="bold" opacity="0">
                    Hadoop Distributed File System
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="1s" fill="freeze"/>
                </text>
                <text x="400" y="160" text-anchor="middle" fill="white" font-size="18" opacity="0">
                    大数据存储的基石
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="2s" fill="freeze"/>
                </text>
            </svg>
            <p style="font-size: 2em; text-align: center; margin-top: 30px;">
                深入理解分布式存储架构与实现原理
            </p>
        </div>
        <div class="slide-number">1 / 29</div>
    </div>

    <!-- Slide 2: Course Overview -->
    <div class="slide">
        <h2>课程概览</h2>
        <div class="two-column">
            <div>
                <h3>第一部分：基础概念 (30分钟)</h3>
                <ul>
                    <li>什么是HDFS</li>
                    <li>设计目标与特点</li>
                    <li>架构概述</li>
                    <li>与传统文件系统的区别</li>
                </ul>
                
                <h3>第二部分：核心组件 (30分钟)</h3>
                <ul>
                    <li>NameNode详解</li>
                    <li>DataNode详解</li>
                    <li>Secondary NameNode</li>
                    <li>客户端交互</li>
                </ul>
            </div>
            <div>
                <h3>第三部分：数据操作 (20分钟)</h3>
                <ul>
                    <li>文件读取流程</li>
                    <li>文件写入流程</li>
                    <li>数据复制机制</li>
                    <li>故障恢复</li>
                </ul>
                
                <h3>第四部分：高级特性 (10分钟)</h3>
                <ul>
                    <li>高可用性</li>
                    <li>联邦机制</li>
                    <li>性能优化</li>
                    <li>实际应用案例</li>
                </ul>
            </div>
        </div>
        <div class="slide-number">2 / 29</div>
    </div>

    <!-- Slide 3: What is HDFS -->
    <div class="slide">
        <h2>什么是HDFS？</h2>
        <div class="highlight">
            <p><strong>HDFS (Hadoop Distributed File System)</strong> 是Apache Hadoop项目的核心组件之一，是一个分布式文件系统，专门设计用于在商用硬件集群上存储超大文件。</p>
        </div>
        
        <svg class="svg-animation" viewBox="0 0 800 350">
            <!-- Traditional File System -->
            <g id="traditional">
                <rect x="50" y="50" width="150" height="100" fill="#e74c3c" rx="10" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                </rect>
                <text x="125" y="85" text-anchor="middle" fill="white" font-size="12" font-weight="bold">传统文件系统</text>
                <text x="125" y="105" text-anchor="middle" fill="white" font-size="10">单机存储</text>
                <text x="125" y="125" text-anchor="middle" fill="white" font-size="10">容量有限</text>
            </g>
            
            <!-- Arrow -->
            <path d="M220 100 L280 100" stroke="#34495e" stroke-width="3" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </path>
            <polygon points="275,95 285,100 275,105" fill="#34495e" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </polygon>
            
            <!-- HDFS -->
            <g id="hdfs">
                <rect x="300" y="30" width="120" height="60" fill="#3498db" rx="8" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                </rect>
                <text x="360" y="55" text-anchor="middle" fill="white" font-size="10">NameNode</text>
                <text x="360" y="70" text-anchor="middle" fill="white" font-size="8">元数据管理</text>
                
                <rect x="300" y="110" width="80" height="50" fill="#2ecc71" rx="8" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                </rect>
                <text x="340" y="130" text-anchor="middle" fill="white" font-size="9">DataNode1</text>
                <text x="340" y="145" text-anchor="middle" fill="white" font-size="7">数据存储</text>
                
                <rect x="400" y="110" width="80" height="50" fill="#2ecc71" rx="8" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                </rect>
                <text x="440" y="130" text-anchor="middle" fill="white" font-size="9">DataNode2</text>
                <text x="440" y="145" text-anchor="middle" fill="white" font-size="7">数据存储</text>
                
                <rect x="500" y="110" width="80" height="50" fill="#2ecc71" rx="8" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                </rect>
                <text x="540" y="130" text-anchor="middle" fill="white" font-size="9">DataNode3</text>
                <text x="540" y="145" text-anchor="middle" fill="white" font-size="7">数据存储</text>
            </g>
            
            <!-- Benefits -->
            <text x="400" y="200" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold" opacity="0">
                HDFS 优势
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="400" y="220" text-anchor="middle" fill="#2c3e50" font-size="12" opacity="0">
                ✓ 海量数据存储  ✓ 高容错性  ✓ 高吞吐量
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
        </svg>
        
        <div class="slide-number">3 / 29</div>
    </div>

    <!-- Slide 4: HDFS Design Goals -->
    <div class="slide">
        <h2>HDFS 设计目标</h2>
        <div class="highlight">
            <h3>核心设计理念</h3>
            <p>HDFS的设计基于以下几个关键假设和目标：</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
            <div>
                <h3 style="color: #e74c3c;">🎯 硬件故障是常态</h3>
                <p>假设硬件故障经常发生，系统必须能够自动检测和快速恢复。</p>

                <h3 style="color: #f39c12;">📊 流式数据访问</h3>
                <p>优化批处理而非随机访问，强调高吞吐量而非低延迟。</p>
            </div>
            <div>
                <h3 style="color: #27ae60;">📁 大文件存储</h3>
                <p>针对GB到TB级别的大文件进行优化，而非大量小文件。</p>

                <h3 style="color: #8e44ad;">💰 商用硬件</h3>
                <p>运行在普通的商用硬件上，而非昂贵的专用设备。</p>
            </div>
        </div>

        <svg class="svg-animation" viewBox="0 0 800 200" style="margin-top: 20px;">
            <circle cx="150" cy="100" r="60" fill="#3498db" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </circle>
            <text x="150" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">容错性</text>

            <circle cx="300" cy="100" r="60" fill="#2ecc71" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
            </circle>
            <text x="300" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">高吞吐</text>

            <circle cx="450" cy="100" r="60" fill="#e74c3c" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </circle>
            <text x="450" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">大文件</text>

            <circle cx="600" cy="100" r="60" fill="#f39c12" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
            </circle>
            <text x="600" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">低成本</text>
        </svg>

        <div class="slide-number">4 / 29</div>
    </div>

    <!-- Slide 5: HDFS vs Traditional File Systems -->
    <div class="slide">
        <h2>HDFS vs 传统文件系统</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
            <div>
                <h3 style="color: #e74c3c;">传统文件系统</h3>
                <ul>
                    <li>单机存储，容量受限</li>
                    <li>随机访问优化</li>
                    <li>POSIX语义支持</li>
                    <li>低延迟访问</li>
                    <li>细粒度权限控制</li>
                    <li>支持小文件</li>
                </ul>

                <div class="code-block" style="font-size: 1.2em;">
/home/<USER>/documents/
├── file1.txt (4KB)
├── file2.doc (2MB)
└── photos/
    ├── img1.jpg (500KB)
    └── img2.jpg (800KB)
                </div>
            </div>

            <div>
                <h3 style="color: #3498db;">HDFS</h3>
                <ul>
                    <li>分布式存储，可扩展</li>
                    <li>流式访问优化</li>
                    <li>简化的文件模型</li>
                    <li>高吞吐量</li>
                    <li>基本权限控制</li>
                    <li>大文件优化</li>
                </ul>

                <div class="code-block" style="font-size: 1.2em;">
/user/data/
├── dataset1.txt (1GB)
├── logs.log (5GB)
└── warehouse/
    ├── table1/ (100GB)
    └── table2/ (500GB)
                </div>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>关键差异：</strong>HDFS牺牲了一些传统文件系统的特性（如随机写入、低延迟），换取了更好的可扩展性、容错性和大数据处理能力。</p>
        </div>

        <div class="slide-number">5 / 29</div>
    </div>

    <!-- Slide 6: HDFS Architecture Overview -->
    <div class="slide">
        <h2>HDFS 架构概览</h2>

        <svg class="svg-animation" viewBox="0 0 900 500" style="height: 450px;">
            <!-- Client -->
            <rect x="50" y="200" width="100" height="60" fill="#9b59b6" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="100" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Client</text>
            <text x="100" y="240" text-anchor="middle" fill="white" font-size="10">客户端</text>

            <!-- NameNode -->
            <rect x="350" y="50" width="150" height="80" fill="#e74c3c" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </rect>
            <text x="425" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">NameNode</text>
            <text x="425" y="95" text-anchor="middle" fill="white" font-size="11">元数据管理</text>
            <text x="425" y="110" text-anchor="middle" fill="white" font-size="10">命名空间管理</text>

            <!-- Secondary NameNode -->
            <rect x="550" y="50" width="120" height="80" fill="#f39c12" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
            </rect>
            <text x="610" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Secondary</text>
            <text x="610" y="90" text-anchor="middle" fill="white" font-size="12" font-weight="bold">NameNode</text>
            <text x="610" y="110" text-anchor="middle" fill="white" font-size="10">检查点服务</text>

            <!-- DataNodes -->
            <rect x="200" y="300" width="120" height="70" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
            </rect>
            <text x="260" y="325" text-anchor="middle" fill="white" font-size="12" font-weight="bold">DataNode 1</text>
            <text x="260" y="345" text-anchor="middle" fill="white" font-size="10">数据存储</text>
            <text x="260" y="360" text-anchor="middle" fill="white" font-size="10">Block 1,4,7</text>

            <rect x="350" y="300" width="120" height="70" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
            </rect>
            <text x="410" y="325" text-anchor="middle" fill="white" font-size="12" font-weight="bold">DataNode 2</text>
            <text x="410" y="345" text-anchor="middle" fill="white" font-size="10">数据存储</text>
            <text x="410" y="360" text-anchor="middle" fill="white" font-size="10">Block 2,5,8</text>

            <rect x="500" y="300" width="120" height="70" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </rect>
            <text x="560" y="325" text-anchor="middle" fill="white" font-size="12" font-weight="bold">DataNode 3</text>
            <text x="560" y="345" text-anchor="middle" fill="white" font-size="10">数据存储</text>
            <text x="560" y="360" text-anchor="middle" fill="white" font-size="10">Block 3,6,9</text>

            <!-- Connections -->
            <!-- Client to NameNode -->
            <path d="M150 220 L350 100" stroke="#34495e" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </path>
            <text x="220" y="150" fill="#34495e" font-size="10" opacity="0">
                元数据请求
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>

            <!-- Client to DataNodes -->
            <path d="M150 240 L200 320" stroke="#2980b9" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </path>
            <path d="M150 240 L350 320" stroke="#2980b9" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </path>
            <path d="M150 240 L500 320" stroke="#2980b9" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </path>
            <text x="80" y="280" fill="#2980b9" font-size="10" opacity="0">
                数据读写
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>

            <!-- NameNode to Secondary NameNode -->
            <path d="M500 90 L550 90" stroke="#e67e22" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </path>
            <text x="510" y="80" fill="#e67e22" font-size="10" opacity="0">
                检查点
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
            </text>

            <!-- DataNode heartbeats -->
            <path d="M260 300 L400 130" stroke="#16a085" stroke-width="1" stroke-dasharray="5,5" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
            </path>
            <path d="M410 300 L425 130" stroke="#16a085" stroke-width="1" stroke-dasharray="5,5" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
            </path>
            <path d="M560 300 L450 130" stroke="#16a085" stroke-width="1" stroke-dasharray="5,5" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
            </path>
            <text x="700" y="200" fill="#16a085" font-size="10" opacity="0">
                心跳 & 块报告
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5.5s" fill="freeze"/>
            </text>
        </svg>

        <div class="slide-number">6 / 29</div>
    </div>

    <!-- Slide 7: Master-Slave Architecture -->
    <div class="slide">
        <h2>主从架构模式</h2>

        <div class="highlight">
            <p>HDFS采用经典的主从（Master-Slave）架构模式，具有清晰的职责分离。</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
            <div>
                <h3 style="color: #e74c3c;">🏛️ Master节点 - NameNode</h3>
                <ul>
                    <li><strong>元数据管理：</strong>文件系统命名空间</li>
                    <li><strong>目录结构：</strong>文件和目录的层次结构</li>
                    <li><strong>块映射：</strong>文件到数据块的映射关系</li>
                    <li><strong>副本策略：</strong>数据块的复制和放置</li>
                    <li><strong>访问控制：</strong>权限和安全管理</li>
                    <li><strong>系统监控：</strong>集群状态监控</li>
                </ul>

                <div class="code-block" style="font-size: 1.1em; margin-top: 15px;">
# NameNode存储的元数据示例
/user/data/file1.txt
├── Size: 1GB
├── Blocks: [blk_001, blk_002, blk_003]
├── Replication: 3
└── Permissions: rw-r--r--
                </div>
            </div>

            <div>
                <h3 style="color: #27ae60;">🔧 Slave节点 - DataNode</h3>
                <ul>
                    <li><strong>数据存储：</strong>实际的数据块存储</li>
                    <li><strong>块管理：</strong>本地数据块的增删改查</li>
                    <li><strong>心跳报告：</strong>定期向NameNode报告状态</li>
                    <li><strong>块报告：</strong>汇报本地存储的数据块</li>
                    <li><strong>数据校验：</strong>检查数据完整性</li>
                    <li><strong>副本同步：</strong>参与数据复制过程</li>
                </ul>

                <div class="code-block" style="font-size: 1.1em; margin-top: 15px;">
# DataNode存储的数据块示例
/hadoop/data/current/
├── blk_001 (128MB)
├── blk_001.meta (校验信息)
├── blk_002 (128MB)
└── blk_002.meta (校验信息)
                </div>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>关键特点：</strong>NameNode是单点，但通过Secondary NameNode和高可用机制来保证系统的可靠性。DataNode可以动态添加和移除，提供良好的可扩展性。</p>
        </div>

        <div class="slide-number">7 / 29</div>
    </div>

    <!-- Slide 8: Data Blocks Concept -->
    <div class="slide">
        <h2>数据块（Block）概念</h2>

        <div class="highlight">
            <p>HDFS将大文件分割成固定大小的数据块（默认128MB），这是HDFS的基本存储单元。</p>
        </div>

        <svg class="svg-animation" viewBox="0 0 900 400" style="height: 350px;">
            <!-- Large File -->
            <rect x="50" y="50" width="200" height="60" fill="#3498db" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="150" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">大文件 (500MB)</text>
            <text x="150" y="90" text-anchor="middle" fill="white" font-size="10">example.txt</text>

            <!-- Arrow -->
            <path d="M270 80 L320 80" stroke="#34495e" stroke-width="3" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </path>
            <polygon points="315,75 325,80 315,85" fill="#34495e" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </polygon>
            <text x="295" y="70" text-anchor="middle" fill="#34495e" font-size="10" opacity="0">
                分割
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </text>

            <!-- Blocks -->
            <rect x="350" y="30" width="100" height="40" fill="#e74c3c" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
            </rect>
            <text x="400" y="45" text-anchor="middle" fill="white" font-size="10">Block 1</text>
            <text x="400" y="58" text-anchor="middle" fill="white" font-size="8">128MB</text>

            <rect x="350" y="80" width="100" height="40" fill="#e74c3c" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
            </rect>
            <text x="400" y="95" text-anchor="middle" fill="white" font-size="10">Block 2</text>
            <text x="400" y="108" text-anchor="middle" fill="white" font-size="8">128MB</text>

            <rect x="350" y="130" width="100" height="40" fill="#e74c3c" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.4s" fill="freeze"/>
            </rect>
            <text x="400" y="145" text-anchor="middle" fill="white" font-size="10">Block 3</text>
            <text x="400" y="158" text-anchor="middle" fill="white" font-size="8">128MB</text>

            <rect x="350" y="180" width="100" height="40" fill="#f39c12" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.6s" fill="freeze"/>
            </rect>
            <text x="400" y="195" text-anchor="middle" fill="white" font-size="10">Block 4</text>
            <text x="400" y="208" text-anchor="middle" fill="white" font-size="8">116MB</text>

            <!-- Distribution -->
            <path d="M470 50 L520 50" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </path>
            <path d="M470 100 L520 120" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </path>
            <path d="M470 150 L520 190" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </path>
            <path d="M470 200 L520 260" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </path>

            <!-- DataNodes -->
            <rect x="540" y="30" width="80" height="50" fill="#27ae60" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </rect>
            <text x="580" y="50" text-anchor="middle" fill="white" font-size="9">DataNode1</text>
            <text x="580" y="65" text-anchor="middle" fill="white" font-size="8">Block 1,3</text>

            <rect x="540" y="100" width="80" height="50" fill="#27ae60" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.7s" fill="freeze"/>
            </rect>
            <text x="580" y="120" text-anchor="middle" fill="white" font-size="9">DataNode2</text>
            <text x="580" y="135" text-anchor="middle" fill="white" font-size="8">Block 2,4</text>

            <rect x="540" y="170" width="80" height="50" fill="#27ae60" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.9s" fill="freeze"/>
            </rect>
            <text x="580" y="190" text-anchor="middle" fill="white" font-size="9">DataNode3</text>
            <text x="580" y="205" text-anchor="middle" fill="white" font-size="8">Block 1,2</text>

            <rect x="540" y="240" width="80" height="50" fill="#27ae60" rx="5" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.1s" fill="freeze"/>
            </rect>
            <text x="580" y="260" text-anchor="middle" fill="white" font-size="9">DataNode4</text>
            <text x="580" y="275" text-anchor="middle" fill="white" font-size="8">Block 3,4</text>

            <!-- Replication info -->
            <text x="700" y="150" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                副本分布
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
            <text x="700" y="170" fill="#2c3e50" font-size="10" opacity="0">
                每个块有3个副本
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
            <text x="700" y="185" fill="#2c3e50" font-size="10" opacity="0">
                分布在不同节点
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
        </svg>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
            <div>
                <h3>块大小的优势</h3>
                <ul>
                    <li>减少元数据存储开销</li>
                    <li>提高磁盘传输效率</li>
                    <li>简化存储管理</li>
                    <li>便于并行处理</li>
                </ul>
            </div>
            <div>
                <h3>块大小的考虑</h3>
                <ul>
                    <li>太小：元数据过多，寻址开销大</li>
                    <li>太大：并行度降低，传输时间长</li>
                    <li>默认128MB是经验值</li>
                    <li>可根据应用场景调整</li>
                </ul>
            </div>
        </div>

        <div class="slide-number">8 / 29</div>
    </div>

    <!-- Slide 9: NameNode Deep Dive -->
    <div class="slide">
        <h2>NameNode 深入解析</h2>

        <div class="highlight">
            <p>NameNode是HDFS的"大脑"，负责管理文件系统的命名空间和协调客户端对文件的访问。</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
            <div>
                <h3 style="color: #e74c3c;">🧠 核心职责</h3>
                <ul>
                    <li><strong>命名空间管理：</strong>维护文件系统目录树</li>
                    <li><strong>元数据存储：</strong>文件属性、权限、时间戳</li>
                    <li><strong>块映射管理：</strong>文件到数据块的映射</li>
                    <li><strong>副本策略：</strong>决定数据块的存放位置</li>
                    <li><strong>客户端协调：</strong>处理客户端的文件操作请求</li>
                    <li><strong>DataNode管理：</strong>监控DataNode状态</li>
                </ul>

                <h3 style="color: #3498db;">💾 内存结构</h3>
                <ul>
                    <li><strong>FsImage：</strong>文件系统镜像</li>
                    <li><strong>EditLog：</strong>操作日志</li>
                    <li><strong>BlockMap：</strong>块到DataNode的映射</li>
                    <li><strong>DataNode信息：</strong>节点状态和容量</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 350" style="width: 100%; height: 300px;">
                    <!-- NameNode -->
                    <rect x="50" y="50" width="300" height="250" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="200" y="35" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">NameNode 内存结构</text>

                    <!-- FsImage -->
                    <rect x="70" y="70" width="120" height="50" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="130" y="90" text-anchor="middle" fill="white" font-size="11" font-weight="bold">FsImage</text>
                    <text x="130" y="105" text-anchor="middle" fill="white" font-size="9">文件系统镜像</text>

                    <!-- EditLog -->
                    <rect x="210" y="70" width="120" height="50" fill="#e74c3c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
                    </rect>
                    <text x="270" y="90" text-anchor="middle" fill="white" font-size="11" font-weight="bold">EditLog</text>
                    <text x="270" y="105" text-anchor="middle" fill="white" font-size="9">操作日志</text>

                    <!-- BlockMap -->
                    <rect x="70" y="140" width="120" height="50" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
                    </rect>
                    <text x="130" y="160" text-anchor="middle" fill="white" font-size="11" font-weight="bold">BlockMap</text>
                    <text x="130" y="175" text-anchor="middle" fill="white" font-size="9">块映射表</text>

                    <!-- DataNode Info -->
                    <rect x="210" y="140" width="120" height="50" fill="#f39c12" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.6s" fill="freeze"/>
                    </rect>
                    <text x="270" y="160" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode</text>
                    <text x="270" y="175" text-anchor="middle" fill="white" font-size="9">节点信息</text>

                    <!-- Lease Manager -->
                    <rect x="70" y="210" width="120" height="50" fill="#9b59b6" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.8s" fill="freeze"/>
                    </rect>
                    <text x="130" y="230" text-anchor="middle" fill="white" font-size="11" font-weight="bold">LeaseManager</text>
                    <text x="130" y="245" text-anchor="middle" fill="white" font-size="9">租约管理</text>

                    <!-- Cache Manager -->
                    <rect x="210" y="210" width="120" height="50" fill="#1abc9c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="270" y="230" text-anchor="middle" fill="white" font-size="11" font-weight="bold">CacheManager</text>
                    <text x="270" y="245" text-anchor="middle" fill="white" font-size="9">缓存管理</text>
                </svg>
            </div>
        </div>

        <div class="code-block" style="margin-top: 20px;">
# NameNode启动过程
1. 加载FsImage到内存
2. 重放EditLog中的操作
3. 进入安全模式等待DataNode汇报
4. 退出安全模式，开始提供服务
        </div>

        <div class="slide-number">9 / 29</div>
    </div>

    <!-- Slide 10: NameNode Metadata -->
    <div class="slide">
        <h2>NameNode 元数据管理</h2>

        <div class="two-column">
            <div>
                <h3 style="color: #3498db;">📁 FsImage (文件系统镜像)</h3>
                <ul>
                    <li>存储文件系统的完整快照</li>
                    <li>包含目录结构和文件属性</li>
                    <li>启动时加载到内存</li>
                    <li>定期生成检查点</li>
                </ul>

                <div class="code-block" style="font-size: 1.1em;">
# FsImage内容示例
/
├── user/ (dir, 755, hdfs:supergroup)
│   └── data/ (dir, 755, user:hadoop)
│       └── file1.txt (file, 644, user:hadoop)
│           ├── size: 268435456 (256MB)
│           ├── blocks: 2
│           ├── replication: 3
│           └── mtime: 1640995200
                </div>

                <h3 style="color: #e74c3c;">📝 EditLog (编辑日志)</h3>
                <ul>
                    <li>记录所有文件系统修改操作</li>
                    <li>保证数据持久性</li>
                    <li>支持故障恢复</li>
                    <li>定期滚动生成新文件</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Memory -->
                    <rect x="50" y="50" width="300" height="100" fill="#3498db" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="200" y="80" text-anchor="middle" fill="white" font-size="14" font-weight="bold">内存中的命名空间</text>
                    <text x="200" y="100" text-anchor="middle" fill="white" font-size="11">FsImage + EditLog</text>
                    <text x="200" y="120" text-anchor="middle" fill="white" font-size="10">实时文件系统状态</text>

                    <!-- Disk Storage -->
                    <rect x="50" y="200" width="140" height="80" fill="#27ae60" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="120" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">FsImage</text>
                    <text x="120" y="240" text-anchor="middle" fill="white" font-size="9">fsimage_001</text>
                    <text x="120" y="255" text-anchor="middle" fill="white" font-size="9">fsimage_002</text>
                    <text x="120" y="270" text-anchor="middle" fill="white" font-size="9">磁盘持久化</text>

                    <rect x="210" y="200" width="140" height="80" fill="#e74c3c" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
                    </rect>
                    <text x="280" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">EditLog</text>
                    <text x="280" y="240" text-anchor="middle" fill="white" font-size="9">edits_001</text>
                    <text x="280" y="255" text-anchor="middle" fill="white" font-size="9">edits_002</text>
                    <text x="280" y="270" text-anchor="middle" fill="white" font-size="9">操作日志</text>

                    <!-- Arrows -->
                    <path d="M120 150 L120 200" stroke="#2c3e50" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </path>
                    <polygon points="115,195 120,205 125,195" fill="#2c3e50" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </polygon>
                    <text x="130" y="175" fill="#2c3e50" font-size="9" opacity="0">
                        检查点
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </text>

                    <path d="M280 150 L280 200" stroke="#2c3e50" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
                    </path>
                    <polygon points="275,195 280,205 285,195" fill="#2c3e50" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
                    </polygon>
                    <text x="290" y="175" fill="#2c3e50" font-size="9" opacity="0">
                        实时写入
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
                    </text>

                    <!-- Recovery Process -->
                    <text x="200" y="320" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        故障恢复过程
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </text>
                    <text x="200" y="340" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        1. 加载最新FsImage → 2. 重放EditLog → 3. 重建内存状态
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>关键机制：</strong>NameNode通过FsImage和EditLog的组合，既保证了高性能的内存访问，又确保了数据的持久性和一致性。</p>
        </div>

        <div class="slide-number">10 / 29</div>
    </div>

    <!-- Slide 11: DataNode Deep Dive -->
    <div class="slide">
        <h2>DataNode 深入解析</h2>

        <div class="highlight">
            <p>DataNode是HDFS的"工作马"，负责实际的数据存储和数据块管理。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #27ae60;">🔧 核心功能</h3>
                <ul>
                    <li><strong>数据块存储：</strong>在本地磁盘存储数据块</li>
                    <li><strong>数据校验：</strong>定期检查数据完整性</li>
                    <li><strong>心跳机制：</strong>定期向NameNode报告状态</li>
                    <li><strong>块报告：</strong>汇报本地存储的所有数据块</li>
                    <li><strong>数据传输：</strong>响应客户端读写请求</li>
                    <li><strong>副本管理：</strong>参与数据复制和恢复</li>
                </ul>

                <h3 style="color: #e74c3c;">📊 工作流程</h3>
                <div class="code-block" style="font-size: 1.1em;">
# DataNode启动流程
1. 读取配置文件
2. 初始化存储目录
3. 向NameNode注册
4. 发送块报告
5. 开始心跳循环
6. 处理客户端请求
                </div>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- DataNode -->
                    <rect x="50" y="30" width="300" height="320" fill="#ecf0f1" stroke="#27ae60" stroke-width="3" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="200" y="20" text-anchor="middle" fill="#27ae60" font-size="14" font-weight="bold">DataNode 架构</text>

                    <!-- Storage Directories -->
                    <rect x="70" y="50" width="100" height="60" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="120" y="70" text-anchor="middle" fill="white" font-size="10" font-weight="bold">存储目录1</text>
                    <text x="120" y="85" text-anchor="middle" fill="white" font-size="8">/data1/hadoop</text>
                    <text x="120" y="100" text-anchor="middle" fill="white" font-size="8">Block 1,4,7</text>

                    <rect x="230" y="50" width="100" height="60" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
                    </rect>
                    <text x="280" y="70" text-anchor="middle" fill="white" font-size="10" font-weight="bold">存储目录2</text>
                    <text x="280" y="85" text-anchor="middle" fill="white" font-size="8">/data2/hadoop</text>
                    <text x="280" y="100" text-anchor="middle" fill="white" font-size="8">Block 2,5,8</text>

                    <!-- Block Scanner -->
                    <rect x="70" y="130" width="260" height="40" fill="#e74c3c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
                    </rect>
                    <text x="200" y="145" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Block Scanner</text>
                    <text x="200" y="160" text-anchor="middle" fill="white" font-size="9">数据块扫描器 - 检查数据完整性</text>

                    <!-- Data Transfer -->
                    <rect x="70" y="190" width="120" height="50" fill="#f39c12" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.6s" fill="freeze"/>
                    </rect>
                    <text x="130" y="210" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Data Transfer</text>
                    <text x="130" y="225" text-anchor="middle" fill="white" font-size="8">数据传输服务</text>

                    <!-- Heartbeat -->
                    <rect x="210" y="190" width="120" height="50" fill="#9b59b6" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.8s" fill="freeze"/>
                    </rect>
                    <text x="270" y="210" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Heartbeat</text>
                    <text x="270" y="225" text-anchor="middle" fill="white" font-size="8">心跳服务</text>

                    <!-- Block Report -->
                    <rect x="70" y="260" width="120" height="50" fill="#1abc9c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="130" y="280" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Block Report</text>
                    <text x="130" y="295" text-anchor="middle" fill="white" font-size="8">块报告服务</text>

                    <!-- Replication -->
                    <rect x="210" y="260" width="120" height="50" fill="#e67e22" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
                    </rect>
                    <text x="270" y="280" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Replication</text>
                    <text x="270" y="295" text-anchor="middle" fill="white" font-size="8">副本管理</text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>关键特点：</strong>DataNode是无状态的，可以动态添加和移除。所有的元数据信息都由NameNode管理，DataNode只负责数据的物理存储和传输。</p>
        </div>

        <div class="slide-number">11 / 29</div>
    </div>

    <!-- Slide 12: Secondary NameNode -->
    <div class="slide">
        <h2>Secondary NameNode</h2>

        <div class="highlight">
            <p>Secondary NameNode不是NameNode的备份，而是帮助NameNode进行检查点操作的辅助服务。</p>
        </div>

        <svg class="svg-animation" viewBox="0 0 900 400" style="height: 350px;">
            <!-- NameNode -->
            <rect x="50" y="100" width="150" height="100" fill="#e74c3c" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="125" y="130" text-anchor="middle" fill="white" font-size="12" font-weight="bold">NameNode</text>
            <text x="125" y="150" text-anchor="middle" fill="white" font-size="10">FsImage + EditLog</text>
            <text x="125" y="170" text-anchor="middle" fill="white" font-size="10">内存中运行</text>

            <!-- Secondary NameNode -->
            <rect x="350" y="100" width="150" height="100" fill="#f39c12" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </rect>
            <text x="425" y="125" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Secondary</text>
            <text x="425" y="140" text-anchor="middle" fill="white" font-size="11" font-weight="bold">NameNode</text>
            <text x="425" y="160" text-anchor="middle" fill="white" font-size="10">检查点服务</text>
            <text x="425" y="180" text-anchor="middle" fill="white" font-size="10">合并操作</text>

            <!-- Checkpoint Process -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                <!-- Step 1 -->
                <path d="M200 130 L350 130" stroke="#3498db" stroke-width="2" fill="none"/>
                <polygon points="345,125 355,130 345,135" fill="#3498db"/>
                <text x="275" y="120" text-anchor="middle" fill="#3498db" font-size="9">1. 获取FsImage</text>

                <!-- Step 2 -->
                <path d="M200 150 L350 150" stroke="#27ae60" stroke-width="2" fill="none"/>
                <polygon points="345,145 355,150 345,155" fill="#27ae60"/>
                <text x="275" y="140" text-anchor="middle" fill="#27ae60" font-size="9">2. 获取EditLog</text>

                <!-- Step 3 -->
                <path d="M350 170 L200 170" stroke="#9b59b6" stroke-width="2" fill="none"/>
                <polygon points="205,165 195,170 205,175" fill="#9b59b6"/>
                <text x="275" y="185" text-anchor="middle" fill="#9b59b6" font-size="9">3. 返回新FsImage</text>
            </g>

            <!-- Process Details -->
            <rect x="550" y="50" width="300" height="300" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </rect>
            <text x="700" y="40" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">检查点过程</text>

            <text x="570" y="80" fill="#2c3e50" font-size="11" font-weight="bold" opacity="0">
                1. 触发条件
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </text>
            <text x="580" y="100" fill="#2c3e50" font-size="10" opacity="0">
                • 时间间隔 (默认1小时)
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </text>
            <text x="580" y="115" fill="#2c3e50" font-size="10" opacity="0">
                • EditLog大小达到阈值
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </text>

            <text x="570" y="140" fill="#2c3e50" font-size="11" font-weight="bold" opacity="0">
                2. 执行步骤
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="580" y="160" fill="#2c3e50" font-size="10" opacity="0">
                • 从NameNode下载FsImage
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="580" y="175" fill="#2c3e50" font-size="10" opacity="0">
                • 从NameNode下载EditLog
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="580" y="190" fill="#2c3e50" font-size="10" opacity="0">
                • 在本地合并生成新FsImage
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="580" y="205" fill="#2c3e50" font-size="10" opacity="0">
                • 上传新FsImage到NameNode
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>
            <text x="580" y="220" fill="#2c3e50" font-size="10" opacity="0">
                • NameNode创建新的EditLog
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
            </text>

            <text x="570" y="250" fill="#2c3e50" font-size="11" font-weight="bold" opacity="0">
                3. 重要作用
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
            <text x="580" y="270" fill="#2c3e50" font-size="10" opacity="0">
                • 减少NameNode启动时间
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
            <text x="580" y="285" fill="#2c3e50" font-size="10" opacity="0">
                • 防止EditLog过大
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
            <text x="580" y="300" fill="#2c3e50" font-size="10" opacity="0">
                • 提供FsImage备份
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
            </text>
        </svg>

        <div class="slide-number">12 / 29</div>
    </div>

    <!-- Slide 13: Client Interaction -->
    <div class="slide">
        <h2>客户端交互模式</h2>

        <div class="highlight">
            <p>HDFS客户端通过特定的协议与NameNode和DataNode交互，实现文件的读写操作。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #3498db;">🔄 交互流程</h3>
                <ul>
                    <li><strong>元数据操作：</strong>与NameNode通信</li>
                    <li><strong>数据操作：</strong>直接与DataNode通信</li>
                    <li><strong>负载均衡：</strong>选择最近的DataNode</li>
                    <li><strong>错误处理：</strong>自动重试和故障转移</li>
                </ul>

                <h3 style="color: #e74c3c;">📋 客户端API</h3>
                <div class="code-block" style="font-size: 1.1em;">
// Java API示例
FileSystem fs = FileSystem.get(conf);

// 创建文件
FSDataOutputStream out =
    fs.create(new Path("/user/data/file.txt"));

// 读取文件
FSDataInputStream in =
    fs.open(new Path("/user/data/file.txt"));

// 列出目录
FileStatus[] status =
    fs.listStatus(new Path("/user/data"));
                </div>
            </div>

            <div>
                <svg viewBox="0 0 400 350" style="width: 100%; height: 300px;">
                    <!-- Client -->
                    <rect x="50" y="150" width="80" height="50" fill="#9b59b6" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="90" y="170" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Client</text>
                    <text x="90" y="185" text-anchor="middle" fill="white" font-size="9">客户端</text>

                    <!-- NameNode -->
                    <rect x="200" y="50" width="100" height="60" fill="#e74c3c" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="250" y="75" text-anchor="middle" fill="white" font-size="11" font-weight="bold">NameNode</text>
                    <text x="250" y="90" text-anchor="middle" fill="white" font-size="9">元数据管理</text>

                    <!-- DataNodes -->
                    <rect x="200" y="150" width="80" height="50" fill="#27ae60" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </rect>
                    <text x="240" y="170" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode1</text>
                    <text x="240" y="185" text-anchor="middle" fill="white" font-size="8">数据存储</text>

                    <rect x="200" y="220" width="80" height="50" fill="#27ae60" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.7s" fill="freeze"/>
                    </rect>
                    <text x="240" y="240" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode2</text>
                    <text x="240" y="255" text-anchor="middle" fill="white" font-size="8">数据存储</text>

                    <rect x="200" y="290" width="80" height="50" fill="#27ae60" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.9s" fill="freeze"/>
                    </rect>
                    <text x="240" y="310" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode3</text>
                    <text x="240" y="325" text-anchor="middle" fill="white" font-size="8">数据存储</text>

                    <!-- Connections -->
                    <!-- Client to NameNode -->
                    <path d="M130 165 L200 90" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </path>
                    <text x="150" y="120" fill="#3498db" font-size="9" opacity="0">
                        1. 元数据请求
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </text>

                    <!-- Client to DataNodes -->
                    <path d="M130 175 L200 175" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>
                    <path d="M130 180 L200 245" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>
                    <path d="M130 185 L200 315" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>
                    <text x="140" y="210" fill="#27ae60" font-size="9" opacity="0">
                        2. 数据读写
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>设计优势：</strong>元数据和数据操作分离，NameNode只处理轻量级的元数据请求，大量的数据传输直接在客户端和DataNode之间进行，避免了NameNode成为性能瓶颈。</p>
        </div>

        <div class="slide-number">13 / 29</div>
    </div>

    <!-- Slide 14: File Read Process -->
    <div class="slide">
        <h2>HDFS 文件读取流程</h2>

        <div class="highlight">
            <p>HDFS的文件读取过程涉及客户端、NameNode和DataNode之间的协调工作。</p>
        </div>

        <svg class="svg-animation" viewBox="0 0 900 450" style="height: 400px;">
            <!-- Client -->
            <rect x="50" y="200" width="100" height="60" fill="#9b59b6" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="100" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Client</text>
            <text x="100" y="240" text-anchor="middle" fill="white" font-size="10">客户端</text>

            <!-- NameNode -->
            <rect x="300" y="50" width="120" height="80" fill="#e74c3c" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
            </rect>
            <text x="360" y="80" text-anchor="middle" fill="white" font-size="12" font-weight="bold">NameNode</text>
            <text x="360" y="100" text-anchor="middle" fill="white" font-size="10">元数据管理</text>

            <!-- DataNodes -->
            <rect x="550" y="150" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </rect>
            <text x="600" y="175" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode1</text>
            <text x="600" y="190" text-anchor="middle" fill="white" font-size="9">Block A, C</text>

            <rect x="550" y="230" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
            </rect>
            <text x="600" y="255" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode2</text>
            <text x="600" y="270" text-anchor="middle" fill="white" font-size="9">Block B, A</text>

            <rect x="550" y="310" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
            </rect>
            <text x="600" y="335" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode3</text>
            <text x="600" y="350" text-anchor="middle" fill="white" font-size="9">Block C, B</text>

            <!-- Step 1: Request metadata -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                <path d="M150 220 L300 100" stroke="#3498db" stroke-width="3" fill="none"/>
                <polygon points="295,105 305,100 295,95" fill="#3498db"/>
                <text x="200" y="150" fill="#3498db" font-size="11" font-weight="bold">1. 请求文件元数据</text>
                <text x="200" y="165" fill="#3498db" font-size="9">open("/user/data/file.txt")</text>
            </g>

            <!-- Step 2: Return block locations -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                <path d="M300 110 L150 230" stroke="#e74c3c" stroke-width="3" fill="none"/>
                <polygon points="155,225 145,230 155,235" fill="#e74c3c"/>
                <text x="180" y="180" fill="#e74c3c" font-size="11" font-weight="bold">2. 返回块位置信息</text>
                <text x="180" y="195" fill="#e74c3c" font-size="9">Block A: DN1,DN2</text>
                <text x="180" y="208" fill="#e74c3c" font-size="9">Block B: DN2,DN3</text>
            </g>

            <!-- Step 3: Read from DataNodes -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                <path d="M150 240 L550 180" stroke="#27ae60" stroke-width="3" fill="none"/>
                <polygon points="545,185 555,180 545,175" fill="#27ae60"/>
                <text x="300" y="220" fill="#27ae60" font-size="11" font-weight="bold">3. 直接从DataNode读取</text>
                <text x="300" y="235" fill="#27ae60" font-size="9">选择最近的副本</text>
            </g>

            <!-- Step 4: Data stream -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                <path d="M550 190 L150 250" stroke="#f39c12" stroke-width="3" fill="none"/>
                <polygon points="155,245 145,250 155,255" fill="#f39c12"/>
                <text x="300" y="270" fill="#f39c12" font-size="11" font-weight="bold">4. 数据流传输</text>
                <text x="300" y="285" fill="#f39c12" font-size="9">流式读取数据块</text>
            </g>
        </svg>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
            <div>
                <h3>读取步骤详解</h3>
                <ol style="font-size: 1.6em; line-height: 1.8;">
                    <li><strong>打开文件：</strong>客户端调用FileSystem.open()</li>
                    <li><strong>获取元数据：</strong>向NameNode请求文件的块位置信息</li>
                    <li><strong>选择DataNode：</strong>根据网络拓扑选择最近的副本</li>
                    <li><strong>建立连接：</strong>与选定的DataNode建立数据连接</li>
                    <li><strong>流式读取：</strong>按顺序读取各个数据块</li>
                    <li><strong>故障处理：</strong>如果DataNode失败，自动切换到其他副本</li>
                </ol>
            </div>
            <div>
                <h3>性能优化特点</h3>
                <ul style="font-size: 1.6em; line-height: 1.8;">
                    <li><strong>就近访问：</strong>优先选择本地或同机架的DataNode</li>
                    <li><strong>并行读取：</strong>可以同时从多个DataNode读取不同块</li>
                    <li><strong>缓存机制：</strong>客户端缓存块位置信息</li>
                    <li><strong>流水线处理：</strong>边读取边处理数据</li>
                    <li><strong>自动重试：</strong>网络异常时自动重试</li>
                </ul>
            </div>
        </div>

        <div class="slide-number">14 / 29</div>
    </div>

    <!-- Slide 15: File Write Process -->
    <div class="slide">
        <h2>HDFS 文件写入流程</h2>

        <div class="highlight">
            <p>HDFS的文件写入过程更加复杂，涉及数据块的分割、副本的创建和管道式传输。</p>
        </div>

        <svg class="svg-animation" viewBox="0 0 900 500" style="height: 450px;">
            <!-- Client -->
            <rect x="50" y="200" width="100" height="60" fill="#9b59b6" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="100" y="225" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Client</text>
            <text x="100" y="240" text-anchor="middle" fill="white" font-size="10">客户端</text>

            <!-- NameNode -->
            <rect x="300" y="50" width="120" height="80" fill="#e74c3c" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
            </rect>
            <text x="360" y="80" text-anchor="middle" fill="white" font-size="12" font-weight="bold">NameNode</text>
            <text x="360" y="100" text-anchor="middle" fill="white" font-size="10">元数据管理</text>

            <!-- DataNodes Pipeline -->
            <rect x="550" y="150" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </rect>
            <text x="600" y="175" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode1</text>
            <text x="600" y="190" text-anchor="middle" fill="white" font-size="9">主副本</text>

            <rect x="700" y="230" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
            </rect>
            <text x="750" y="255" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode2</text>
            <text x="750" y="270" text-anchor="middle" fill="white" font-size="9">副本2</text>

            <rect x="550" y="310" width="100" height="60" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
            </rect>
            <text x="600" y="335" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DataNode3</text>
            <text x="600" y="350" text-anchor="middle" fill="white" font-size="9">副本3</text>

            <!-- Step 1: Create file -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                <path d="M150 220 L300 100" stroke="#3498db" stroke-width="3" fill="none"/>
                <polygon points="295,105 305,100 295,95" fill="#3498db"/>
                <text x="200" y="150" fill="#3498db" font-size="11" font-weight="bold">1. 创建文件</text>
                <text x="200" y="165" fill="#3498db" font-size="9">create("/user/data/file.txt")</text>
            </g>

            <!-- Step 2: Allocate blocks -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                <path d="M300 110 L150 230" stroke="#e74c3c" stroke-width="3" fill="none"/>
                <polygon points="155,225 145,230 155,235" fill="#e74c3c"/>
                <text x="180" y="180" fill="#e74c3c" font-size="11" font-weight="bold">2. 分配数据块</text>
                <text x="180" y="195" fill="#e74c3c" font-size="9">返回DataNode列表</text>
            </g>

            <!-- Step 3: Pipeline setup -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                <path d="M150 240 L550 180" stroke="#27ae60" stroke-width="3" fill="none"/>
                <polygon points="545,185 555,180 545,175" fill="#27ae60"/>
                <text x="300" y="220" fill="#27ae60" font-size="11" font-weight="bold">3. 建立管道</text>
                <text x="300" y="235" fill="#27ae60" font-size="9">连接到第一个DataNode</text>
            </g>

            <!-- Pipeline connections -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                <path d="M650 180 L700 250" stroke="#f39c12" stroke-width="2" fill="none"/>
                <polygon points="695,245 705,250 695,255" fill="#f39c12"/>
                <path d="M700 280 L650 340" stroke="#f39c12" stroke-width="2" fill="none"/>
                <polygon points="655,335 645,340 655,345" fill="#f39c12"/>
                <text x="720" y="200" fill="#f39c12" font-size="10">管道传输</text>
            </g>

            <!-- Step 4: Data flow -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                <path d="M150 250 L550 190" stroke="#9b59b6" stroke-width="3" fill="none"/>
                <polygon points="545,195 555,190 545,185" fill="#9b59b6"/>
                <text x="300" y="270" fill="#9b59b6" font-size="11" font-weight="bold">4. 数据写入</text>
                <text x="300" y="285" fill="#9b59b6" font-size="9">流水线式传输</text>
            </g>

            <!-- Acknowledgment -->
            <g opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="6s" fill="freeze"/>
                <path d="M550 200 L150 260" stroke="#e67e22" stroke-width="2" stroke-dasharray="5,5" fill="none"/>
                <polygon points="155,255 145,260 155,265" fill="#e67e22"/>
                <text x="300" y="320" fill="#e67e22" font-size="11" font-weight="bold">5. 确认应答</text>
                <text x="300" y="335" fill="#e67e22" font-size="9">逐级返回ACK</text>
            </g>
        </svg>

        <div class="slide-number">15 / 29</div>
    </div>

    <!-- Slide 16: Write Pipeline Details -->
    <div class="slide">
        <h2>写入管道详解</h2>

        <div class="highlight">
            <p>HDFS使用管道机制来高效地创建数据块的多个副本，确保数据的可靠性和写入性能。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #e74c3c;">🔄 管道写入过程</h3>
                <ol style="font-size: 1.5em; line-height: 1.8;">
                    <li><strong>建立管道：</strong>客户端连接到第一个DataNode</li>
                    <li><strong>数据传输：</strong>客户端向第一个DataNode发送数据</li>
                    <li><strong>副本复制：</strong>第一个DataNode同时写入本地并转发给下一个</li>
                    <li><strong>链式传播：</strong>数据沿着管道依次传播</li>
                    <li><strong>确认应答：</strong>从最后一个DataNode开始逐级确认</li>
                    <li><strong>完成写入：</strong>所有副本写入成功后返回客户端</li>
                </ol>

                <div class="code-block" style="font-size: 1.1em; margin-top: 15px;">
# 写入管道示例
Client → DataNode1 → DataNode2 → DataNode3
         ↓           ↓           ↓
       写入本地    写入本地    写入本地
         ↓           ↓           ↓
       转发数据    转发数据      ACK
         ↓           ↓           ↑
       接收ACK     接收ACK     发送ACK
         ↓           ↑
       发送ACK     发送ACK
         ↑
       最终ACK
                </div>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Pipeline visualization -->
                    <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">写入管道流程</text>

                    <!-- Client -->
                    <rect x="50" y="60" width="80" height="40" fill="#9b59b6" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="90" y="80" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Client</text>

                    <!-- DataNode1 -->
                    <rect x="50" y="130" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="90" y="150" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode1</text>

                    <!-- DataNode2 -->
                    <rect x="160" y="200" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="200" y="220" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode2</text>

                    <!-- DataNode3 -->
                    <rect x="270" y="270" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </rect>
                    <text x="310" y="290" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNode3</text>

                    <!-- Data flow arrows -->
                    <g opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                        <path d="M90 100 L90 130" stroke="#3498db" stroke-width="3" fill="none"/>
                        <polygon points="85,125 90,135 95,125" fill="#3498db"/>
                        <text x="100" y="120" fill="#3498db" font-size="9">数据</text>

                        <path d="M130 150 L160 210" stroke="#3498db" stroke-width="3" fill="none"/>
                        <polygon points="155,205 165,210 155,215" fill="#3498db"/>

                        <path d="M240 220 L270 280" stroke="#3498db" stroke-width="3" fill="none"/>
                        <polygon points="265,275 275,280 265,285" fill="#3498db"/>
                    </g>

                    <!-- ACK flow arrows -->
                    <g opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                        <path d="M270 290 L240 230" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3" fill="none"/>
                        <polygon points="245,235 235,230 245,225" fill="#e74c3c"/>
                        <text x="250" y="250" fill="#e74c3c" font-size="9">ACK</text>

                        <path d="M160 230 L130 160" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3" fill="none"/>
                        <polygon points="135,165 125,160 135,155" fill="#e74c3c"/>

                        <path d="M90 170 L90 100" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3" fill="none"/>
                        <polygon points="85,105 90,95 95,105" fill="#e74c3c"/>
                    </g>

                    <!-- Benefits -->
                    <text x="200" y="340" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        管道优势
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                    </text>
                    <text x="200" y="360" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        • 并行写入提高效率  • 减少网络传输  • 保证数据一致性
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="slide-number">16 / 29</div>
    </div>

    <!-- Slide 17: Replication Strategy -->
    <div class="slide">
        <h2>数据复制策略</h2>

        <div class="highlight">
            <p>HDFS通过智能的副本放置策略来平衡数据可靠性、读写性能和网络带宽使用。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #e74c3c;">🎯 默认复制策略</h3>
                <ul style="font-size: 1.5em; line-height: 1.8;">
                    <li><strong>第一个副本：</strong>写入客户端所在节点（本地写入）</li>
                    <li><strong>第二个副本：</strong>放置在不同机架的随机节点</li>
                    <li><strong>第三个副本：</strong>与第二个副本在同一机架的不同节点</li>
                    <li><strong>更多副本：</strong>随机选择，但避免过度集中</li>
                </ul>

                <h3 style="color: #3498db;">⚖️ 设计权衡</h3>
                <ul style="font-size: 1.5em; line-height: 1.8;">
                    <li><strong>可靠性：</strong>机架级别的容错能力</li>
                    <li><strong>性能：</strong>本地读取和机架内传输</li>
                    <li><strong>带宽：</strong>减少跨机架网络流量</li>
                    <li><strong>负载：</strong>避免热点节点</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Rack 1 -->
                    <rect x="50" y="50" width="120" height="150" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="110" y="40" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">机架 1</text>

                    <!-- Client Node -->
                    <rect x="70" y="70" width="80" height="30" fill="#9b59b6" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="110" y="85" text-anchor="middle" fill="white" font-size="9" font-weight="bold">Client Node</text>
                    <circle cx="140" cy="85" r="8" fill="#e74c3c" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </circle>
                    <text x="140" y="88" text-anchor="middle" fill="white" font-size="8" font-weight="bold">1</text>

                    <!-- Other nodes in Rack 1 -->
                    <rect x="70" y="110" width="80" height="30" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
                    </rect>
                    <text x="110" y="125" text-anchor="middle" fill="white" font-size="9">DataNode</text>

                    <rect x="70" y="150" width="80" height="30" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
                    </rect>
                    <text x="110" y="165" text-anchor="middle" fill="white" font-size="9">DataNode</text>
                    <circle cx="140" cy="165" r="8" fill="#f39c12" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                    </circle>
                    <text x="140" y="168" text-anchor="middle" fill="white" font-size="8" font-weight="bold">3</text>

                    <!-- Rack 2 -->
                    <rect x="230" y="50" width="120" height="150" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="290" y="40" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">机架 2</text>

                    <!-- Nodes in Rack 2 -->
                    <rect x="250" y="70" width="80" height="30" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.6s" fill="freeze"/>
                    </rect>
                    <text x="290" y="85" text-anchor="middle" fill="white" font-size="9">DataNode</text>
                    <circle cx="320" cy="85" r="8" fill="#3498db" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </circle>
                    <text x="320" y="88" text-anchor="middle" fill="white" font-size="8" font-weight="bold">2</text>

                    <rect x="250" y="110" width="80" height="30" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.8s" fill="freeze"/>
                    </rect>
                    <text x="290" y="125" text-anchor="middle" fill="white" font-size="9">DataNode</text>

                    <rect x="250" y="150" width="80" height="30" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="290" y="165" text-anchor="middle" fill="white" font-size="9">DataNode</text>

                    <!-- Network connections -->
                    <path d="M170 85 L250 85" stroke="#2c3e50" stroke-width="2" stroke-dasharray="5,5" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </path>
                    <text x="210" y="75" text-anchor="middle" fill="#2c3e50" font-size="8">跨机架</text>

                    <path d="M110 100 L110 150" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                    </path>
                    <text x="130" y="130" fill="#27ae60" font-size="8">机架内</text>

                    <!-- Legend -->
                    <text x="200" y="230" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        副本放置顺序
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>
                    <circle cx="150" cy="250" r="8" fill="#e74c3c" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </circle>
                    <text x="150" y="253" text-anchor="middle" fill="white" font-size="8" font-weight="bold">1</text>
                    <text x="170" y="255" fill="#2c3e50" font-size="10" opacity="0">
                        第一个副本（本地）
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>

                    <circle cx="150" cy="270" r="8" fill="#3498db" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </circle>
                    <text x="150" y="273" text-anchor="middle" fill="white" font-size="8" font-weight="bold">2</text>
                    <text x="170" y="275" fill="#2c3e50" font-size="10" opacity="0">
                        第二个副本（远程机架）
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>

                    <circle cx="150" cy="290" r="8" fill="#f39c12" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </circle>
                    <text x="150" y="293" text-anchor="middle" fill="white" font-size="8" font-weight="bold">3</text>
                    <text x="170" y="295" fill="#2c3e50" font-size="10" opacity="0">
                        第三个副本（同机架）
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>策略优势：</strong>这种放置策略在单个节点或整个机架故障时仍能保证数据可用性，同时最小化网络带宽消耗。</p>
        </div>

        <div class="slide-number">17 / 29</div>
    </div>

    <!-- Slide 18: Fault Tolerance -->
    <div class="slide">
        <h2>故障容错机制</h2>

        <div class="highlight">
            <p>HDFS设计时假设硬件故障是常态，因此内置了完善的故障检测和恢复机制。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #e74c3c;">🚨 故障检测</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>心跳机制：</strong>DataNode定期向NameNode发送心跳</li>
                    <li><strong>超时检测：</strong>超过10分钟无心跳则认为节点失效</li>
                    <li><strong>数据校验：</strong>使用CRC32校验和检测数据损坏</li>
                    <li><strong>块报告：</strong>定期汇报本地存储的数据块状态</li>
                </ul>

                <h3 style="color: #27ae60;">🔧 故障恢复</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>副本重建：</strong>自动创建丢失数据块的新副本</li>
                    <li><strong>负载均衡：</strong>重新分布数据以平衡存储</li>
                    <li><strong>坏块处理：</strong>隔离和替换损坏的数据块</li>
                    <li><strong>节点替换：</strong>新节点自动加入集群</li>
                </ul>

                <div class="code-block" style="font-size: 1.1em; margin-top: 15px;">
# 故障恢复示例
1. DataNode2 故障
2. NameNode检测到心跳超时
3. 标记DataNode2为失效
4. 扫描受影响的数据块
5. 在其他节点创建新副本
6. 更新块映射表
7. 通知客户端新的位置信息
                </div>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Normal state -->
                    <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">故障恢复过程</text>

                    <!-- NameNode -->
                    <rect x="150" y="50" width="100" height="40" fill="#e74c3c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="200" y="70" text-anchor="middle" fill="white" font-size="10" font-weight="bold">NameNode</text>

                    <!-- DataNodes - Normal -->
                    <rect x="50" y="120" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="90" y="135" text-anchor="middle" fill="white" font-size="9">DataNode1</text>
                    <text x="90" y="150" text-anchor="middle" fill="white" font-size="8">Block A,B</text>

                    <rect x="160" y="120" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.7s" fill="freeze"/>
                        <animate attributeName="fill" values="#27ae60;#e74c3c" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="200" y="135" text-anchor="middle" fill="white" font-size="9">DataNode2</text>
                    <text x="200" y="150" text-anchor="middle" fill="white" font-size="8">Block A,C</text>

                    <rect x="270" y="120" width="80" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.9s" fill="freeze"/>
                    </rect>
                    <text x="310" y="135" text-anchor="middle" fill="white" font-size="9">DataNode3</text>
                    <text x="310" y="150" text-anchor="middle" fill="white" font-size="8">Block B,C</text>

                    <!-- Failure indication -->
                    <text x="200" y="180" text-anchor="middle" fill="#e74c3c" font-size="12" font-weight="bold" opacity="0">
                        ❌ DataNode2 故障
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </text>

                    <!-- Recovery process -->
                    <text x="200" y="210" text-anchor="middle" fill="#2c3e50" font-size="11" font-weight="bold" opacity="0">
                        恢复过程
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </text>

                    <!-- New DataNode -->
                    <rect x="160" y="240" width="80" height="40" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                    </rect>
                    <text x="200" y="255" text-anchor="middle" fill="white" font-size="9">DataNode4</text>
                    <text x="200" y="270" text-anchor="middle" fill="white" font-size="8">Block A,C</text>

                    <!-- Recovery arrows -->
                    <path d="M90 160 L180 240" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                    </path>
                    <text x="120" y="200" fill="#3498db" font-size="8" opacity="0">
                        复制Block A
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                    </text>

                    <path d="M310 160 L220 240" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </path>
                    <text x="280" y="200" fill="#3498db" font-size="8" opacity="0">
                        复制Block C
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>

                    <!-- Status -->
                    <text x="200" y="320" text-anchor="middle" fill="#27ae60" font-size="11" font-weight="bold" opacity="0">
                        ✅ 恢复完成
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="6s" fill="freeze"/>
                    </text>
                    <text x="200" y="340" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        所有数据块副本数恢复正常
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="6s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="slide-number">18 / 29</div>
    </div>

    <!-- Slide 19: HDFS High Availability -->
    <div class="slide">
        <h2>HDFS 高可用性 (HA)</h2>

        <div class="highlight">
            <p>为了解决NameNode单点故障问题，HDFS 2.0引入了高可用性机制，通过Active/Standby模式实现故障自动切换。</p>
        </div>

        <svg class="svg-animation" viewBox="0 0 900 400" style="height: 350px;">
            <!-- Active NameNode -->
            <rect x="100" y="100" width="150" height="80" fill="#27ae60" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
            </rect>
            <text x="175" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Active NameNode</text>
            <text x="175" y="145" text-anchor="middle" fill="white" font-size="10">主节点</text>
            <text x="175" y="165" text-anchor="middle" fill="white" font-size="10">处理客户端请求</text>

            <!-- Standby NameNode -->
            <rect x="350" y="100" width="150" height="80" fill="#f39c12" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
            </rect>
            <text x="425" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Standby NameNode</text>
            <text x="425" y="145" text-anchor="middle" fill="white" font-size="10">备用节点</text>
            <text x="425" y="165" text-anchor="middle" fill="white" font-size="10">同步元数据</text>

            <!-- Shared Storage -->
            <rect x="200" y="250" width="200" height="60" fill="#3498db" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
            </rect>
            <text x="300" y="275" text-anchor="middle" fill="white" font-size="12" font-weight="bold">共享存储</text>
            <text x="300" y="295" text-anchor="middle" fill="white" font-size="10">QJM / NFS</text>

            <!-- ZooKeeper -->
            <rect x="550" y="100" width="120" height="80" fill="#9b59b6" rx="10" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
            </rect>
            <text x="610" y="130" text-anchor="middle" fill="white" font-size="11" font-weight="bold">ZooKeeper</text>
            <text x="610" y="150" text-anchor="middle" fill="white" font-size="10">故障检测</text>
            <text x="610" y="165" text-anchor="middle" fill="white" font-size="10">自动切换</text>

            <!-- Connections -->
            <!-- Active to Shared Storage -->
            <path d="M175 180 L250 250" stroke="#27ae60" stroke-width="3" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
            </path>
            <text x="190" y="220" fill="#27ae60" font-size="9" opacity="0">
                写入EditLog
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
            </text>

            <!-- Standby to Shared Storage -->
            <path d="M425 180 L350 250" stroke="#f39c12" stroke-width="3" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
            </path>
            <text x="380" y="220" fill="#f39c12" font-size="9" opacity="0">
                读取EditLog
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
            </text>

            <!-- ZooKeeper connections -->
            <path d="M250 140 L550 140" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
            </path>
            <path d="M500 140 L550 140" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
            </path>
            <text x="400" y="130" fill="#9b59b6" font-size="9" opacity="0">
                心跳监控
                <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
            </text>

            <!-- DataNodes -->
            <rect x="700" y="200" width="100" height="50" fill="#e74c3c" rx="8" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
            </rect>
            <text x="750" y="220" text-anchor="middle" fill="white" font-size="10" font-weight="bold">DataNodes</text>
            <text x="750" y="235" text-anchor="middle" fill="white" font-size="9">数据存储</text>

            <!-- Active to DataNodes -->
            <path d="M250 140 L700 225" stroke="#27ae60" stroke-width="2" fill="none" opacity="0">
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </path>
            <text x="450" y="180" fill="#27ae60" font-size="9" opacity="0">
                管理DataNode
                <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
            </text>
        </svg>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
            <div>
                <h3>HA 组件</h3>
                <ul style="font-size: 1.5em; line-height: 1.8;">
                    <li><strong>Active NameNode：</strong>处理所有客户端请求</li>
                    <li><strong>Standby NameNode：</strong>保持热备状态</li>
                    <li><strong>共享存储：</strong>存储EditLog，实现状态同步</li>
                    <li><strong>ZooKeeper：</strong>故障检测和自动切换</li>
                </ul>
            </div>
            <div>
                <h3>切换过程</h3>
                <ol style="font-size: 1.5em; line-height: 1.8;">
                    <li>ZooKeeper检测到Active故障</li>
                    <li>触发故障转移过程</li>
                    <li>Standby读取最新EditLog</li>
                    <li>Standby切换为Active状态</li>
                    <li>客户端重新连接新Active</li>
                </ol>
            </div>
        </div>

        <div class="slide-number">19 / 29</div>
    </div>

    <!-- Slide 20: HDFS Federation -->
    <div class="slide">
        <h2>HDFS 联邦机制</h2>

        <div class="highlight">
            <p>HDFS联邦通过多个独立的NameNode来水平扩展命名空间，解决单个NameNode的内存和性能限制。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #e74c3c;">🏛️ 传统HDFS架构问题</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>命名空间限制：</strong>单个NameNode内存限制</li>
                    <li><strong>性能瓶颈：</strong>所有元数据操作集中处理</li>
                    <li><strong>隔离性差：</strong>不同应用共享同一命名空间</li>
                    <li><strong>扩展困难：</strong>无法水平扩展元数据服务</li>
                </ul>

                <h3 style="color: #27ae60;">🔧 联邦架构优势</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>水平扩展：</strong>多个NameNode并行工作</li>
                    <li><strong>命名空间隔离：</strong>不同应用使用独立命名空间</li>
                    <li><strong>性能提升：</strong>分散元数据操作负载</li>
                    <li><strong>故障隔离：</strong>单个NameNode故障不影响其他</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Federation Architecture -->
                    <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">HDFS 联邦架构</text>

                    <!-- NameNode 1 -->
                    <rect x="50" y="60" width="100" height="60" fill="#e74c3c" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="100" y="80" text-anchor="middle" fill="white" font-size="10" font-weight="bold">NameNode1</text>
                    <text x="100" y="95" text-anchor="middle" fill="white" font-size="8">Namespace: /app1</text>
                    <text x="100" y="110" text-anchor="middle" fill="white" font-size="8">Block Pool: BP1</text>

                    <!-- NameNode 2 -->
                    <rect x="200" y="60" width="100" height="60" fill="#3498db" rx="8" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="250" y="80" text-anchor="middle" fill="white" font-size="10" font-weight="bold">NameNode2</text>
                    <text x="250" y="95" text-anchor="middle" fill="white" font-size="8">Namespace: /app2</text>
                    <text x="250" y="110" text-anchor="middle" fill="white" font-size="8">Block Pool: BP2</text>

                    <!-- DataNode Pool -->
                    <rect x="50" y="160" width="250" height="100" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="175" y="150" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">共享DataNode池</text>

                    <!-- DataNodes -->
                    <rect x="70" y="180" width="60" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </rect>
                    <text x="100" y="195" text-anchor="middle" fill="white" font-size="9">DN1</text>
                    <text x="100" y="210" text-anchor="middle" fill="white" font-size="7">BP1,BP2</text>

                    <rect x="150" y="180" width="60" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.7s" fill="freeze"/>
                    </rect>
                    <text x="180" y="195" text-anchor="middle" fill="white" font-size="9">DN2</text>
                    <text x="180" y="210" text-anchor="middle" fill="white" font-size="7">BP1,BP2</text>

                    <rect x="230" y="180" width="60" height="40" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.9s" fill="freeze"/>
                    </rect>
                    <text x="260" y="195" text-anchor="middle" fill="white" font-size="9">DN3</text>
                    <text x="260" y="210" text-anchor="middle" fill="white" font-size="7">BP1,BP2</text>

                    <!-- Connections -->
                    <path d="M100 120 L100 180" stroke="#e74c3c" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </path>
                    <path d="M100 120 L180 180" stroke="#e74c3c" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </path>
                    <path d="M100 120 L260 180" stroke="#e74c3c" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </path>

                    <path d="M250 120 L100 180" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>
                    <path d="M250 120 L180 180" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>
                    <path d="M250 120 L260 180" stroke="#3498db" stroke-width="2" fill="none" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </path>

                    <!-- Block Pool explanation -->
                    <text x="200" y="290" text-anchor="middle" fill="#2c3e50" font-size="11" font-weight="bold" opacity="0">
                        块池 (Block Pool)
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                    <text x="200" y="310" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        每个NameNode管理独立的块池
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                    <text x="200" y="325" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        DataNode存储来自多个块池的数据
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="slide-number">20 / 29</div>
    </div>

    <!-- Slide 21: Performance Optimization -->
    <div class="slide">
        <h2>HDFS 性能优化</h2>

        <div class="highlight">
            <p>通过合理的配置和优化策略，可以显著提升HDFS的读写性能和整体吞吐量。</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div>
                <h3 style="color: #e74c3c;">⚙️ 配置优化</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>块大小调整：</strong>根据文件大小调整块大小</li>
                    <li><strong>副本数量：</strong>平衡可靠性和存储成本</li>
                    <li><strong>内存配置：</strong>合理分配NameNode内存</li>
                    <li><strong>网络配置：</strong>优化网络带宽和延迟</li>
                    <li><strong>磁盘配置：</strong>使用多个磁盘提高I/O</li>
                </ul>

                <div class="code-block" style="font-size: 1em; margin-top: 15px;">
# 关键配置参数
dfs.blocksize=268435456  # 256MB
dfs.replication=3        # 副本数
dfs.namenode.handler.count=64  # 处理线程
dfs.datanode.max.transfer.threads=8192
dfs.client.read.shortcircuit=true
                </div>
            </div>

            <div>
                <h3 style="color: #27ae60;">🚀 性能优化策略</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>短路读取：</strong>本地数据直接读取</li>
                    <li><strong>数据本地性：</strong>计算靠近数据</li>
                    <li><strong>压缩算法：</strong>减少网络传输和存储</li>
                    <li><strong>缓存机制：</strong>热点数据内存缓存</li>
                    <li><strong>负载均衡：</strong>均匀分布数据和负载</li>
                </ul>

                <svg viewBox="0 0 350 200" style="width: 100%; height: 180px; margin-top: 15px;">
                    <!-- Performance metrics -->
                    <rect x="50" y="20" width="80" height="50" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="90" y="40" text-anchor="middle" fill="white" font-size="10" font-weight="bold">吞吐量</text>
                    <text x="90" y="55" text-anchor="middle" fill="white" font-size="8">+200%</text>

                    <rect x="150" y="20" width="80" height="50" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="190" y="40" text-anchor="middle" fill="white" font-size="10" font-weight="bold">延迟</text>
                    <text x="190" y="55" text-anchor="middle" fill="white" font-size="8">-50%</text>

                    <rect x="250" y="20" width="80" height="50" fill="#e74c3c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="290" y="40" text-anchor="middle" fill="white" font-size="10" font-weight="bold">存储效率</text>
                    <text x="290" y="55" text-anchor="middle" fill="white" font-size="8">+30%</text>

                    <!-- Optimization techniques -->
                    <text x="175" y="100" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        优化技术
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </text>

                    <circle cx="90" cy="130" r="25" fill="#f39c12" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </circle>
                    <text x="90" y="135" text-anchor="middle" fill="white" font-size="8" font-weight="bold">短路读取</text>

                    <circle cx="175" cy="130" r="25" fill="#9b59b6" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.2s" fill="freeze"/>
                    </circle>
                    <text x="175" y="135" text-anchor="middle" fill="white" font-size="8" font-weight="bold">数据压缩</text>

                    <circle cx="260" cy="130" r="25" fill="#1abc9c" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.4s" fill="freeze"/>
                    </circle>
                    <text x="260" y="135" text-anchor="middle" fill="white" font-size="8" font-weight="bold">负载均衡</text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>最佳实践：</strong>性能优化需要根据具体的工作负载特征进行调整，监控系统指标并持续优化配置参数。</p>
        </div>

        <div class="slide-number">21 / 29</div>
    </div>

    <!-- Slide 22: HDFS Commands -->
    <div class="slide">
        <h2>HDFS 常用命令</h2>

        <div class="highlight">
            <p>掌握HDFS命令行工具是管理和操作HDFS文件系统的基础技能。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #3498db;">📁 文件操作命令</h3>
                <div class="code-block" style="font-size: 1.1em;">
# 列出目录内容
hdfs dfs -ls /user/data

# 创建目录
hdfs dfs -mkdir /user/data/new

# 上传文件
hdfs dfs -put local.txt /user/data/

# 下载文件
hdfs dfs -get /user/data/file.txt

# 复制文件
hdfs dfs -cp /user/data/src /user/data/dst

# 移动文件
hdfs dfs -mv /user/data/old /user/data/new

# 删除文件
hdfs dfs -rm /user/data/file.txt

# 删除目录
hdfs dfs -rm -r /user/data/dir
                </div>
            </div>

            <div>
                <h3 style="color: #e74c3c;">🔧 管理命令</h3>
                <div class="code-block" style="font-size: 1.1em;">
# 查看文件系统状态
hdfs dfsadmin -report

# 安全模式操作
hdfs dfsadmin -safemode get
hdfs dfsadmin -safemode leave

# 检查文件系统
hdfs fsck /

# 查看文件块信息
hdfs fsck /user/data/file.txt -files -blocks

# 负载均衡
hdfs balancer

# 查看DataNode状态
hdfs dfsadmin -printTopology

# 刷新节点
hdfs dfsadmin -refreshNodes
                </div>

                <h3 style="color: #27ae60;">📊 监控命令</h3>
                <div class="code-block" style="font-size: 1.1em;">
# 查看磁盘使用情况
hdfs dfs -du -h /user/data

# 统计文件数量
hdfs dfs -count /user/data

# 查看文件详细信息
hdfs dfs -stat "%n %o %r %u %g %s" /user/data/file.txt
                </div>
            </div>
        </div>

        <div class="slide-number">22 / 29</div>
    </div>

    <!-- Slide 23: Real-world Use Cases -->
    <div class="slide">
        <h2>HDFS 实际应用案例</h2>

        <div class="highlight">
            <p>HDFS在各个行业和场景中都有广泛的应用，支撑着大数据处理和分析的基础设施。</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div>
                <h3 style="color: #e74c3c;">🏢 企业级应用</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>Yahoo：</strong>最早的大规模HDFS部署，存储PB级数据</li>
                    <li><strong>Facebook：</strong>用户数据和日志分析，数万台服务器</li>
                    <li><strong>Netflix：</strong>视频内容存储和推荐系统数据</li>
                    <li><strong>LinkedIn：</strong>用户行为数据和社交网络分析</li>
                    <li><strong>Twitter：</strong>实时数据流处理和历史数据存储</li>
                </ul>

                <h3 style="color: #3498db;">🏭 行业应用</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>金融：</strong>交易数据、风险分析、反欺诈</li>
                    <li><strong>电信：</strong>通话记录、网络监控、用户行为</li>
                    <li><strong>零售：</strong>销售数据、库存管理、客户分析</li>
                    <li><strong>医疗：</strong>病历数据、基因组学、医学影像</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 400" style="width: 100%; height: 350px;">
                    <!-- Use case diagram -->
                    <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">HDFS 应用场景</text>

                    <!-- Data Lake -->
                    <rect x="50" y="60" width="120" height="60" fill="#3498db" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </rect>
                    <text x="110" y="80" text-anchor="middle" fill="white" font-size="11" font-weight="bold">数据湖</text>
                    <text x="110" y="95" text-anchor="middle" fill="white" font-size="9">多源数据存储</text>
                    <text x="110" y="110" text-anchor="middle" fill="white" font-size="9">结构化/非结构化</text>

                    <!-- Log Analytics -->
                    <rect x="230" y="60" width="120" height="60" fill="#e74c3c" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </rect>
                    <text x="290" y="80" text-anchor="middle" fill="white" font-size="11" font-weight="bold">日志分析</text>
                    <text x="290" y="95" text-anchor="middle" fill="white" font-size="9">系统日志</text>
                    <text x="290" y="110" text-anchor="middle" fill="white" font-size="9">用户行为日志</text>

                    <!-- Backup & Archive -->
                    <rect x="50" y="150" width="120" height="60" fill="#27ae60" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="110" y="170" text-anchor="middle" fill="white" font-size="11" font-weight="bold">备份归档</text>
                    <text x="110" y="185" text-anchor="middle" fill="white" font-size="9">长期数据保存</text>
                    <text x="110" y="200" text-anchor="middle" fill="white" font-size="9">成本效益高</text>

                    <!-- Machine Learning -->
                    <rect x="230" y="150" width="120" height="60" fill="#f39c12" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </rect>
                    <text x="290" y="170" text-anchor="middle" fill="white" font-size="11" font-weight="bold">机器学习</text>
                    <text x="290" y="185" text-anchor="middle" fill="white" font-size="9">训练数据存储</text>
                    <text x="290" y="200" text-anchor="middle" fill="white" font-size="9">模型和特征</text>

                    <!-- ETL Processing -->
                    <rect x="140" y="240" width="120" height="60" fill="#9b59b6" rx="10" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="200" y="260" text-anchor="middle" fill="white" font-size="11" font-weight="bold">ETL处理</text>
                    <text x="200" y="275" text-anchor="middle" fill="white" font-size="9">数据清洗</text>
                    <text x="200" y="290" text-anchor="middle" fill="white" font-size="9">数据转换</text>

                    <!-- Statistics -->
                    <text x="200" y="330" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        全球部署统计
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </text>
                    <text x="200" y="350" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        • 数千家企业使用  • 存储EB级数据  • 支持数万节点集群
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="slide-number">23 / 29</div>
    </div>

    <!-- Slide 24: HDFS vs Other Storage Systems -->
    <div class="slide">
        <h2>HDFS vs 其他存储系统</h2>

        <div class="highlight">
            <p>了解HDFS与其他存储系统的对比，有助于选择合适的存储解决方案。</p>
        </div>

        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; font-size: 1.2em; margin: 20px 0;">
                <thead>
                    <tr style="background: #34495e; color: white;">
                        <th style="padding: 15px; border: 1px solid #ddd;">特性</th>
                        <th style="padding: 15px; border: 1px solid #ddd;">HDFS</th>
                        <th style="padding: 15px; border: 1px solid #ddd;">Amazon S3</th>
                        <th style="padding: 15px; border: 1px solid #ddd;">GFS</th>
                        <th style="padding: 15px; border: 1px solid #ddd;">传统NAS</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: #ecf0f1;">
                        <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">扩展性</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">水平扩展</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">无限扩展</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">水平扩展</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">垂直扩展</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">一致性</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">强一致性</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">最终一致性</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">强一致性</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">强一致性</td>
                    </tr>
                    <tr style="background: #ecf0f1;">
                        <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">访问模式</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">流式读写</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">REST API</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">流式读写</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">随机访问</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">成本</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">中等</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">按使用付费</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">高</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">高</td>
                    </tr>
                    <tr style="background: #ecf0f1;">
                        <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">适用场景</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">大数据处理</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">云存储</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">Google内部</td>
                        <td style="padding: 12px; border: 1px solid #ddd;">企业文件共享</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
            <div>
                <h3 style="color: #27ae60;">✅ HDFS 优势</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li>开源免费，社区活跃</li>
                    <li>与Hadoop生态系统深度集成</li>
                    <li>成熟稳定，经过大规模验证</li>
                    <li>强一致性保证</li>
                    <li>本地化部署，数据可控</li>
                </ul>
            </div>
            <div>
                <h3 style="color: #e74c3c;">⚠️ HDFS 限制</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li>不适合小文件存储</li>
                    <li>不支持随机写入</li>
                    <li>NameNode单点瓶颈</li>
                    <li>需要专业运维团队</li>
                    <li>启动和恢复时间较长</li>
                </ul>
            </div>
        </div>

        <div class="slide-number">24 / 29</div>
    </div>

    <!-- Slide 25: Best Practices -->
    <div class="slide">
        <h2>HDFS 最佳实践</h2>

        <div class="highlight">
            <p>遵循最佳实践可以确保HDFS集群的稳定性、性能和可维护性。</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div>
                <h3 style="color: #3498db;">🏗️ 架构设计</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>合理规划集群规模：</strong>根据数据量和增长预期</li>
                    <li><strong>网络拓扑设计：</strong>考虑机架感知和带宽</li>
                    <li><strong>硬件选型：</strong>平衡性能和成本</li>
                    <li><strong>高可用配置：</strong>部署HA和Federation</li>
                </ul>

                <h3 style="color: #e74c3c;">⚙️ 配置优化</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>内存分配：</strong>NameNode堆内存设置</li>
                    <li><strong>线程池配置：</strong>处理并发请求</li>
                    <li><strong>网络参数：</strong>超时和重试设置</li>
                    <li><strong>压缩算法：</strong>选择合适的压缩方式</li>
                </ul>

                <h3 style="color: #27ae60;">📊 监控告警</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>关键指标监控：</strong>磁盘、内存、网络</li>
                    <li><strong>日志分析：</strong>及时发现异常</li>
                    <li><strong>性能基线：</strong>建立性能基准</li>
                    <li><strong>自动化告警：</strong>故障及时通知</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #f39c12;">🔧 运维管理</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>定期备份：</strong>元数据和重要数据</li>
                    <li><strong>容量规划：</strong>提前扩容避免空间不足</li>
                    <li><strong>版本升级：</strong>制定升级策略和回滚计划</li>
                    <li><strong>安全加固：</strong>认证、授权、加密</li>
                </ul>

                <div class="code-block" style="font-size: 1em; margin-top: 15px;">
# 监控脚本示例
#!/bin/bash
# 检查HDFS健康状态
hdfs dfsadmin -report | grep "Live datanodes"
hdfs dfsadmin -report | grep "Dead datanodes"

# 检查磁盘使用率
df -h | grep hadoop

# 检查NameNode内存使用
jstat -gc $(pgrep -f NameNode) 1s 1
                </div>

                <h3 style="color: #9b59b6;">📋 数据管理</h3>
                <ul style="font-size: 1.3em; line-height: 1.8;">
                    <li><strong>目录结构规划：</strong>合理的命名空间设计</li>
                    <li><strong>数据生命周期：</strong>自动化数据归档和清理</li>
                    <li><strong>权限管理：</strong>最小权限原则</li>
                    <li><strong>数据质量：</strong>定期校验数据完整性</li>
                </ul>
            </div>
        </div>

        <div class="slide-number">25 / 29</div>
    </div>

    <!-- Slide 26: Troubleshooting -->
    <div class="slide">
        <h2>HDFS 故障排查</h2>

        <div class="highlight">
            <p>掌握常见故障的排查方法和解决策略，是HDFS运维的重要技能。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #e74c3c;">🚨 常见故障类型</h3>

                <h4 style="color: #c0392b; margin-top: 20px;">NameNode故障</h4>
                <ul style="font-size: 1.2em; line-height: 1.6;">
                    <li>内存不足导致OOM</li>
                    <li>磁盘空间不足</li>
                    <li>元数据损坏</li>
                    <li>网络分区</li>
                </ul>

                <h4 style="color: #c0392b; margin-top: 15px;">DataNode故障</h4>
                <ul style="font-size: 1.2em; line-height: 1.6;">
                    <li>磁盘故障</li>
                    <li>网络连接问题</li>
                    <li>数据块损坏</li>
                    <li>心跳超时</li>
                </ul>

                <h4 style="color: #c0392b; margin-top: 15px;">性能问题</h4>
                <ul style="font-size: 1.2em; line-height: 1.6;">
                    <li>读写速度慢</li>
                    <li>负载不均衡</li>
                    <li>网络带宽瓶颈</li>
                    <li>小文件过多</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #27ae60;">🔧 排查方法</h3>

                <div class="code-block" style="font-size: 1em;">
# 1. 检查集群状态
hdfs dfsadmin -report
hdfs dfsadmin -printTopology

# 2. 检查文件系统
hdfs fsck / -files -blocks -locations

# 3. 查看日志
tail -f $HADOOP_LOG_DIR/hadoop-*-namenode-*.log
tail -f $HADOOP_LOG_DIR/hadoop-*-datanode-*.log

# 4. 检查进程状态
jps | grep -E "(NameNode|DataNode)"
ps aux | grep java

# 5. 网络连通性测试
telnet namenode-host 9000
telnet datanode-host 50010

# 6. 磁盘空间检查
df -h
du -sh /hadoop/data/*
                </div>

                <h3 style="color: #3498db;">💡 解决策略</h3>
                <ul style="font-size: 1.2em; line-height: 1.6;">
                    <li><strong>预防为主：</strong>监控告警、定期巡检</li>
                    <li><strong>快速定位：</strong>日志分析、指标监控</li>
                    <li><strong>分级处理：</strong>根据影响程度制定策略</li>
                    <li><strong>文档记录：</strong>建立故障知识库</li>
                    <li><strong>演练验证：</strong>定期进行故障演练</li>
                </ul>
            </div>
        </div>

        <div class="slide-number">26 / 29</div>
    </div>

    <!-- Slide 27: Future of HDFS -->
    <div class="slide">
        <h2>HDFS 的未来发展</h2>

        <div class="highlight">
            <p>HDFS持续演进，适应云计算、容器化和新兴技术的发展趋势。</p>
        </div>

        <div class="two-column">
            <div>
                <h3 style="color: #3498db;">🚀 技术发展趋势</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>云原生支持：</strong>Kubernetes集成，容器化部署</li>
                    <li><strong>存储分层：</strong>热温冷数据自动分层</li>
                    <li><strong>智能运维：</strong>AI驱动的自动化运维</li>
                    <li><strong>边缘计算：</strong>支持边缘数据处理</li>
                    <li><strong>多云支持：</strong>跨云平台数据管理</li>
                </ul>

                <h3 style="color: #e74c3c;">⚡ 性能优化</h3>
                <ul style="font-size: 1.4em; line-height: 1.8;">
                    <li><strong>NVMe存储：</strong>利用高速存储设备</li>
                    <li><strong>内存计算：</strong>更多内存缓存机制</li>
                    <li><strong>网络优化：</strong>RDMA和高速网络</li>
                    <li><strong>压缩算法：</strong>更高效的压缩技术</li>
                </ul>
            </div>

            <div>
                <svg viewBox="0 0 400 350" style="width: 100%; height: 300px;">
                    <!-- Future roadmap -->
                    <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">HDFS 发展路线图</text>

                    <!-- Timeline -->
                    <line x1="50" y1="80" x2="350" y2="80" stroke="#34495e" stroke-width="3" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                    </line>

                    <!-- 2024 -->
                    <circle cx="100" cy="80" r="8" fill="#3498db" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
                    </circle>
                    <text x="100" y="105" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">2024</text>
                    <rect x="50" y="120" width="100" height="60" fill="#3498db" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                    </rect>
                    <text x="100" y="140" text-anchor="middle" fill="white" font-size="9" font-weight="bold">云原生</text>
                    <text x="100" y="155" text-anchor="middle" fill="white" font-size="8">Kubernetes</text>
                    <text x="100" y="170" text-anchor="middle" fill="white" font-size="8">容器化部署</text>

                    <!-- 2025 -->
                    <circle cx="200" cy="80" r="8" fill="#27ae60" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="1.5s" fill="freeze"/>
                    </circle>
                    <text x="200" y="105" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">2025</text>
                    <rect x="150" y="120" width="100" height="60" fill="#27ae60" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                    </rect>
                    <text x="200" y="140" text-anchor="middle" fill="white" font-size="9" font-weight="bold">智能运维</text>
                    <text x="200" y="155" text-anchor="middle" fill="white" font-size="8">AI自动化</text>
                    <text x="200" y="170" text-anchor="middle" fill="white" font-size="8">预测性维护</text>

                    <!-- 2026+ -->
                    <circle cx="300" cy="80" r="8" fill="#e74c3c" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    </circle>
                    <text x="300" y="105" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">2026+</text>
                    <rect x="250" y="120" width="100" height="60" fill="#e74c3c" rx="5" opacity="0">
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    </rect>
                    <text x="300" y="140" text-anchor="middle" fill="white" font-size="9" font-weight="bold">边缘计算</text>
                    <text x="300" y="155" text-anchor="middle" fill="white" font-size="8">多云支持</text>
                    <text x="300" y="170" text-anchor="middle" fill="white" font-size="8">量子存储</text>

                    <!-- Challenges -->
                    <text x="200" y="220" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        面临的挑战
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    </text>
                    <text x="200" y="240" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        • 云存储竞争  • 新兴技术冲击  • 运维复杂性
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                    </text>

                    <!-- Opportunities -->
                    <text x="200" y="270" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" opacity="0">
                        发展机遇
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="4.5s" fill="freeze"/>
                    </text>
                    <text x="200" y="290" text-anchor="middle" fill="#2c3e50" font-size="10" opacity="0">
                        • 数据量持续增长  • 开源生态繁荣  • 企业数字化转型
                        <animate attributeName="opacity" values="0;1" dur="1s" begin="5s" fill="freeze"/>
                    </text>
                </svg>
            </div>
        </div>

        <div class="highlight" style="margin-top: 20px;">
            <p><strong>展望未来：</strong>HDFS将继续作为大数据存储的重要基础设施，在新技术的推动下不断演进和完善。</p>
        </div>

        <div class="slide-number">27 / 29</div>
    </div>

    <!-- Slide 28: Summary -->
    <div class="slide">
        <h2>课程总结</h2>

        <div class="center-content">
            <svg class="svg-animation" viewBox="0 0 800 400" style="height: 350px;">
                <!-- Central HDFS -->
                <circle cx="400" cy="200" r="80" fill="#3498db" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="0s" fill="freeze"/>
                </circle>
                <text x="400" y="195" text-anchor="middle" fill="white" font-size="16" font-weight="bold">HDFS</text>
                <text x="400" y="215" text-anchor="middle" fill="white" font-size="12">分布式文件系统</text>

                <!-- Key concepts around -->
                <circle cx="250" cy="120" r="50" fill="#e74c3c" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze"/>
                </circle>
                <text x="250" y="115" text-anchor="middle" fill="white" font-size="11" font-weight="bold">架构设计</text>
                <text x="250" y="130" text-anchor="middle" fill="white" font-size="9">主从模式</text>

                <circle cx="550" cy="120" r="50" fill="#27ae60" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="1.2s" fill="freeze"/>
                </circle>
                <text x="550" y="115" text-anchor="middle" fill="white" font-size="11" font-weight="bold">数据管理</text>
                <text x="550" y="130" text-anchor="middle" fill="white" font-size="9">块存储</text>

                <circle cx="200" cy="280" r="50" fill="#f39c12" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="1.4s" fill="freeze"/>
                </circle>
                <text x="200" y="275" text-anchor="middle" fill="white" font-size="11" font-weight="bold">容错机制</text>
                <text x="200" y="290" text-anchor="middle" fill="white" font-size="9">副本策略</text>

                <circle cx="600" cy="280" r="50" fill="#9b59b6" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="1.6s" fill="freeze"/>
                </circle>
                <text x="600" y="275" text-anchor="middle" fill="white" font-size="11" font-weight="bold">高可用性</text>
                <text x="600" y="290" text-anchor="middle" fill="white" font-size="9">HA机制</text>

                <!-- Connections -->
                <path d="M320 200 L300 140" stroke="#34495e" stroke-width="2" fill="none" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                </path>
                <path d="M480 200 L500 140" stroke="#34495e" stroke-width="2" fill="none" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                </path>
                <path d="M340 250 L250 280" stroke="#34495e" stroke-width="2" fill="none" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                </path>
                <path d="M460 250 L550 280" stroke="#34495e" stroke-width="2" fill="none" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze"/>
                </path>
            </svg>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
                <div>
                    <h3 style="color: #3498db;">🎯 核心要点</h3>
                    <ul style="font-size: 1.4em; line-height: 1.8;">
                        <li>HDFS是为大数据设计的分布式文件系统</li>
                        <li>采用主从架构，NameNode管理元数据</li>
                        <li>数据块复制提供容错能力</li>
                        <li>优化大文件的流式访问</li>
                        <li>与Hadoop生态系统深度集成</li>
                    </ul>
                </div>
                <div>
                    <h3 style="color: #e74c3c;">💡 关键收获</h3>
                    <ul style="font-size: 1.4em; line-height: 1.8;">
                        <li>理解分布式存储的设计原理</li>
                        <li>掌握HDFS的架构和工作机制</li>
                        <li>学会HDFS的部署和运维</li>
                        <li>了解性能优化和故障处理</li>
                        <li>认识HDFS在大数据中的价值</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="slide-number">28 / 29</div>
    </div>

    <!-- Slide 29: Thank You -->
    <div class="slide">
        <div class="center-content">
            <h1 style="font-size: 4em; margin-bottom: 40px;">谢谢大家！</h1>

            <svg class="svg-animation" viewBox="0 0 600 300" style="height: 250px;">
                <!-- Thank you animation -->
                <circle cx="300" cy="150" r="100" fill="none" stroke="#3498db" stroke-width="4" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="0s" fill="freeze"/>
                    <animate attributeName="r" values="100;120;100" dur="3s" begin="1s" repeatCount="indefinite"/>
                </circle>

                <text x="300" y="120" text-anchor="middle" fill="#2c3e50" font-size="24" font-weight="bold" opacity="0">
                    HDFS
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="1s" fill="freeze"/>
                </text>
                <text x="300" y="150" text-anchor="middle" fill="#2c3e50" font-size="18" opacity="0">
                    分布式文件系统
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="1.5s" fill="freeze"/>
                </text>
                <text x="300" y="180" text-anchor="middle" fill="#2c3e50" font-size="16" opacity="0">
                    大数据存储的基石
                    <animate attributeName="opacity" values="0;1" dur="2s" begin="2s" fill="freeze"/>
                </text>

                <!-- Floating elements -->
                <circle cx="150" cy="80" r="15" fill="#e74c3c" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="2.5s" fill="freeze"/>
                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-10; 0,0" dur="2s" begin="3s" repeatCount="indefinite"/>
                </circle>

                <circle cx="450" cy="80" r="15" fill="#27ae60" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="3s" fill="freeze"/>
                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-10; 0,0" dur="2s" begin="3.5s" repeatCount="indefinite"/>
                </circle>

                <circle cx="100" cy="220" r="12" fill="#f39c12" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze"/>
                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-8; 0,0" dur="2.5s" begin="4s" repeatCount="indefinite"/>
                </circle>

                <circle cx="500" cy="220" r="12" fill="#9b59b6" opacity="0">
                    <animate attributeName="opacity" values="0;1" dur="1s" begin="4s" fill="freeze"/>
                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-8; 0,0" dur="2.5s" begin="4.5s" repeatCount="indefinite"/>
                </circle>
            </svg>

            <div style="margin-top: 40px;">
                <p style="font-size: 2em; color: #2c3e50; margin-bottom: 20px;">
                    🎓 学习永无止境，实践出真知
                </p>
                <p style="font-size: 1.6em; color: #7f8c8d;">
                    继续探索大数据技术的精彩世界
                </p>
            </div>
        </div>

        <div class="slide-number">29 / 29</div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页</button>
        <button class="nav-btn" onclick="toggleFullscreen()">全屏</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            updateProgressBar();
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function previousSlide() {
            showSlide(currentSlide - 1);
        }
        
        function updateProgressBar() {
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key === 'f' || e.key === 'F') {
                toggleFullscreen();
            }
        });
        
        // Initialize progress bar
        updateProgressBar();
    </script>
</body>
</html>
